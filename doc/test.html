<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>积分系统后台管理</title>
  <style>
    body { font-family: "微软雅黑", Arial, sans-serif; background: #f5f6fa; margin: 0; }
    .sidebar { width: 200px; background: #2d3a4b; color: #fff; height: 100vh; float: left; }
    .sidebar h2 { text-align: center; padding: 20px 0; margin: 0; font-size: 20px; }
    .sidebar ul { list-style: none; padding: 0; margin: 0; }
    .sidebar ul li { padding: 15px 30px; cursor: pointer; }
    .sidebar ul li:hover, .sidebar ul li.active { background: #1a2233; }
    .main { margin-left: 200px; padding: 30px; }
    h1 { font-size: 24px; margin-bottom: 20px; }
    table { width: 100%; border-collapse: collapse; background: #fff; }
    th, td { padding: 10px; border: 1px solid #eaeaea; text-align: left; }
    th { background: #f0f2f5; }
    .actions button { margin-right: 5px; }
    .search-bar { margin-bottom: 20px; }
    .search-bar input { padding: 5px 10px; width: 200px; }
    .search-bar button { padding: 5px 15px; }
  </style>
</head>
<body>
  <div class="sidebar">
    <h2>积分系统后台</h2>
    <ul>
      <li class="active">用户积分管理</li>
      <li>积分明细</li>
      <li>任务管理</li>
      <li>商品管理</li>
      <li>订单管理</li>
      <li>发货管理</li>
      <li>用户邀请</li>
      <li>收货地址管理</li>
    </ul>
  </div>
  <div class="main">
    <!-- 用户积分管理示例 -->
    <div id="user-points">
      <h1>用户积分管理</h1>
      <div class="search-bar">
        <input type="text" placeholder="输入用户ID/昵称搜索">
        <button>搜索</button>
        <button>导出</button>
      </div>
      <table>
        <thead>
          <tr>
            <th>用户ID</th>
            <th>用户名</th>
            <th>当前积分</th>
            <th>冻结积分</th>
            <th>已过期积分</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>10001</td>
            <td>张三</td>
            <td>1200</td>
            <td>0</td>
            <td>50</td>
            <td class="actions">
              <button>积分明细</button>
              <button>调整积分</button>
            </td>
          </tr>
          <!-- 更多数据 -->
        </tbody>
      </table>
    </div>

    <!-- 任务管理示例 -->
    <div id="task" style="display:none;">
      <h1>任务管理</h1>
      <button>新增任务</button>
      <table>
        <thead>
          <tr>
            <th>任务ID</th>
            <th>任务名称</th>
            <th>类型</th>
            <th>奖励积分</th>
            <th>每日上限</th>
            <th>总上限</th>
            <th>状态</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>1</td>
            <td>每日签到</td>
            <td>日常</td>
            <td>10</td>
            <td>1</td>
            <td>0</td>
            <td>启用</td>
            <td class="actions">
              <button>编辑</button>
              <button>禁用</button>
            </td>
          </tr>
          <!-- 更多数据 -->
        </tbody>
      </table>
    </div>

    <!-- 商品管理示例 -->
    <div id="goods" style="display:none;">
      <h1>积分商品管理</h1>
      <button>新增商品</button>
      <table>
        <thead>
          <tr>
            <th>商品ID</th>
            <th>商品名称</th>
            <th>所需积分</th>
            <th>库存</th>
            <th>每人限兑</th>
            <th>状态</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>101</td>
            <td>京东卡50元</td>
            <td>500</td>
            <td>20</td>
            <td>1</td>
            <td>上架</td>
            <td class="actions">
              <button>编辑</button>
              <button>下架</button>
            </td>
          </tr>
          <!-- 更多数据 -->
        </tbody>
      </table>
    </div>

    <!-- 订单管理示例 -->
    <div id="order" style="display:none;">
      <h1>订单管理</h1>
      <table>
        <thead>
          <tr>
            <th>订单ID</th>
            <th>用户ID</th>
            <th>商品名称</th>
            <th>数量</th>
            <th>消耗积分</th>
            <th>状态</th>
            <th>下单时间</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>5001</td>
            <td>10001</td>
            <td>京东卡50元</td>
            <td>1</td>
            <td>500</td>
            <td>待发货</td>
            <td>2024-05-01 10:00</td>
            <td class="actions">
              <button>发货</button>
              <button>详情</button>
            </td>
          </tr>
          <!-- 更多数据 -->
        </tbody>
      </table>
    </div>

    <!-- 发货管理示例 -->
    <div id="delivery" style="display:none;">
      <h1>发货管理</h1>
      <table>
        <thead>
          <tr>
            <th>发货ID</th>
            <th>订单ID</th>
            <th>用户ID</th>
            <th>收货人</th>
            <th>物流公司</th>
            <th>物流单号</th>
            <th>发货状态</th>
            <th>发货时间</th>
            <th>签收时间</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>8001</td>
            <td>5001</td>
            <td>10001</td>
            <td>张三</td>
            <td>顺丰</td>
            <td>SF123456789</td>
            <td>运输中</td>
            <td>2024-05-02 09:00</td>
            <td></td>
            <td class="actions">
              <button>修改</button>
              <button>详情</button>
            </td>
          </tr>
          <!-- 更多数据 -->
        </tbody>
      </table>
    </div>

    <!-- 用户邀请管理示例 -->
    <div id="invite" style="display:none;">
      <h1>用户邀请管理</h1>
      <table>
        <thead>
          <tr>
            <th>邀请ID</th>
            <th>邀请人ID</th>
            <th>被邀请人ID</th>
            <th>邀请时间</th>
            <th>奖励状态</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>9001</td>
            <td>10001</td>
            <td>10002</td>
            <td>2024-05-01 12:00</td>
            <td>已发放</td>
            <td class="actions">
              <button>详情</button>
            </td>
          </tr>
          <!-- 更多数据 -->
        </tbody>
      </table>
    </div>

    <!-- 收货地址管理示例 -->
    <div id="address" style="display:none;">
      <h1>收货地址管理</h1>
      <table>
        <thead>
          <tr>
            <th>地址ID</th>
            <th>用户ID</th>
            <th>收件人</th>
            <th>手机号</th>
            <th>省</th>
            <th>市</th>
            <th>区/县</th>
            <th>详细地址</th>
            <th>是否默认</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>3001</td>
            <td>10001</td>
            <td>张三</td>
            <td>13800000000</td>
            <td>广东省</td>
            <td>深圳市</td>
            <td>南山区</td>
            <td>科技园南区1号</td>
            <td>是</td>
            <td class="actions">
              <button>编辑</button>
              <button>删除</button>
            </td>
          </tr>
          <!-- 更多数据 -->
        </tbody>
      </table>
    </div>
  </div>
  <!-- 可根据需要添加简单的JS切换菜单内容 -->
</body>
</html>