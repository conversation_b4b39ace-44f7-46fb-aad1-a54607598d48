/*
 Navicat Premium Dump SQL

 Source Server         : prod
 Source Server Type    : MySQL
 Source Server Version : 80036 (8.0.36)
 Source Host           : **************:3306
 Source Schema         : bcy_project

 Target Server Type    : MySQL
 Target Server Version : 80036 (8.0.36)
 File Encoding         : 65001

 Date: 21/01/2025 11:46:38
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for bcy_customer
-- ----------------------------
DROP TABLE IF EXISTS `bcy_customer`;
CREATE TABLE `bcy_customer`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '客户ID',
  `name` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '客户名称',
  `phone` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '联系电话',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '备注',
  `demand` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '客户需求',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '是否联系（0否1是）',
  `url` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '流量路径（第三方会添加相关参数）',
  `ads_id` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '广告ID',
  `site_id` bigint NOT NULL DEFAULT 0 COMMENT '站点ID',
  `platform_type` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '来源平台',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户Id',
  `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除（0否1是）',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `create_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人',
  `update_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '客户记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of bcy_customer
-- ----------------------------

-- ----------------------------
-- Table structure for bcy_pet_category
-- ----------------------------
DROP TABLE IF EXISTS `bcy_pet_category`;
CREATE TABLE `bcy_pet_category`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `parent_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '父级ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '分类名称',
  `images` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT 'banner图',
  `ico` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '分类图标',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '是否显示（0是1否）',
  `keyword` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '关键字',
  `sort` int NOT NULL DEFAULT 0 COMMENT '自定义顺序',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户Id',
  `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除（0否1是）',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `create_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人',
  `update_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_parent_id`(`parent_id` ASC) USING BTREE,
  INDEX `idx_name`(`name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '宠物分类' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of bcy_pet_category
-- ----------------------------
INSERT INTO `bcy_pet_category` VALUES (1, 0, '猫猫', '', '', 0, '', 1, 1, 0, 1, 'admin', 'admin', '2024-11-28 15:27:44', '2024-11-28 17:23:31');
INSERT INTO `bcy_pet_category` VALUES (2, 1, '缅因猫', '', 'https://api.bcycloud.com/sys/upload/bcy/20241128175742/缅因猫.png', 0, '缅因猫是由美国本地猫和安哥拉猫人为杂交培育成的，在猫类中亦属大体型的品种，长像与西伯利亚森林猫相似，体格强壮，被毛厚密。缅因猫能发出像小鸟般唧唧的轻叫声，非常动听，是其非常特别的地方。缅因猫性情温顺，勇敢机灵，喜欢独处，但能与人很好相处，是良好的宠物。', 0, 1, 0, 4, 'admin', 'admin', '2024-11-28 16:20:59', '2024-11-28 18:18:54');
INSERT INTO `bcy_pet_category` VALUES (3, 0, '狗狗', '', '', 0, '', 0, 1, 0, 0, 'admin', 'admin', '2024-11-28 17:23:25', '2024-11-28 17:23:25');
INSERT INTO `bcy_pet_category` VALUES (4, 1, '布偶猫', '', 'https://api.bcycloud.com/sys/upload/bcy/20241203103113/259ce0704d694d30ac1e00a7109d5962_2.png', 0, '　布偶猫，又称“布拉多尔”，是一种杂交品种宠物猫，也是现存体型最大、体重最重的猫之一。', 0, 1, 0, 1, 'admin', 'admin', '2024-11-29 11:47:17', '2024-12-03 10:31:15');
INSERT INTO `bcy_pet_category` VALUES (5, 3, '贵宾犬', '', 'https://api.bcycloud.com/sys/upload/bcy/20241203102846/3a48ccfb38e146898c00ad0ac93ef57d_1.png', 0, '贵宾犬，别名：贵妇犬、卷毛狗，泰迪犬是贵宾犬的 “泰迪装 ”，因为这个狗狗造型受到了许多人的喜爱，成为了一种美容潮流，所以将美容成“泰迪装 ”的贵宾犬称之为“泰迪犬”。它智商很高，在犬里排名第二，寿命一般为10-15年。', 1, 1, 0, 2, 'admin', 'admin', '2024-11-29 15:16:05', '2024-12-03 10:28:49');
INSERT INTO `bcy_pet_category` VALUES (6, 0, '水族', '', '', 0, '', 3, 1, 0, 0, 'admin', 'admin', '2024-12-03 10:33:23', '2024-12-03 10:33:23');
INSERT INTO `bcy_pet_category` VALUES (7, 6, '孔雀鱼', '', 'https://api.bcycloud.com/sys/upload/bcy/20241203103539/e5ee411551844f7aa3787f1d80bc6fbd_3.png', 0, '孔雀鱼，又称孔雀花鳉、虹鳉、我国东北部分地区称之为“凤尾鱼”、“火炬”，是鳉亚目，花鳉科，花鳉属的一种热带鱼，饲养难度较低。原产于南美洲的委内瑞拉，巴巴多斯，巴西北部与圭亚那，作为观赏鱼引入新加坡、中国、日本、韩国、美国以及众多欧洲国家(比如俄罗斯、德国等)。', 1, 1, 0, 3, 'admin', 'admin', '2024-12-03 10:35:08', '2024-12-03 10:35:42');
INSERT INTO `bcy_pet_category` VALUES (8, 6, '泰狮', '', 'https://api.bcycloud.com/sys/upload/bcy/20241203110532/8ece14b6c6d4e87d48047ffba3652ec9.jpeg', 0, '泰狮是一种侧视观赏型的冷水观赏鱼，特别适合在玻璃缸中饲养。这一品种是由中国的狮子头金鱼培育而来，体色鲜艳，姿态优雅美丽，因此具有很高的观赏性。不仅如此，泰狮还拥有丰富多彩的颜色选择和各具特色的形态特点。由于其出色的观赏性和易于饲养的特性，泰狮被认为是生命力极强的冷水观赏鱼之一。', 3, 1, 0, 0, 'admin', 'admin', '2024-12-03 11:05:36', '2024-12-03 11:05:36');

-- ----------------------------
-- Table structure for bcy_pet_category_detail
-- ----------------------------
DROP TABLE IF EXISTS `bcy_pet_category_detail`;
CREATE TABLE `bcy_pet_category_detail`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '类型介绍ID',
  `category_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '分类ID',
  `tags` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '标签',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '详情',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户Id',
  `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除（0否1是）',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `create_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人',
  `update_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category_id`(`category_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '类型介绍表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of bcy_pet_category_detail
-- ----------------------------
INSERT INTO `bcy_pet_category_detail` VALUES (1, 2, '测试', '<p>测试</p>', 1, 0, 0, 'admin', 'admin', '2024-11-29 11:43:08', '2024-11-29 11:43:08');
INSERT INTO `bcy_pet_category_detail` VALUES (2, 4, '测试', '<p><img src=\"https://api.bcycloud.com/sys/upload/bcy/20241129144804/缅因猫.png\" alt=\"\" data-href=\"\" style=\"width: 290.00px;height: 290.00px;\"/></p>', 1, 0, 2, 'admin', 'admin', '2024-11-29 11:47:28', '2024-11-29 14:53:17');
INSERT INTO `bcy_pet_category_detail` VALUES (3, 4, '习性', '<p>参数123</p>', 1, 0, 0, 'admin', 'admin', '2024-11-29 11:49:11', '2024-11-29 11:49:11');

-- ----------------------------
-- Table structure for bcy_pet_content
-- ----------------------------
DROP TABLE IF EXISTS `bcy_pet_content`;
CREATE TABLE `bcy_pet_content`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '文章ID',
  `category_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '分类ID',
  `title` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '文章标题',
  `sub_title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '文章副标题',
  `short_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '简介',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '文章缩略图',
  `source` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '文章来源',
  `author` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '博昌云科技' COMMENT '文章作者',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '是否显示0显示 1不显示',
  `type` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '文章类型',
  `push_time` datetime NULL DEFAULT NULL COMMENT '发布时间',
  `sort` int NOT NULL DEFAULT 0 COMMENT '自定义顺序',
  `tags` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '文章标签',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户Id',
  `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除（0否1是）',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `create_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人',
  `update_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category_id`(`category_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '文章' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of bcy_pet_content
-- ----------------------------
INSERT INTO `bcy_pet_content` VALUES (1, 1, '测试', '测试', '1234', '', '', '博昌云科技', 0, 'science', '2024-12-02 17:52:56', 0, '测试', 1, 0, 8, 'admin', 'admin', '2024-12-02 17:17:21', '2024-12-02 17:52:56');
INSERT INTO `bcy_pet_content` VALUES (2, 1, '测试1', '测试', '1234231', '', '', '博昌云科技', 0, 'look_pet', NULL, 0, '123', 1, 1, 5, 'admin', 'admin', '2024-12-02 17:40:38', '2024-12-02 17:43:16');

-- ----------------------------
-- Table structure for bcy_pet_content_detail
-- ----------------------------
DROP TABLE IF EXISTS `bcy_pet_content_detail`;
CREATE TABLE `bcy_pet_content_detail`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '文章详情ID',
  `content_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '文章ID',
  `details` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '文章内容',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户Id',
  `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除（0否1是）',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `create_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人',
  `update_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_content_id`(`content_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '文章详情' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of bcy_pet_content_detail
-- ----------------------------
INSERT INTO `bcy_pet_content_detail` VALUES (1, 1, '<p>擦完萨维</p>', 1, 0, 2, 'admin', 'admin', '2024-12-02 17:17:21', '2024-12-02 17:37:31');
INSERT INTO `bcy_pet_content_detail` VALUES (2, 2, '<p>暗室逢灯123</p>', 1, 1, 5, 'admin', 'admin', '2024-12-02 17:40:38', '2024-12-02 17:42:00');

-- ----------------------------
-- Table structure for bcy_pet_content_user
-- ----------------------------
DROP TABLE IF EXISTS `bcy_pet_content_user`;
CREATE TABLE `bcy_pet_content_user`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `content_id` bigint NOT NULL COMMENT '文章Id',
  `user_id` bigint NOT NULL COMMENT '用户Id',
  `type` tinyint NOT NULL COMMENT '类型(0点赞1收藏)',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户Id',
  `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除（0否1是）',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `create_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人',
  `update_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '文章用户关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of bcy_pet_content_user
-- ----------------------------

-- ----------------------------
-- Table structure for bcy_pet_info
-- ----------------------------
DROP TABLE IF EXISTS `bcy_pet_info`;
CREATE TABLE `bcy_pet_info`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '唯一标识码',
  `user_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户ID',
  `category_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '分类ID',
  `name` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '宠物名称',
  `short_description` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '简介',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '图片',
  `sex` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '性别',
  `birthday` datetime NULL DEFAULT NULL COMMENT '出生日期',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否丢失（0否1是）',
  `is_default` int NOT NULL DEFAULT 0 COMMENT '是否默认(0是1否)',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户Id',
  `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除（0否1是）',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `create_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人',
  `update_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category_id`(`category_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户宠物信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of bcy_pet_info
-- ----------------------------

-- ----------------------------
-- Table structure for bcy_pet_seek_info
-- ----------------------------
DROP TABLE IF EXISTS `bcy_pet_seek_info`;
CREATE TABLE `bcy_pet_seek_info`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户ID',
  `pet_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '宠物ID',
  `category_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '分类ID',
  `name` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '宠物名称',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '寻宠介绍',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '图片',
  `status` int NOT NULL DEFAULT 0 COMMENT '状态(0待审核1驳回2通过)',
  `phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '联系电话',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '联系地址',
  `msg` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '驳回原因',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户Id',
  `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除（0否1是）',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `create_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人',
  `update_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_pet_id`(`pet_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '寻宠信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of bcy_pet_seek_info
-- ----------------------------

-- ----------------------------
-- Table structure for bcy_site_column_category
-- ----------------------------
DROP TABLE IF EXISTS `bcy_site_column_category`;
CREATE TABLE `bcy_site_column_category`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '栏目ID',
  `app_id` bigint NOT NULL DEFAULT 0 COMMENT '站点ID',
  `parent_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '父级ID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '标题',
  `sub_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '副标题',
  `banner` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT 'banner图',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '是否显示（0是1否）',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '栏目管理描述',
  `keyword` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '栏目管理关键字',
  `url` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '页面链接',
  `sort` int NOT NULL DEFAULT 0 COMMENT '自定义顺序',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户Id',
  `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除（0否1是）',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `create_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人',
  `update_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_parent_id`(`parent_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '网站栏目分类表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of bcy_site_column_category
-- ----------------------------

-- ----------------------------
-- Table structure for bcy_site_config
-- ----------------------------
DROP TABLE IF EXISTS `bcy_site_config`;
CREATE TABLE `bcy_site_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '站点id',
  `name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '站点名称',
  `url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '站点域名，多个回车换行显示',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT 'ico图标',
  `logo` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '网站logo',
  `keyword` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '站点关键字',
  `copyright` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '站点版权信息',
  `link_phone` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '联系电话',
  `link_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '联系地址',
  `lat_lng` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '地图坐标',
  `qr_code` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '二维码',
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '邮箱地址',
  `beian` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '备案号',
  `beian_net` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '网安备案号',
  `model` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'default' COMMENT '站点模板',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '描述',
  `status` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '0运行中 1已停止  ',
  `static_dir` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '网站生成的目录',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户Id',
  `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除（0否1是）',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `create_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人',
  `update_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '网站配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of bcy_site_config
-- ----------------------------

-- ----------------------------
-- Table structure for bcy_site_content
-- ----------------------------
DROP TABLE IF EXISTS `bcy_site_content`;
CREATE TABLE `bcy_site_content`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `category_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '所属栏目ID',
  `app_id` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '站点ID',
  `details` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '文章内容',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '文章标题',
  `sub_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '文章副标题',
  `keyword` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '关键字',
  `description` varchar(400) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '描述',
  `images` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '文章缩略图',
  `source` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '文章来源',
  `author` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '博昌云科技' COMMENT '文章作者',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '是否显示(0显示 1不显示)',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '文章类型',
  `push_datetime` datetime NULL DEFAULT NULL COMMENT '发布时间',
  `sort` int NOT NULL DEFAULT 0 COMMENT '自定义顺序',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户Id',
  `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除（0否1是）',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `create_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人',
  `update_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category_id`(`category_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '网站文章表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of bcy_site_content
-- ----------------------------

-- ----------------------------
-- Table structure for bcy_web_config
-- ----------------------------
DROP TABLE IF EXISTS `bcy_web_config`;
CREATE TABLE `bcy_web_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '页面配置ID',
  `site_name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '站点名称',
  `icon` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT 'ico图标',
  `avatar` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '头像',
  `head_image` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '上部分图片',
  `bottom_image` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '下部分图片',
  `site_copyright` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '站点版权信息',
  `site_link_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '通知人',
  `platform_type` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '平台类型',
  `button_title` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '立即咨询' COMMENT '按钮描述',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '悬浮窗标题',
  `sub_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '悬浮窗副标题',
  `link_url` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '客服链接',
  `wx_template_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '微信通知模板',
  `notice_msg` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '通知内容',
  `notice_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '通知类型',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '状态（0启用1禁用）',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户Id',
  `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除（0否1是）',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `create_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人',
  `update_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '留资配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of bcy_web_config
-- ----------------------------
INSERT INTO `bcy_web_config` VALUES (1, '博昌云留资获客', 'https://api.bcycloud.com/sys/upload/bcy/20240926203336/博昌云logo.ico', 'https://api.bcycloud.com/sys/upload/bcy/20240926203341/博昌云logo.png', 'https://api.bcycloud.com/sys/upload/bcy/20241029223713/WechatIMG1129.jpg', 'https://api.bcycloud.com/sys/upload/bcy/20241027002221/WechatIMG1036.jpg', ' Copyright © 2023 - 2024 博昌云科技 All Rights Reserved.', '<EMAIL>', 'web', '立即咨询', '欢迎咨询', '专业  创新  品质  诚信', '15711593316', '', '已提交号码，请及时回访！', '4', 0, 1, 0, 3, 'admin', 'admin', '2024-11-21 23:09:08', '2024-11-27 14:11:35');

-- ----------------------------
-- Table structure for sys_api_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_api_log`;
CREATE TABLE `sys_api_log`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'API日志Id',
  `operator_type` tinyint NOT NULL DEFAULT 1 COMMENT '操作类别（0:其它,1:后台用户,2:手机端用户）',
  `operator_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '操作名称',
  `model` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '模块',
  `url` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '请求地址',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '操作状态(0:成功,1:失败)',
  `method` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '请求方法',
  `time` bigint NOT NULL COMMENT '执行时长(毫秒)',
  `ip` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT 'IP地址',
  `ip_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT 'IP所在地',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户Id',
  `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除（0否1是）',
  `create_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人',
  `update_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE,
  INDEX `idx_operator_name`(`operator_name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'API日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_api_log
-- ----------------------------
INSERT INTO `sys_api_log` VALUES (1, 1, '领取服务', 'bcy-admin', 'https://analytics.oceanengine.com/api/v2/conversion', 0, 'POST', 1732631928678, '183.253.18.27', '中国 福建 厦门 移动', 0, 0, 0, 'BCY', 'BCY', '2024-11-26 22:38:49', '2024-11-26 22:38:49');

-- ----------------------------
-- Table structure for sys_api_log_item
-- ----------------------------
DROP TABLE IF EXISTS `sys_api_log_item`;
CREATE TABLE `sys_api_log_item`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'API日志详情Id',
  `log_id` bigint NOT NULL DEFAULT 0 COMMENT 'API日志Id',
  `params` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '请求参数',
  `result` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '请求结果',
  `error_msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '错误信息',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户Id',
  `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除（0否1是）',
  `create_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人',
  `update_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_log_id`(`log_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'API日志详情表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_api_log_item
-- ----------------------------
INSERT INTO `sys_api_log_item` VALUES (1, 1, '{\"context\":{\"ad\":{\"callback\":\"B.eBa7IEwdefDUhKkmgOvZ5wseIsN6ZizREf2xc4rbpiorM3lo1gcgAdcJizyMdWTFe4liC4H90tDhE8oMTvclBHe2lNGfZSmuDxuJ1Ut9e0MMDYeWZSsMblrAHhQrQb5WvG\"}},\"event_type\":\"form\",\"timestamp\":1732631928453}', '{\"code\":0,\"message\":\"成功\"}', '', 0, 0, 0, 'BCY', 'BCY', '2024-11-26 22:38:49', '2024-11-26 22:38:49');

-- ----------------------------
-- Table structure for sys_config
-- ----------------------------
DROP TABLE IF EXISTS `sys_config`;
CREATE TABLE `sys_config`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '配置id',
  `name` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '配置名称',
  `params_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '配置标识符',
  `params_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '配置',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '是否启用（0是1否）',
  `can_delete` tinyint NOT NULL DEFAULT 1 COMMENT '是否可删除（0否1是）',
  `remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '备注',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户Id',
  `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除（0否1是）',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
  `update_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '更新人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统配置管理表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_config
-- ----------------------------
INSERT INTO `sys_config` VALUES (1, '登录页展示', 'SYS_SETTING', '{\n	\"title\": \"博昌云科技\",\n	\"version\": \"多租户\",\n	\"isTenant\": 1,\n	\"notice\": \"Copyright © 2023 - 2024 博昌云科技 All Rights Reserved.\",\n	\"beiAnNumber\": \"\",\n	\"number\": \"\",\n	\"websocket\": \"wss://api.bcycloud.com/sys/websocket\",\n	\"isCode\": 1,\n}', 0, 1, '', 1, 0, 8, '2024-11-08 23:58:59', '2024-11-22 10:43:45', 'admin', 'admin');
INSERT INTO `sys_config` VALUES (2, '页面地址', 'WEB_CONFIG_URL', 'https://web.bcycloud.com?platform=', 0, 1, '', 1, 0, 0, '2024-11-09 00:07:18', '2024-11-09 00:07:18', 'admin', 'admin');
INSERT INTO `sys_config` VALUES (3, '短信验证配置', 'VERIFICATION_CODE_SMS', '{\n\"signName\":\"厦门博昌云科技\",\n\"templateCode\":\"SMS_469060834\",\n\"sdkAppId\":\"\"\n}', 0, 1, '', 1, 0, 3, '2024-10-11 17:34:29', '2024-10-26 10:01:25', 'admin', 'admin');
INSERT INTO `sys_config` VALUES (4, '短信通知配置', 'NOTICE_CODE_SMS', ' {\n\"signName\":\"厦门博昌云科技\",\n\"templateCode\":\"SMS_469105753\",\n\"sdkAppId\":\"\"\n}', 0, 1, '', 1, 0, 3, '2024-10-12 00:00:26', '2024-10-26 10:01:42', 'admin', 'admin');

-- ----------------------------
-- Table structure for sys_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_dept`;
CREATE TABLE `sys_dept`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '部门Id',
  `parent_id` bigint NOT NULL DEFAULT 0 COMMENT '上级部门',
  `name` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '部门名称',
  `remark` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '部门备注',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户Id',
  `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除（0否1是）',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
  `update_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '更新人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '部门表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_dept
-- ----------------------------
INSERT INTO `sys_dept` VALUES (1, 0, '总经办', '默认顶级部门', 0, 0, 0, '2024-11-07 21:35:14', '2024-11-07 21:35:14', 'admin', 'admin');
INSERT INTO `sys_dept` VALUES (2, 0, '总经办', '默认顶级部门', 1, 0, 0, '2024-11-07 21:35:16', '2024-11-07 21:35:16', 'admin', 'admin');
INSERT INTO `sys_dept` VALUES (3, 2, '技术部', '技术服务', 1, 0, 1, '2024-11-07 22:03:37', '2024-11-07 22:04:27', 'admin', 'admin');
INSERT INTO `sys_dept` VALUES (4, 0, '总经办', '默认顶级部门', 2, 0, 0, '2024-11-14 21:28:38', '2024-11-14 21:28:38', 'admin', 'admin');

-- ----------------------------
-- Table structure for sys_dept_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_dept_user`;
CREATE TABLE `sys_dept_user`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `dept_id` bigint NOT NULL DEFAULT 0 COMMENT '部门Id',
  `user_id` bigint NOT NULL DEFAULT 0 COMMENT '用户Id',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户Id',
  `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除（0否1是）',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
  `update_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '更新人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '部门用户关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_dept_user
-- ----------------------------
INSERT INTO `sys_dept_user` VALUES (1, 2, 2, 1, 1, 0, '2024-11-10 00:22:11', '2024-11-10 00:24:39', 'admin', 'admin');
INSERT INTO `sys_dept_user` VALUES (3, 2, 2, 1, 1, 0, '2024-11-10 00:24:40', '2024-11-10 01:24:46', 'admin', 'admin');
INSERT INTO `sys_dept_user` VALUES (4, 2, 3, 1, 1, 0, '2024-11-10 00:38:57', '2024-11-10 01:24:41', 'admin', 'admin');
INSERT INTO `sys_dept_user` VALUES (5, 2, 3, 1, 1, 0, '2024-11-10 01:24:41', '2024-11-14 21:28:52', 'admin', 'admin');
INSERT INTO `sys_dept_user` VALUES (6, 2, 2, 1, 0, 0, '2024-11-10 01:24:46', '2024-11-10 01:24:46', 'admin', 'admin');
INSERT INTO `sys_dept_user` VALUES (7, 2, 3, 1, 0, 0, '2024-11-14 21:28:53', '2024-11-14 21:28:53', 'admin', 'admin');

-- ----------------------------
-- Table structure for sys_diction
-- ----------------------------
DROP TABLE IF EXISTS `sys_diction`;
CREATE TABLE `sys_diction`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '字典Id',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '名称',
  `data_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '字段名',
  `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除（0否1是）',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户Id',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人',
  `update_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uniq_data_code`(`data_code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '字典表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_diction
-- ----------------------------
INSERT INTO `sys_diction` VALUES (1, '性别', 'sex', 0, 2, 0, '2024-08-04 23:14:17', '2024-08-04 23:14:17', 'bcy', 'bcy');
INSERT INTO `sys_diction` VALUES (2, '消息类型', 'msg_type', 0, 1, 0, '2024-08-06 23:16:30', '2024-08-06 23:16:30', 'bcy', 'bcy');
INSERT INTO `sys_diction` VALUES (3, '服务', 'module', 1, 1, 4, '2024-08-31 10:03:05', '2024-11-08 23:27:46', 'admin', 'admin');
INSERT INTO `sys_diction` VALUES (4, '平台类型', 'platform_type', 0, 1, 3, '2024-09-25 21:57:17', '2024-10-17 16:04:15', 'admin', 'admin');
INSERT INTO `sys_diction` VALUES (5, '通知类型', 'notice_type', 0, 1, 1, '2024-09-25 22:34:53', '2024-10-17 16:04:05', 'admin', 'admin');
INSERT INTO `sys_diction` VALUES (6, '宠物文章类型', 'pet_content_type', 0, 1, 0, '2024-12-02 17:33:29', '2024-12-02 17:33:29', 'admin', 'admin');

-- ----------------------------
-- Table structure for sys_diction_item
-- ----------------------------
DROP TABLE IF EXISTS `sys_diction_item`;
CREATE TABLE `sys_diction_item`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '字典详情id',
  `diction_id` bigint NOT NULL DEFAULT 0 COMMENT '字典id',
  `data_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '字段名',
  `name` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '名称',
  `value` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '值',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户Id',
  `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除（0否1是）',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `create_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
  `update_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '更新人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_data_code`(`data_code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 46 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '字典详情表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_diction_item
-- ----------------------------
INSERT INTO `sys_diction_item` VALUES (1, 1, 'sex', '未知', '0', 1, 2, 0, 0, 'bcy', 'bcy', '2024-08-04 23:14:17', '2024-10-17 16:25:14');
INSERT INTO `sys_diction_item` VALUES (2, 1, 'sex', '男', '1', 2, 2, 0, 0, 'bcy', 'bcy', '2024-08-04 23:14:17', '2024-10-17 16:25:14');
INSERT INTO `sys_diction_item` VALUES (3, 1, 'sex', '女', '2', 3, 2, 0, 0, 'bcy', 'bcy', '2024-08-04 23:14:17', '2024-10-17 16:25:14');
INSERT INTO `sys_diction_item` VALUES (4, 2, 'msg_type', '系统通知', '1', 0, 1, 0, 1, 'bcy', 'admin', '2024-08-06 23:16:30', '2024-11-28 18:05:36');
INSERT INTO `sys_diction_item` VALUES (5, 2, 'msg_type', '系统升级', '2', 2, 1, 0, 0, 'bcy', 'bcy', '2024-08-06 23:16:30', '2024-10-17 16:25:15');
INSERT INTO `sys_diction_item` VALUES (6, 2, 'msg_type', '维护公告', '3', 3, 1, 0, 0, 'bcy', 'bcy', '2024-08-06 23:16:30', '2024-10-17 16:25:15');
INSERT INTO `sys_diction_item` VALUES (9, 3, 'module', '后台系统', 'bcy-sys', 1, 1, 1, 0, 'admin', 'admin', '2024-09-22 13:16:14', '2024-10-17 16:25:15');
INSERT INTO `sys_diction_item` VALUES (10, 3, 'module', 'CMS系统', 'bcy-cms', 2, 1, 1, 0, 'admin', 'admin', '2024-09-22 13:16:14', '2024-10-17 16:25:15');
INSERT INTO `sys_diction_item` VALUES (15, 5, 'notice_type', '短信', '1', 1, 1, 1, 0, 'admin', 'admin', '2024-09-25 22:34:53', '2024-10-17 16:25:15');
INSERT INTO `sys_diction_item` VALUES (16, 5, 'notice_type', '微信', '2', 2, 1, 1, 0, 'admin', 'admin', '2024-09-25 22:34:53', '2024-10-17 16:25:15');
INSERT INTO `sys_diction_item` VALUES (17, 5, 'notice_type', '邮件', '3', 3, 1, 1, 0, 'admin', 'admin', '2024-09-25 22:34:53', '2024-10-17 16:25:15');
INSERT INTO `sys_diction_item` VALUES (18, 4, 'platform_type', '抖音', 'douyin', 1, 1, 1, 0, 'admin', 'admin', '2024-09-25 23:18:28', '2024-10-17 16:25:16');
INSERT INTO `sys_diction_item` VALUES (19, 4, 'platform_type', 'B站', 'Bzhan', 2, 1, 1, 0, 'admin', 'admin', '2024-09-25 23:18:28', '2024-10-17 16:25:16');
INSERT INTO `sys_diction_item` VALUES (20, 4, 'platform_type', 'H5', 'web', 0, 1, 1, 0, 'admin', 'admin', '2024-09-25 23:18:28', '2024-10-17 16:25:16');
INSERT INTO `sys_diction_item` VALUES (21, 3, 'module', '后台系统', 'bcy-sys', 1, 1, 1, 0, 'admin', 'admin', '2024-10-17 15:57:51', '2024-10-17 16:25:16');
INSERT INTO `sys_diction_item` VALUES (22, 3, 'module', 'CMS系统', 'bcy-cms', 2, 1, 1, 0, 'admin', 'admin', '2024-10-17 15:57:51', '2024-10-17 16:25:16');
INSERT INTO `sys_diction_item` VALUES (23, 3, 'module', '通用', 'bcy-api', 0, 1, 1, 0, 'admin', 'admin', '2024-10-17 15:57:51', '2024-10-17 16:25:16');
INSERT INTO `sys_diction_item` VALUES (24, 5, 'notice_type', '短信', '1', 1, 1, 0, 0, 'admin', 'admin', '2024-10-17 16:04:05', '2024-10-17 16:04:05');
INSERT INTO `sys_diction_item` VALUES (25, 5, 'notice_type', '微信', '2', 2, 1, 0, 0, 'admin', 'admin', '2024-10-17 16:04:05', '2024-10-17 16:04:05');
INSERT INTO `sys_diction_item` VALUES (26, 5, 'notice_type', '邮件', '4', 3, 1, 0, 1, 'admin', 'admin', '2024-10-17 16:04:05', '2024-11-23 21:26:30');
INSERT INTO `sys_diction_item` VALUES (27, 4, 'platform_type', 'H5页面', 'web', 0, 1, 0, 1, 'admin', 'admin', '2024-10-17 16:04:15', '2024-11-27 11:36:55');
INSERT INTO `sys_diction_item` VALUES (28, 4, 'platform_type', '抖音', 'douyin', 1, 1, 0, 0, 'admin', 'admin', '2024-10-17 16:04:15', '2024-10-17 16:25:17');
INSERT INTO `sys_diction_item` VALUES (29, 4, 'platform_type', 'B站', 'Bzhan', 2, 1, 0, 0, 'admin', 'admin', '2024-10-17 16:04:15', '2024-10-17 16:25:17');
INSERT INTO `sys_diction_item` VALUES (30, 3, 'module', '通用服务', 'bcy-api', 0, 1, 1, 1, 'admin', 'admin', '2024-10-17 16:04:30', '2024-11-08 23:27:31');
INSERT INTO `sys_diction_item` VALUES (31, 3, 'module', '后台系统', 'bcy-sys', 3, 1, 1, 1, 'admin', 'admin', '2024-10-17 16:04:30', '2024-11-08 23:27:37');
INSERT INTO `sys_diction_item` VALUES (32, 3, 'module', 'CMS系统', 'bcy-cms', 2, 1, 1, 0, 'admin', 'admin', '2024-10-17 16:04:30', '2024-11-08 23:27:24');
INSERT INTO `sys_diction_item` VALUES (33, 1, 'sex', '性别', '6', 0, 2, 1, 0, 'bcy', 'admin', '2024-08-04 23:14:17', '2024-11-08 23:35:55');
INSERT INTO `sys_diction_item` VALUES (34, 1, 'sex', '性别', '6', 0, 2, 1, 0, 'bcy', 'admin', '2024-08-04 23:14:17', '2024-11-08 23:36:00');
INSERT INTO `sys_diction_item` VALUES (35, 1, 'sex', '测试1', '6', 1, 2, 1, 3, 'bcy', 'admin', '2024-08-04 23:14:17', '2024-11-08 23:42:07');
INSERT INTO `sys_diction_item` VALUES (36, 1, 'sex', '性别1', '123', 1, 2, 1, 1, 'bcy', 'admin', '2024-08-04 23:14:17', '2024-11-08 23:42:07');
INSERT INTO `sys_diction_item` VALUES (37, 1, 'sex', '123', '1233', 1, 1, 1, 0, 'admin', 'admin', '2024-11-08 23:42:00', '2024-11-08 23:42:07');
INSERT INTO `sys_diction_item` VALUES (38, 1, 'sex', '测试', '123', 1, 1, 1, 0, 'admin', 'admin', '2024-11-08 23:44:28', '2024-11-08 23:44:40');
INSERT INTO `sys_diction_item` VALUES (39, 5, 'notice_type', '站内', '0', 1, 1, 0, 0, 'admin', 'admin', '2024-11-23 21:26:45', '2024-11-23 21:26:45');
INSERT INTO `sys_diction_item` VALUES (40, 5, 'notice_type', 'APP', '3', 2, 1, 0, 1, 'admin', 'admin', '2024-11-23 21:26:52', '2024-11-23 21:27:00');
INSERT INTO `sys_diction_item` VALUES (41, 4, 'platform_type', '测试1', '1732678679802', 1, 1, 1, 1, 'admin', 'admin', '2024-11-27 11:38:00', '2024-11-27 11:38:21');
INSERT INTO `sys_diction_item` VALUES (42, 4, 'platform_type', '测试2', '1732678691778', 1, 1, 1, 0, 'admin', 'admin', '2024-11-27 11:38:12', '2024-11-27 11:38:21');
INSERT INTO `sys_diction_item` VALUES (43, 6, 'pet_content_type', '科普', 'science', 1, 1, 0, 0, 'admin', 'admin', '2024-12-02 17:34:26', '2024-12-02 17:34:26');
INSERT INTO `sys_diction_item` VALUES (44, 6, 'pet_content_type', '寻宠', 'look_pet', 2, 1, 0, 0, 'admin', 'admin', '2024-12-02 17:35:08', '2024-12-02 17:35:08');
INSERT INTO `sys_diction_item` VALUES (45, 6, 'pet_content_type', '领养', 'adopt', 3, 1, 0, 0, 'admin', 'admin', '2024-12-02 17:35:29', '2024-12-02 17:35:29');

-- ----------------------------
-- Table structure for sys_file_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_file_log`;
CREATE TABLE `sys_file_log`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '文件日志Id',
  `user_id` bigint NOT NULL DEFAULT 0 COMMENT '用户Id',
  `file_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '文件路径',
  `file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '文件名称',
  `file_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '文件路径',
  `source_file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '源文件名称',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '状态（0未下载1已下载）',
  `err_msg` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '失败原因',
  `time` bigint NOT NULL DEFAULT 0 COMMENT '执行时长(毫秒)',
  `export_time` datetime NOT NULL COMMENT '导出时间',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户Id',
  `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除（0否1是）',
  `create_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人',
  `update_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_file_name`(`file_name` ASC) USING BTREE,
  INDEX `idx_export_time`(`export_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 42 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '文件日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_file_log
-- ----------------------------
INSERT INTO `sys_file_log` VALUES (1, 1, 'http://lxwthinker.tpddns.cn:8081/sys/upload/user/20241107133724/博昌云logo.png', '博昌云logo.png', '/upload/user/20241107133724/博昌云logo.png', '博昌云logo.png', 0, '', 1, '2024-11-07 21:37:25', 0, 1, 0, 'admin', 'admin', '2024-11-07 21:37:25', '2024-11-07 21:37:25');
INSERT INTO `sys_file_log` VALUES (2, 1, 'http://lxwthinker.tpddns.cn:8081/sys/upload/user/20241107134001/博昌云logo.png', '博昌云logo.png', '/upload/user/20241107134001/博昌云logo.png', '博昌云logo.png', 0, '', 0, '2024-11-07 21:40:01', 0, 1, 0, 'admin', 'admin', '2024-11-07 21:40:01', '2024-11-07 21:40:01');
INSERT INTO `sys_file_log` VALUES (3, 1, '', '20241109用户列表.xlsx', '/upload/xlsx/20241109164204/20241109用户列表.xlsx', '20241109用户列表.xlsx', 0, '', 609, '2024-11-10 00:42:05', 0, 1, 0, 'admin', 'admin', '2024-11-10 00:42:05', '2024-11-10 00:42:05');
INSERT INTO `sys_file_log` VALUES (4, 1, '', '20241109用户列表.xlsx', '/upload/xlsx/20241109164208/20241109用户列表.xlsx', '20241109用户列表.xlsx', 0, '', 70, '2024-11-10 00:42:09', 0, 1, 0, 'admin', 'admin', '2024-11-10 00:42:09', '2024-11-10 00:42:09');
INSERT INTO `sys_file_log` VALUES (5, 1, '', '20241109用户列表.xlsx', '/upload/xlsx/20241109164818/20241109用户列表.xlsx', '20241109用户列表.xlsx', 0, '', 569, '2024-11-10 00:48:19', 0, 1, 0, 'admin', 'admin', '2024-11-10 00:48:19', '2024-11-10 00:48:19');
INSERT INTO `sys_file_log` VALUES (6, 1, '', '20241109用户列表.xlsx', '/upload/xlsx/20241109164850/20241109用户列表.xlsx', '20241109用户列表.xlsx', 0, '', 67, '2024-11-10 00:48:50', 0, 1, 0, 'admin', 'admin', '2024-11-10 00:48:50', '2024-11-10 00:48:50');
INSERT INTO `sys_file_log` VALUES (7, 1, '', '20241109用户列表.xlsx', '/upload/xlsx/20241109164857/20241109用户列表.xlsx', '20241109用户列表.xlsx', 0, '', 78, '2024-11-10 00:48:58', 0, 1, 0, 'admin', 'admin', '2024-11-10 00:48:58', '2024-11-10 00:48:58');
INSERT INTO `sys_file_log` VALUES (8, 1, '', '20241109用户列表.xlsx', '/upload/xlsx/20241109164904/20241109用户列表.xlsx', '20241109用户列表.xlsx', 0, '', 74, '2024-11-10 00:49:05', 0, 1, 0, 'admin', 'admin', '2024-11-10 00:49:05', '2024-11-10 00:49:05');
INSERT INTO `sys_file_log` VALUES (9, 1, '', '20241109用户列表.xlsx', '/upload/xlsx/20241109164911/20241109用户列表.xlsx', '20241109用户列表.xlsx', 0, '', 49, '2024-11-10 00:49:12', 0, 1, 0, 'admin', 'admin', '2024-11-10 00:49:12', '2024-11-10 00:49:12');
INSERT INTO `sys_file_log` VALUES (10, 1, '', '20241109用户列表.xlsx', '/upload/xlsx/20241109164917/20241109用户列表.xlsx', '20241109用户列表.xlsx', 0, '', 48, '2024-11-10 00:49:17', 0, 1, 0, 'admin', 'admin', '2024-11-10 00:49:17', '2024-11-10 00:49:17');
INSERT INTO `sys_file_log` VALUES (11, 1, '', '20241109用户列表.xlsx', '/upload/xlsx/20241109164928/20241109用户列表.xlsx', '20241109用户列表.xlsx', 0, '', 43, '2024-11-10 00:49:29', 0, 1, 0, 'admin', 'admin', '2024-11-10 00:49:29', '2024-11-10 00:49:29');
INSERT INTO `sys_file_log` VALUES (12, 1, 'http://lxwthinker.tpddns.cn:8081/sys/upload/user/20241110125605/博昌云logo.png', '博昌云logo.png', '/upload/user/20241110125605/博昌云logo.png', '博昌云logo.png', 0, '', 41, '2024-11-10 20:56:06', 0, 1, 0, 'admin', 'admin', '2024-11-10 20:56:06', '2024-11-10 20:56:06');
INSERT INTO `sys_file_log` VALUES (13, 1, '', '20241114用户列表.xlsx', '/upload/xlsx/20241114133950/20241114用户列表.xlsx', '20241114用户列表.xlsx', 0, '', 838, '2024-11-14 21:39:51', 0, 1, 0, 'admin', 'admin', '2024-11-14 21:39:51', '2024-11-14 21:39:51');
INSERT INTO `sys_file_log` VALUES (14, 1, '', '20241114用户列表.xlsx', '/upload/xlsx/20241114134646/20241114用户列表.xlsx', '20241114用户列表.xlsx', 0, '', 46, '2024-11-14 21:46:46', 0, 1, 0, 'admin', 'admin', '2024-11-14 21:46:46', '2024-11-14 21:46:46');
INSERT INTO `sys_file_log` VALUES (15, 1, '', '20241114用户列表', '/upload/xlsx/20241114144332/20241114用户列表', '20241114用户列表', 0, '', 585, '2024-11-14 22:43:33', 0, 1, 0, 'admin', 'admin', '2024-11-14 22:43:33', '2024-11-14 22:43:33');
INSERT INTO `sys_file_log` VALUES (16, 1, '', '20241114用户列表', '/upload/xlsx/20241114144904/20241114用户列表', '20241114用户列表', 0, '', 78, '2024-11-14 22:49:04', 0, 1, 0, 'admin', 'admin', '2024-11-14 22:49:04', '2024-11-14 22:49:04');
INSERT INTO `sys_file_log` VALUES (17, 1, '', '20241114用户列表', '/upload/xlsx/20241114145006/20241114用户列表', '20241114用户列表', 0, '', 62, '2024-11-14 22:50:06', 0, 1, 0, 'admin', 'admin', '2024-11-14 22:50:06', '2024-11-14 22:50:06');
INSERT INTO `sys_file_log` VALUES (18, 1, 'http://lxwthinker.tpddns.cn:32003/sys/Users/<USER>/lxw/code/bcy/plus/bcy-admin/upload/xlsx/20241114225954/用户列表.xlsx', '用户列表', '/Users/<USER>/lxw/code/bcy/plus/bcy-admin/upload/xlsx/20241114225954/用户列表', '用户列表', 0, '', 39885, '2024-11-14 23:00:34', 0, 1, 0, 'admin', 'admin', '2024-11-14 23:00:34', '2024-11-14 23:00:34');
INSERT INTO `sys_file_log` VALUES (19, 1, 'http://lxwthinker.tpddns.cn:8081/sys/upload/bcy/20241118065836/博昌云logo.png', '博昌云logo.png', '/upload/bcy/20241118065836/博昌云logo.png', '博昌云logo.png', 0, '', 1, '2024-11-18 14:58:37', 0, 1, 0, 'admin', 'admin', '2024-11-18 14:58:37', '2024-11-18 14:58:37');
INSERT INTO `sys_file_log` VALUES (20, 1, 'http://lxwthinker.tpddns.cn:8081/sys/upload/\'bcy\'/20241119013638/博昌云logo.png', '博昌云logo.png', '/upload/\'bcy\'/20241119013638/博昌云logo.png', '博昌云logo.png', 0, '', 0, '2024-11-19 09:36:39', 0, 0, 0, 'BCY', 'BCY', '2024-11-19 09:36:39', '2024-11-19 09:36:39');
INSERT INTO `sys_file_log` VALUES (21, 1, 'http://lxwthinker.tpddns.cn:8081/sys/upload/\'bcy\'/20241119013643/博昌云logo-纵.png', '博昌云logo-纵.png', '/upload/\'bcy\'/20241119013643/博昌云logo-纵.png', '博昌云logo-纵.png', 0, '', 0, '2024-11-19 09:36:44', 0, 0, 0, 'BCY', 'BCY', '2024-11-19 09:36:44', '2024-11-19 09:36:44');
INSERT INTO `sys_file_log` VALUES (22, 1, 'http://lxwthinker.tpddns.cn:8081/sys/upload/bcy/20241119014353/博昌云logo.png', '博昌云logo.png', '/upload/bcy/20241119014353/博昌云logo.png', '博昌云logo.png', 0, '', 5, '2024-11-19 09:43:54', 0, 0, 0, 'BCY', 'BCY', '2024-11-19 09:43:54', '2024-11-19 09:43:54');
INSERT INTO `sys_file_log` VALUES (23, 1, 'http://lxwthinker.tpddns.cn:8081/sys/upload/bcy/20241119014356/博昌云logo-横.png', '博昌云logo-横.png', '/upload/bcy/20241119014356/博昌云logo-横.png', '博昌云logo-横.png', 0, '', 0, '2024-11-19 09:43:57', 0, 0, 1, 'BCY', 'admin', '2024-11-19 09:43:57', '2024-11-19 09:44:03');
INSERT INTO `sys_file_log` VALUES (24, 1, 'https://api.bcycloud.com/sys/upload/xlsx/20241122231710/客户列表.xlsx', '客户列表', '/upload/xlsx/20241122231710/客户列表', '客户列表', 0, '', 1397, '2024-11-22 23:17:12', 0, 1, 0, 'admin', 'admin', '2024-11-22 23:17:12', '2024-11-22 23:17:12');
INSERT INTO `sys_file_log` VALUES (25, 1, 'https://api.bcycloud.com/sys/upload/xlsx/20241123115103/客户列表.xlsx', '客户列表', '/upload/xlsx/20241123115103/客户列表.xlsx', '客户列表', 0, '', 1262, '2024-11-23 11:51:05', 0, 1, 0, 'admin', 'admin', '2024-11-23 11:51:05', '2024-11-23 11:51:05');
INSERT INTO `sys_file_log` VALUES (26, 1, 'https://api.bcycloud.com/sys/upload/xlsx/20241123123336/客户列表.xlsx', '客户列表.xlsx', '/upload/xlsx/20241123123336/客户列表.xlsx', '客户列表.xlsx', 0, '', 1574, '2024-11-23 12:33:38', 0, 1, 0, 'admin', 'admin', '2024-11-23 12:33:38', '2024-11-23 12:33:38');
INSERT INTO `sys_file_log` VALUES (27, 1, 'https://api.bcycloud.com/sys/upload/bcy/20241127141107/博昌云logo-横.png', '博昌云logo-横.png', '/upload/bcy/20241127141107/博昌云logo-横.png', '博昌云logo-横.png', 0, '', 2, '2024-11-27 14:11:08', 0, 0, 1, 'BCY', 'admin', '2024-11-27 14:11:08', '2024-11-27 14:11:34');
INSERT INTO `sys_file_log` VALUES (28, 1, 'https://api.bcycloud.com/sys/upload/bcy/20241128175742/缅因猫.png', '缅因猫.png', '/upload/bcy/20241128175742/缅因猫.png', '缅因猫.png', 0, '', 4, '2024-11-28 17:57:43', 0, 1, 0, 'admin', 'admin', '2024-11-28 17:57:43', '2024-11-28 17:57:43');
INSERT INTO `sys_file_log` VALUES (29, 1, 'https://api.bcycloud.com/sys/upload/bcy/20241129144804/缅因猫.png', '缅因猫.png', '/upload/bcy/20241129144804/缅因猫.png', '缅因猫.png', 0, '', 20, '2024-11-29 14:48:04', 0, 1, 0, 'admin', 'admin', '2024-11-29 14:48:04', '2024-11-29 14:48:04');
INSERT INTO `sys_file_log` VALUES (30, 1, 'https://api.bcycloud.com/sys/upload/bcy/20241203102846/3a48ccfb38e146898c00ad0ac93ef57d_1.png', '3a48ccfb38e146898c00ad0ac93ef57d_1.png', '/upload/bcy/20241203102846/3a48ccfb38e146898c00ad0ac93ef57d_1.png', '3a48ccfb38e146898c00ad0ac93ef57d_1.png', 0, '', 7, '2024-12-03 10:28:46', 0, 1, 0, 'admin', 'admin', '2024-12-03 10:28:46', '2024-12-03 10:28:46');
INSERT INTO `sys_file_log` VALUES (31, 1, 'https://api.bcycloud.com/sys/upload/bcy/20241203103113/259ce0704d694d30ac1e00a7109d5962_2.png', '259ce0704d694d30ac1e00a7109d5962_2.png', '/upload/bcy/20241203103113/259ce0704d694d30ac1e00a7109d5962_2.png', '259ce0704d694d30ac1e00a7109d5962_2.png', 0, '', 3, '2024-12-03 10:31:13', 0, 1, 0, 'admin', 'admin', '2024-12-03 10:31:13', '2024-12-03 10:31:13');
INSERT INTO `sys_file_log` VALUES (32, 1, 'https://api.bcycloud.com/sys/upload/bcy/20241203103539/e5ee411551844f7aa3787f1d80bc6fbd_3.png', 'e5ee411551844f7aa3787f1d80bc6fbd_3.png', '/upload/bcy/20241203103539/e5ee411551844f7aa3787f1d80bc6fbd_3.png', 'e5ee411551844f7aa3787f1d80bc6fbd_3.png', 0, '', 3, '2024-12-03 10:35:39', 0, 1, 0, 'admin', 'admin', '2024-12-03 10:35:39', '2024-12-03 10:35:39');
INSERT INTO `sys_file_log` VALUES (33, 1, 'https://api.bcycloud.com/sys/upload/bcy/20241203110532/8ece14b6c6d4e87d48047ffba3652ec9.jpeg', '8ece14b6c6d4e87d48047ffba3652ec9.jpeg', '/upload/bcy/20241203110532/8ece14b6c6d4e87d48047ffba3652ec9.jpeg', '8ece14b6c6d4e87d48047ffba3652ec9.jpeg', 0, '', 0, '2024-12-03 11:05:32', 0, 1, 0, 'admin', 'admin', '2024-12-03 11:05:32', '2024-12-03 11:05:32');
INSERT INTO `sys_file_log` VALUES (34, 1, 'https://api.bcycloud.com/sys/upload/bcy/20241203110701/3831a946361557ef10b6170c34f8996b.jpeg', '3831a946361557ef10b6170c34f8996b.jpeg', '/upload/bcy/20241203110701/3831a946361557ef10b6170c34f8996b.jpeg', '3831a946361557ef10b6170c34f8996b.jpeg', 0, '', 3, '2024-12-03 11:07:01', 0, 1, 0, 'admin', 'admin', '2024-12-03 11:07:01', '2024-12-03 11:07:01');
INSERT INTO `sys_file_log` VALUES (35, 1, 'https://api.bcycloud.com/sys/upload/bcy/20241203110749/dc665291b2f7d26059c3f882a7ccf6ab.jpeg', 'dc665291b2f7d26059c3f882a7ccf6ab.jpeg', '/upload/bcy/20241203110749/dc665291b2f7d26059c3f882a7ccf6ab.jpeg', 'dc665291b2f7d26059c3f882a7ccf6ab.jpeg', 0, '', 3, '2024-12-03 11:07:50', 0, 1, 0, 'admin', 'admin', '2024-12-03 11:07:50', '2024-12-03 11:07:50');
INSERT INTO `sys_file_log` VALUES (36, 1, 'https://api.bcycloud.com/sys/upload/bcy/20241203111516/305a6cb6b6eca1f359b64ca233d513fb.jpeg', '305a6cb6b6eca1f359b64ca233d513fb.jpeg', '/upload/bcy/20241203111516/305a6cb6b6eca1f359b64ca233d513fb.jpeg', '305a6cb6b6eca1f359b64ca233d513fb.jpeg', 0, '', 1, '2024-12-03 11:15:17', 0, 1, 0, 'admin', 'admin', '2024-12-03 11:15:17', '2024-12-03 11:15:17');
INSERT INTO `sys_file_log` VALUES (37, 0, 'https://api.bcycloud.com/sys/upload/bcyapp/20241219115945/博昌云logo.png', '博昌云logo.png', '/upload/bcyapp/20241219115945/博昌云logo.png', '博昌云logo.png', 0, '', 8, '2024-12-19 11:59:46', 0, 0, 0, 'BCY', 'BCY', '2024-12-19 11:59:46', '2024-12-19 11:59:46');
INSERT INTO `sys_file_log` VALUES (38, 0, 'https://api.bcycloud.com/sys/upload/bcyapp/20241219151119/jmckJMAaSeBs66a9464e37e6aecb8e748ddd5c33281d.png', 'jmckJMAaSeBs66a9464e37e6aecb8e748ddd5c33281d.png', '/upload/bcyapp/20241219151119/jmckJMAaSeBs66a9464e37e6aecb8e748ddd5c33281d.png', 'jmckJMAaSeBs66a9464e37e6aecb8e748ddd5c33281d.png', 0, '', 0, '2024-12-19 15:11:19', 0, 0, 0, 'BCY', 'BCY', '2024-12-19 15:11:19', '2024-12-19 15:11:19');
INSERT INTO `sys_file_log` VALUES (39, 0, 'https://api.bcycloud.com/sys/upload/bcyapp/20241219152535/博昌云logo.png', '博昌云logo.png', '/upload/bcyapp/20241219152535/博昌云logo.png', '博昌云logo.png', 0, '', 0, '2024-12-19 15:25:35', 0, 1, 0, 'BCY', 'BCY', '2024-12-19 15:25:35', '2024-12-19 15:25:35');
INSERT INTO `sys_file_log` VALUES (40, 0, 'https://api.bcycloud.com/sys/upload/bcyapp/20241223160315/ENOShNAG0KxI66a9464e37e6aecb8e748ddd5c33281d.png', 'ENOShNAG0KxI66a9464e37e6aecb8e748ddd5c33281d.png', '/upload/bcyapp/20241223160315/ENOShNAG0KxI66a9464e37e6aecb8e748ddd5c33281d.png', 'ENOShNAG0KxI66a9464e37e6aecb8e748ddd5c33281d.png', 0, '', 5, '2024-12-23 16:03:16', 0, 1, 0, 'BCY', 'BCY', '2024-12-23 16:03:16', '2024-12-23 16:03:16');
INSERT INTO `sys_file_log` VALUES (41, 0, 'https://api.bcycloud.com/sys/upload/bcyapp/20241224154630/博昌云logo.png', '博昌云logo.png', '/upload/bcyapp/20241224154630/博昌云logo.png', '博昌云logo.png', 0, '', 1, '2024-12-24 15:46:30', 0, 1, 0, 'BCY', 'BCY', '2024-12-24 15:46:30', '2024-12-24 15:46:30');

-- ----------------------------
-- Table structure for sys_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_log`;
CREATE TABLE `sys_log`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志Id',
  `user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户名',
  `operator_type` tinyint NOT NULL DEFAULT 1 COMMENT '操作类别（0:其它,1:后台用户,2:手机端用户）',
  `model` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '模块',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '请求地址',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '操作状态(0:正常,1:异常)',
  `operation` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户操作',
  `method` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '请求方法',
  `params` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '请求参数',
  `result` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '请求结果',
  `error_msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '错误信息',
  `time` bigint NOT NULL COMMENT '执行时长(毫秒)',
  `ip` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'IP地址',
  `ip_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT 'IP所在地',
  `create_date` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_name`(`user_name` ASC) USING BTREE,
  INDEX `idx_operation`(`operation` ASC) USING BTREE,
  INDEX `idx_create_date`(`create_date` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 182 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_log
-- ----------------------------
INSERT INTO `sys_log` VALUES (39, 'BCY', 1, 'SYS', '/sys/login/logout', 0, '用户注销', 'com.bcy.system.api.login.LoginController.logout()', '{}', '{\"code\":200,\"data\":\"退出成功\"}', '', 13, '*************', '中国 福建 厦门 电信', '2024-11-09 23:06:59');
INSERT INTO `sys_log` VALUES (40, 'admin', 1, 'SYS', '/sys/login/sys', 0, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"admin\",\"uuid\":\"\"}', '{\"code\":200,\"data\":{\"accessToken\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjEsInJuU3RyIjoiQXRBcjRrSklVY0syU2lWYU1qSTFzQ0psb1cwZGJiU3cifQ.OAWoSNjI-M4kTT4lBQXdTIhX-agDgkJ4_a9kNkFJkyI\",\"expireTime\":259200}}', '', 792, '*************', '中国 福建 厦门 电信', '2024-11-09 23:07:11');
INSERT INTO `sys_log` VALUES (41, 'admin', 1, 'SYS', '/sys/config/update', 0, '修改配置', 'com.bcy.system.api.sys.SysConfigController.update()', '{\"id\":1,\"name\":\"登录页展示\",\"paramsKey\":\"SYS_SETTING\",\"paramsValue\":\"{\\n\\t\\\"title\\\": \\\"博昌云科技\\\",\\n\\t\\\"version\\\": \\\"多租户\\\",\\n\\t\\\"isTenant\\\": 1,\\n\\t\\\"notice\\\": \\\"Copyright © 2023 - 2024 博昌云科技 All Rights Reserved.\\\",\\n\\t\\\"beiAnNumber\\\": \\\"\\\",\\n\\t\\\"number\\\": \\\"\\\",\\n\\t\\\"websocket\\\": \\\"wss://api.bcycloud.com/sys/websocket\\\",\\n\\t\\\"isCode\\\": 1,\\n}\",\"remark\":\"\",\"status\":0}', '{\"code\":200,\"data\":\"更新成功\"}', '', 232, '*************', '中国 福建 厦门 电信', '2024-11-09 23:07:51');
INSERT INTO `sys_log` VALUES (42, 'BCY', 1, 'SYS', '/sys/login/logout', 0, '用户注销', 'com.bcy.system.api.login.LoginController.logout()', '{}', '{\"code\":200,\"data\":\"退出成功\"}', '', 3, '*************', '中国 福建 厦门 电信', '2024-11-09 23:07:54');
INSERT INTO `sys_log` VALUES (43, 'admin', 1, 'SYS', '/sys/login/sys', 0, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"pec7e\",\"userName\":\"admin\",\"uuid\":\"bcy1731164874917\"}', '{\"code\":200,\"data\":{\"accessToken\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjEsInJuU3RyIjoiZ2F0RWR5SWQzWTZ5YVJJRjZCSFJONEJTSVdzOHJsMzgifQ.WKrAh0Z6oI3RL0SqMCgnAKKB0PTmneDjvlZv7Ug7EBc\",\"expireTime\":259200}}', '', 186, '*************', '中国 福建 厦门 电信', '2024-11-09 23:08:00');
INSERT INTO `sys_log` VALUES (44, 'admin', 1, 'SYS', '/sys/config/update', 0, '修改配置', 'com.bcy.system.api.sys.SysConfigController.update()', '{\"id\":1,\"name\":\"登录页展示\",\"paramsKey\":\"SYS_SETTING\",\"paramsValue\":\"{\\n\\t\\\"title\\\": \\\"博昌云科技\\\",\\n\\t\\\"version\\\": \\\"多租户\\\",\\n\\t\\\"isTenant\\\": 1,\\n\\t\\\"notice\\\": \\\"Copyright © 2023 - 2024 博昌云科技 All Rights Reserved.\\\",\\n\\t\\\"beiAnNumber\\\": \\\"\\\",\\n\\t\\\"number\\\": \\\"\\\",\\n\\t\\\"websocket\\\": \\\"wss://api.bcycloud.com/sys/websocket\\\",\\n\\t\\\"isCode\\\": 0,\\n}\",\"remark\":\"\",\"status\":0}', '{\"code\":200,\"data\":\"更新成功\"}', '', 210, '*************', '中国 福建 厦门 电信', '2024-11-09 23:08:08');
INSERT INTO `sys_log` VALUES (45, 'BCY', 1, 'SYS', '/sys/login/logout', 0, '用户注销', 'com.bcy.system.api.login.LoginController.logout()', '{}', '{\"code\":200,\"data\":\"退出成功\"}', '', 124, '*************', '中国 福建 厦门 电信', '2024-11-10 00:52:41');
INSERT INTO `sys_log` VALUES (46, 'admin', 1, 'SYS', '/sys/login/sys', 0, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"admin\",\"uuid\":\"\"}', '{\"code\":200,\"data\":{\"accessToken\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjEsInJuU3RyIjoiRWxaNmN2VW4xQ2R4Q0xqQXJWWGN2MkV3TUpXeVF3QzQifQ.khqGh8XxI0aMHR6DImE_nqWBIVIn-A8szSsJMIOhSL4\",\"expireTime\":259200}}', '', 1097, '*************', '中国 福建 厦门 电信', '2024-11-10 00:52:44');
INSERT INTO `sys_log` VALUES (47, 'BCY', 1, 'SYS', '/sys/login/logout', 0, '用户注销', 'com.bcy.system.api.login.LoginController.logout()', '{}', '{\"code\":200,\"data\":\"退出成功\"}', '', 5, '*************', '中国 福建 厦门 电信', '2024-11-10 00:54:03');
INSERT INTO `sys_log` VALUES (48, 'admin', 1, 'SYS', '/sys/login/sys', 0, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"admin\",\"uuid\":\"\"}', '{\"code\":200,\"data\":{\"accessToken\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjEsInJuU3RyIjoicUJ0RDAyUFdTNkJabG5ieTVDUUtuNldPQzhJd2pKcVkifQ.SoKhLP4vIhQkAbJKQTNOcr2xwcyO0j-TL3P7rSWLc_s\",\"expireTime\":259200}}', '', 240, '*************', '中国 福建 厦门 电信', '2024-11-10 00:54:05');
INSERT INTO `sys_log` VALUES (49, 'BCY', 1, 'SYS', '/sys/login/logout', 0, '用户注销', 'com.bcy.system.api.login.LoginController.logout()', '{}', '{\"code\":200,\"data\":\"退出成功\"}', '', 6, '*************', '中国 福建 厦门 电信', '2024-11-10 01:02:51');
INSERT INTO `sys_log` VALUES (50, 'admin', 1, 'SYS', '/sys/login/sys', 0, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"admin\",\"uuid\":\"\"}', '{\"code\":200,\"data\":{\"accessToken\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjEsInJuU3RyIjoiZGpQdFRCODdpaDVxcWlWTEM5akptU3B5VmVlNVlTNXcifQ.OronZf03aHdDAx0-bz3lFhcfi5pYBTIMj9nXKhgSezI\",\"expireTime\":259200}}', '', 204, '*************', '中国 福建 厦门 电信', '2024-11-10 01:02:53');
INSERT INTO `sys_log` VALUES (51, 'BCY', 1, 'SYS', '/sys/login/logout', 0, '用户注销', 'com.bcy.system.api.login.LoginController.logout()', '{}', '{\"code\":200,\"data\":\"退出成功\"}', '', 3, '*************', '中国 福建 厦门 电信', '2024-11-10 01:02:59');
INSERT INTO `sys_log` VALUES (52, 'admin', 1, 'SYS', '/sys/login/sys', 0, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"admin\",\"uuid\":\"\"}', '{\"code\":200,\"data\":{\"accessToken\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjEsInJuU3RyIjoibTNyWWh1WkIzVGdsZVR0a0wwMFl5M2R1UlJxUGJORGoifQ.Co1gtnOuvbbv5NW7awSHeMhIb4N5Ak82dkwuMSCddAE\",\"expireTime\":259200}}', '', 401, '*************', '中国 福建 厦门 电信', '2024-11-10 01:03:01');
INSERT INTO `sys_log` VALUES (53, 'admin', 1, 'SYS', '/sys/login/sys', 0, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"admin\",\"uuid\":\"\"}', '{\"code\":200,\"data\":{\"accessToken\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjEsInJuU3RyIjoieEYyUXdGSWdNWjdhM25NcmhWaGhQY0VyUVNQVlp6VlYifQ.y2JjyFGyuxHx4cTF7r0Th3El_WO3Xra6YHs1rWJYBcc\",\"expireTime\":259200}}', '', 969, '*************', '中国 福建 厦门 电信', '2024-11-10 01:05:56');
INSERT INTO `sys_log` VALUES (54, 'admin', 1, 'SYS', '/sys/menu/update', 0, '修改菜单', 'com.bcy.system.api.sys.SysMenuController.update()', '{\"alwaysShow\":0,\"component\":\"logs/syslog/index\",\"icon\":\"table\",\"id\":33,\"keepAlive\":0,\"module\":\"bcy-sys\",\"name\":\"日志管理\",\"parentId\":1,\"path\":\"syslog\",\"perm\":\"\",\"redirect\":\"\",\"sort\":199,\"type\":\"MENU\",\"visible\":1}', '{\"code\":200,\"data\":\"更新成功\"}', '', 160, '*************', '中国 福建 厦门 电信', '2024-11-10 19:39:29');
INSERT INTO `sys_log` VALUES (55, 'admin', 1, 'SYS', '/sys/menu/insert', 1, '新增菜单', 'com.bcy.system.api.sys.SysMenuController.insert()', '{\"alwaysShow\":1,\"icon\":\"el-icon-Coffee\",\"keepAlive\":1,\"name\":\"日志管理\",\"parentId\":0,\"path\":\"logs\",\"sort\":1,\"type\":\"CATALOG\",\"visible\":1}', '{\"code\":500,\"errMsg\":\"null该菜单名称已存在!\",\"module\":\"BCY\",\"stackTrace\":[{\"fileName\":\"BcySysMenuService.java\",\"lineNumber\":175,\"className\":\"com.bcy.system.business.sys.BcySysMenuService\",\"methodName\":\"saveMenu\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":-2,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke0\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":77,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"DelegatingMethodAccessorImpl.java\",\"lineNumber\":43,\"className\":\"jdk.internal.reflect.DelegatingMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"Method.java\",\"lineNumber\":568,\"className\":\"java.lang.reflect.Method\",\"methodName\":\"invoke\"},{\"fileName\":\"AopUtils.java\",\"lineNumber\":355,\"className\":\"org.springframework.aop.support.AopUtils\",\"methodName\":\"invokeJoinpointUsingReflection\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":716,\"className\":\"org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor\",\"methodName\":\"intercept\"},{\"fileName\":\"<generated>\",\"lineNumber\":-1,\"className\":\"com.bcy.system.business.sys.BcySysMenuService$$SpringCGLIB$$0\",\"methodName\":\"saveMenu\"},{\"fileName\":\"SysMenuController.java\",\"lineNumber\":61,\"className\":\"com.bcy.system.api.sys.SysMenuController\",\"methodName\":\"insert\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":-2,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke0\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":77,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"DelegatingMethodAccessorImpl.java\",\"lineNumber\":43,\"className\":\"jdk.internal.reflect.DelegatingMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"Method.java\",\"lineNumber\":568,\"className\":\"java.lang.reflect.Method\",\"methodName\":\"invoke\"},{\"fileName\":\"AopUtils.java\",\"lineNumber\":355,\"className\":\"org.springframework.aop.support.AopUtils\",\"methodName\":\"invokeJoin', '', 40, '*************', '中国 福建 厦门 电信', '2024-11-10 19:40:23');
INSERT INTO `sys_log` VALUES (56, 'admin', 1, 'SYS', '/sys/menu/update', 0, '修改菜单', 'com.bcy.system.api.sys.SysMenuController.update()', '{\"alwaysShow\":0,\"component\":\"logs/syslog/index\",\"icon\":\"table\",\"id\":33,\"keepAlive\":0,\"module\":\"bcy-sys\",\"name\":\"系统日志\",\"parentId\":1,\"path\":\"syslog\",\"perm\":\"\",\"redirect\":\"\",\"sort\":199,\"type\":\"MENU\",\"visible\":1}', '{\"code\":200,\"data\":\"更新成功\"}', '', 157, '*************', '中国 福建 厦门 电信', '2024-11-10 19:40:37');
INSERT INTO `sys_log` VALUES (57, 'admin', 1, 'SYS', '/sys/menu/insert', 0, '新增菜单', 'com.bcy.system.api.sys.SysMenuController.insert()', '{\"alwaysShow\":1,\"icon\":\"el-icon-Coffee\",\"keepAlive\":0,\"module\":\"bcy-sys\",\"name\":\"日志管理\",\"parentId\":0,\"path\":\"logs\",\"perm\":\"\",\"redirect\":\"\",\"sort\":100,\"type\":\"CATALOG\",\"visible\":1}', '{\"code\":200,\"data\":\"新增成功\"}', '', 172, '*************', '中国 福建 厦门 电信', '2024-11-10 19:41:00');
INSERT INTO `sys_log` VALUES (58, 'admin', 1, 'SYS', '/sys/menu/update', 0, '修改菜单', 'com.bcy.system.api.sys.SysMenuController.update()', '{\"alwaysShow\":0,\"component\":\"logs/syslog/index\",\"icon\":\"table\",\"id\":33,\"keepAlive\":0,\"module\":\"bcy-sys\",\"name\":\"系统日志\",\"parentId\":53,\"path\":\"syslog\",\"perm\":\"\",\"redirect\":\"\",\"sort\":199,\"type\":\"MENU\",\"visible\":1}', '{\"code\":200,\"data\":\"更新成功\"}', '', 207, '*************', '中国 福建 厦门 电信', '2024-11-10 19:41:13');
INSERT INTO `sys_log` VALUES (59, 'admin', 1, 'SYS', '/sys/menu/update', 0, '修改菜单', 'com.bcy.system.api.sys.SysMenuController.update()', '{\"alwaysShow\":0,\"component\":\"system/apilog/index\",\"icon\":\"api\",\"id\":35,\"keepAlive\":1,\"module\":\"bcy-sys\",\"name\":\"API日志\",\"parentId\":53,\"path\":\"apilog\",\"perm\":\"\",\"redirect\":\"\",\"sort\":200,\"type\":\"MENU\",\"visible\":1}', '{\"code\":200,\"data\":\"更新成功\"}', '', 175, '*************', '中国 福建 厦门 电信', '2024-11-10 19:41:27');
INSERT INTO `sys_log` VALUES (60, 'admin', 1, 'SYS', '/sys/menu/update', 0, '修改菜单', 'com.bcy.system.api.sys.SysMenuController.update()', '{\"alwaysShow\":0,\"component\":\"Layout\",\"icon\":\"el-icon-Coffee\",\"id\":53,\"keepAlive\":0,\"module\":\"bcy-sys\",\"name\":\"日志管理\",\"parentId\":0,\"path\":\"/logs\",\"perm\":\"\",\"redirect\":\"\",\"sort\":100,\"type\":\"CATALOG\",\"visible\":1}', '{\"code\":200,\"data\":\"更新成功\"}', '', 207, '*************', '中国 福建 厦门 电信', '2024-11-10 19:41:44');
INSERT INTO `sys_log` VALUES (61, 'admin', 1, 'SYS', '/sys/menu/insert', 0, '新增菜单', 'com.bcy.system.api.sys.SysMenuController.insert()', '{\"alwaysShow\":0,\"component\":\"logs/filelog/index\",\"icon\":\"el-icon-Folder\",\"keepAlive\":1,\"name\":\"文件日志\",\"parentId\":53,\"path\":\"filelog\",\"sort\":3,\"type\":\"MENU\",\"visible\":1}', '{\"code\":200,\"data\":\"新增成功\"}', '', 168, '*************', '中国 福建 厦门 电信', '2024-11-10 19:42:41');
INSERT INTO `sys_log` VALUES (62, 'admin', 1, 'SYS', '/sys/menu/update', 0, '修改菜单', 'com.bcy.system.api.sys.SysMenuController.update()', '{\"alwaysShow\":0,\"component\":\"Layout\",\"icon\":\"el-icon-Coffee\",\"id\":53,\"keepAlive\":0,\"module\":\"bcy-sys\",\"name\":\"日志管理\",\"parentId\":0,\"path\":\"/logs\",\"perm\":\"\",\"redirect\":\"\",\"sort\":101,\"type\":\"CATALOG\",\"visible\":1}', '{\"code\":200,\"data\":\"更新成功\"}', '', 265, '*************', '中国 福建 厦门 电信', '2024-11-10 19:43:03');
INSERT INTO `sys_log` VALUES (63, 'admin', 1, 'SYS', '/sys/menu/update', 0, '修改菜单', 'com.bcy.system.api.sys.SysMenuController.update()', '{\"alwaysShow\":1,\"component\":\"logs/filelog/index\",\"icon\":\"el-icon-Folder\",\"id\":54,\"keepAlive\":1,\"module\":\"\",\"name\":\"文件日志\",\"parentId\":53,\"path\":\"filelog\",\"perm\":\"\",\"redirect\":\"\",\"sort\":3,\"type\":\"MENU\",\"visible\":1}', '{\"code\":200,\"data\":\"更新成功\"}', '', 214, '*************', '中国 福建 厦门 电信', '2024-11-10 19:43:35');
INSERT INTO `sys_log` VALUES (64, 'admin', 1, 'SYS', '/sys/menu/update', 0, '修改菜单', 'com.bcy.system.api.sys.SysMenuController.update()', '{\"alwaysShow\":0,\"component\":\"logs/apilog/index\",\"icon\":\"api\",\"id\":35,\"keepAlive\":1,\"module\":\"bcy-sys\",\"name\":\"API日志\",\"parentId\":53,\"path\":\"apilog\",\"perm\":\"\",\"redirect\":\"\",\"sort\":200,\"type\":\"MENU\",\"visible\":1}', '{\"code\":200,\"data\":\"更新成功\"}', '', 256, '*************', '中国 福建 厦门 电信', '2024-11-10 19:43:58');
INSERT INTO `sys_log` VALUES (65, 'admin', 1, 'SYS', '/sys/menu/update', 0, '修改菜单', 'com.bcy.system.api.sys.SysMenuController.update()', '{\"alwaysShow\":0,\"component\":\"logs/filelog/index\",\"icon\":\"el-icon-Folder\",\"id\":54,\"keepAlive\":1,\"module\":\"\",\"name\":\"文件日志\",\"parentId\":53,\"path\":\"filelog\",\"perm\":\"\",\"redirect\":\"\",\"sort\":1,\"type\":\"MENU\",\"visible\":1}', '{\"code\":200,\"data\":\"更新成功\"}', '', 221, '*************', '中国 福建 厦门 电信', '2024-11-10 19:44:15');
INSERT INTO `sys_log` VALUES (66, 'admin', 1, 'SYS', '/sys/menu/update', 0, '修改菜单', 'com.bcy.system.api.sys.SysMenuController.update()', '{\"alwaysShow\":0,\"component\":\"logs/syslog/index\",\"icon\":\"table\",\"id\":33,\"keepAlive\":0,\"module\":\"bcy-sys\",\"name\":\"系统日志\",\"parentId\":53,\"path\":\"syslog\",\"perm\":\"\",\"redirect\":\"\",\"sort\":2,\"type\":\"MENU\",\"visible\":1}', '{\"code\":200,\"data\":\"更新成功\"}', '', 169, '*************', '中国 福建 厦门 电信', '2024-11-10 19:44:25');
INSERT INTO `sys_log` VALUES (67, 'admin', 1, 'SYS', '/sys/menu/update', 0, '修改菜单', 'com.bcy.system.api.sys.SysMenuController.update()', '{\"alwaysShow\":0,\"component\":\"logs/apilog/index\",\"icon\":\"api\",\"id\":35,\"keepAlive\":0,\"module\":\"bcy-sys\",\"name\":\"API日志\",\"parentId\":53,\"path\":\"apilog\",\"perm\":\"\",\"redirect\":\"\",\"sort\":3,\"type\":\"MENU\",\"visible\":1}', '{\"code\":200,\"data\":\"更新成功\"}', '', 177, '*************', '中国 福建 厦门 电信', '2024-11-10 19:44:36');
INSERT INTO `sys_log` VALUES (68, 'admin', 1, 'SYS', '/sys/log/sys/clean', 0, '清理日志', 'com.bcy.system.api.log.SysLogController.clean()', '{\"day\":\"7\"}', '{\"code\":500,\"msg\":\"暂无数据\"}', '', 171, '*************', '中国 福建 厦门 电信', '2024-11-10 20:12:16');
INSERT INTO `sys_log` VALUES (69, 'admin', 1, 'SYS', '/sys/log/sys/clean', 0, '清理日志', 'com.bcy.system.api.log.SysLogController.clean()', '{\"day\":\"1\"}', '{\"code\":200,\"data\":\"清除成功\"}', '', 237, '*************', '中国 福建 厦门 电信', '2024-11-10 20:12:26');
INSERT INTO `sys_log` VALUES (70, 'admin', 1, 'SYS', '/sys/log/file/delete', 1, '批量删除文件日志', 'com.bcy.system.api.log.SysFileLogController.delete()', '{\"ids\":[11]}', '{\"code\":500,\"errMsg\":\"批量删除失败，存在未下载的文件\",\"module\":\"BCY\",\"stackTrace\":[{\"fileName\":\"BcyFileLogService.java\",\"lineNumber\":76,\"className\":\"com.bcy.system.business.log.BcyFileLogService\",\"methodName\":\"deleteLogs\"},{\"fileName\":\"SysFileLogController.java\",\"lineNumber\":41,\"className\":\"com.bcy.system.api.log.SysFileLogController\",\"methodName\":\"delete\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":-2,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke0\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":77,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"DelegatingMethodAccessorImpl.java\",\"lineNumber\":43,\"className\":\"jdk.internal.reflect.DelegatingMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"Method.java\",\"lineNumber\":568,\"className\":\"java.lang.reflect.Method\",\"methodName\":\"invoke\"},{\"fileName\":\"AopUtils.java\",\"lineNumber\":355,\"className\":\"org.springframework.aop.support.AopUtils\",\"methodName\":\"invokeJoinpointUsingReflection\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":196,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"invokeJoinpoint\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":163,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"AspectJAfterThrowingAdvice.java\",\"lineNumber\":64,\"className\":\"org.springframework.aop.aspectj.AspectJAfterThrowingAdvice\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":173,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed', '', 7, '*************', '中国 福建 厦门 电信', '2024-11-10 20:49:48');
INSERT INTO `sys_log` VALUES (71, 'admin', 1, 'SYS', '/sys/log/file/delete', 1, '批量删除文件日志', 'com.bcy.system.api.log.SysFileLogController.delete()', '{\"ids\":[10]}', '{\"code\":500,\"errMsg\":\"批量删除失败，存在未下载的文件\",\"module\":\"BCY\",\"stackTrace\":[{\"fileName\":\"BcyFileLogService.java\",\"lineNumber\":76,\"className\":\"com.bcy.system.business.log.BcyFileLogService\",\"methodName\":\"deleteLogs\"},{\"fileName\":\"SysFileLogController.java\",\"lineNumber\":41,\"className\":\"com.bcy.system.api.log.SysFileLogController\",\"methodName\":\"delete\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":-2,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke0\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":77,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"DelegatingMethodAccessorImpl.java\",\"lineNumber\":43,\"className\":\"jdk.internal.reflect.DelegatingMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"Method.java\",\"lineNumber\":568,\"className\":\"java.lang.reflect.Method\",\"methodName\":\"invoke\"},{\"fileName\":\"AopUtils.java\",\"lineNumber\":355,\"className\":\"org.springframework.aop.support.AopUtils\",\"methodName\":\"invokeJoinpointUsingReflection\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":196,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"invokeJoinpoint\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":163,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"AspectJAfterThrowingAdvice.java\",\"lineNumber\":64,\"className\":\"org.springframework.aop.aspectj.AspectJAfterThrowingAdvice\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":173,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed', '', 3, '*************', '中国 福建 厦门 电信', '2024-11-10 20:49:59');
INSERT INTO `sys_log` VALUES (72, 'admin', 1, 'SYS', '/sys/log/file/delete', 1, '批量删除文件日志', 'com.bcy.system.api.log.SysFileLogController.delete()', '{\"ids\":[11]}', '{\"code\":500,\"errMsg\":\"批量删除失败，存在未下载的文件\",\"module\":\"BCY\",\"stackTrace\":[{\"fileName\":\"BcyFileLogService.java\",\"lineNumber\":76,\"className\":\"com.bcy.system.business.log.BcyFileLogService\",\"methodName\":\"deleteLogs\"},{\"fileName\":\"SysFileLogController.java\",\"lineNumber\":41,\"className\":\"com.bcy.system.api.log.SysFileLogController\",\"methodName\":\"delete\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":-2,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke0\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":77,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"DelegatingMethodAccessorImpl.java\",\"lineNumber\":43,\"className\":\"jdk.internal.reflect.DelegatingMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"Method.java\",\"lineNumber\":568,\"className\":\"java.lang.reflect.Method\",\"methodName\":\"invoke\"},{\"fileName\":\"AopUtils.java\",\"lineNumber\":355,\"className\":\"org.springframework.aop.support.AopUtils\",\"methodName\":\"invokeJoinpointUsingReflection\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":196,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"invokeJoinpoint\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":163,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"AspectJAfterThrowingAdvice.java\",\"lineNumber\":64,\"className\":\"org.springframework.aop.aspectj.AspectJAfterThrowingAdvice\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":173,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed', '', 3, '*************', '中国 福建 厦门 电信', '2024-11-10 20:58:39');
INSERT INTO `sys_log` VALUES (73, 'admin', 1, 'SYS', '/sys/log/file/delete', 1, '批量删除文件日志', 'com.bcy.system.api.log.SysFileLogController.delete()', '{\"ids\":[12]}', '{\"code\":500,\"errMsg\":\"批量删除失败，存在未下载的文件\",\"module\":\"BCY\",\"stackTrace\":[{\"fileName\":\"BcyFileLogService.java\",\"lineNumber\":80,\"className\":\"com.bcy.system.business.log.BcyFileLogService\",\"methodName\":\"deleteLogs\"},{\"fileName\":\"SysFileLogController.java\",\"lineNumber\":41,\"className\":\"com.bcy.system.api.log.SysFileLogController\",\"methodName\":\"delete\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":-2,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke0\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":77,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"DelegatingMethodAccessorImpl.java\",\"lineNumber\":43,\"className\":\"jdk.internal.reflect.DelegatingMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"Method.java\",\"lineNumber\":568,\"className\":\"java.lang.reflect.Method\",\"methodName\":\"invoke\"},{\"fileName\":\"AopUtils.java\",\"lineNumber\":355,\"className\":\"org.springframework.aop.support.AopUtils\",\"methodName\":\"invokeJoinpointUsingReflection\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":196,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"invokeJoinpoint\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":163,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"AspectJAfterThrowingAdvice.java\",\"lineNumber\":64,\"className\":\"org.springframework.aop.aspectj.AspectJAfterThrowingAdvice\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":173,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed', '', 138, '*************', '中国 福建 厦门 电信', '2024-11-10 21:30:16');
INSERT INTO `sys_log` VALUES (74, 'admin', 1, 'SYS', '/sys/config/update', 0, '修改配置', 'com.bcy.system.api.sys.SysConfigController.update()', '{\"id\":1,\"name\":\"登录页展示\",\"paramsKey\":\"SYS_SETTING\",\"paramsValue\":\"{\\n\\t\\\"title\\\": \\\"博昌云科技\\\",\\n\\t\\\"version\\\": \\\"多租户\\\",\\n\\t\\\"isTenant\\\": 1,\\n\\t\\\"notice\\\": \\\"Copyright © 2023 - 2024 博昌云科技 All Rights Reserved.\\\",\\n\\t\\\"beiAnNumber\\\": \\\"\\\",\\n\\t\\\"number\\\": \\\"\\\",\\n\\t\\\"websocket\\\": \\\"wss://lxwthinker.tpddns.cn:8081/sys/websocket\\\",\\n\\t\\\"isCode\\\": 0,\\n}\",\"remark\":\"\",\"status\":0}', '{\"code\":200,\"data\":\"更新成功\"}', '', 464, '*************', '中国 福建 厦门 电信', '2024-11-12 21:32:35');
INSERT INTO `sys_log` VALUES (75, 'BCY', 1, 'SYS', '/sys/login/logout', 0, '用户注销', 'com.bcy.system.api.login.LoginController.logout()', '{}', '{\"code\":200,\"data\":\"退出成功\"}', '', 15, '*************', '中国 福建 厦门 电信', '2024-11-12 21:32:38');
INSERT INTO `sys_log` VALUES (76, 'admin', 1, 'SYS', '/sys/login/sys', 0, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"admin\",\"uuid\":\"\"}', '{\"code\":200,\"data\":{\"accessToken\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjEsInJuU3RyIjoiQVNFRjYyYTBJalpWb1FzWWl6bzFnNVdrUnE4dHF2cEgifQ.qaqravu4bdrBrx2b68yO59vVtBaVa1l4jgdT5nsDomc\",\"expireTime\":259200}}', '', 734, '*************', '中国 福建 厦门 电信', '2024-11-12 21:32:41');
INSERT INTO `sys_log` VALUES (77, 'admin', 1, 'SYS', '/sys/module/insert', 0, '新增模块服务', 'com.bcy.system.api.sys.SysModuleController.insert()', '{\"code\":\"bcy-sys\",\"name\":\"后台服务\",\"remark\":\"后台管理系统\",\"status\":0,\"url\":\"https://sys.bcycloud.com/\"}', '{\"code\":200,\"data\":\"新增成功\"}', '', 533, '*************', '中国 福建 厦门 电信', '2024-11-14 21:26:55');
INSERT INTO `sys_log` VALUES (78, 'admin', 1, 'SYS', '/sys/module/update', 0, '修改模块服务', 'com.bcy.system.api.sys.SysModuleController.update()', '{\"code\":\"bcy-sys\",\"id\":1,\"name\":\"后台服务\",\"remark\":\"博昌云后台管理系统\",\"status\":0,\"url\":\"https://sys.bcycloud.com/\"}', '{\"code\":200,\"data\":\"更新成功\"}', '', 286, '*************', '中国 福建 厦门 电信', '2024-11-14 21:27:09');
INSERT INTO `sys_log` VALUES (79, 'admin', 1, 'SYS', '/sys/module/updateStatus/1', 0, '启用或禁用模块服务', 'com.bcy.system.api.sys.SysModuleController.updateStatus()', '{}', '{\"code\":200,\"data\":\"更改成功\"}', '', 114, '*************', '中国 福建 厦门 电信', '2024-11-14 21:27:11');
INSERT INTO `sys_log` VALUES (80, 'admin', 1, 'SYS', '/sys/module/updateStatus/1', 0, '启用或禁用模块服务', 'com.bcy.system.api.sys.SysModuleController.updateStatus()', '{}', '{\"code\":200,\"data\":\"更改成功\"}', '', 269, '*************', '中国 福建 厦门 电信', '2024-11-14 21:27:22');
INSERT INTO `sys_log` VALUES (81, 'admin', 1, 'SYS', '/sys/tenant/insert', 0, '新增租户信息', 'com.bcy.system.api.sys.SysTenantController.insert()', '{\"appKey\":\"bcy-test\",\"name\":\"博昌云测试\",\"remark\":\"\"}', '{\"code\":200,\"data\":\"新增成功\"}', '', 152, '*************', '中国 福建 厦门 电信', '2024-11-14 21:28:31');
INSERT INTO `sys_log` VALUES (82, 'admin', 1, 'SYS', '/sys/login/sys', 0, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"admin\",\"uuid\":\"\"}', '{\"code\":200,\"data\":{\"accessToken\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjEsInJuU3RyIjoiWURRTGdSVDh0NWhLMnhzc1Vmbm84RnJwYVNvdmxHc0QifQ.-stnUTA5PxiT4ZsDPUdJ3Vv9mNFz-OR5tNQ50zJ7QuQ\",\"expireTime\":259200}}', '', 789, '*************', '中国 福建 厦门 电信', '2024-11-14 21:29:46');
INSERT INTO `sys_log` VALUES (83, 'admin', 1, 'SYS', '/sys/log/sys/clean', 0, '清理日志', 'com.bcy.system.api.log.SysLogController.clean()', '{\"day\":\"7\"}', '{\"code\":500,\"msg\":\"暂无数据\"}', '', 167, '*************', '中国 福建 厦门 电信', '2024-11-14 21:30:50');
INSERT INTO `sys_log` VALUES (84, 'admin', 1, 'SYS', '/sys/login/sys', 0, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"admin\",\"uuid\":\"\"}', '{\"code\":200,\"data\":{\"accessToken\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjEsInJuU3RyIjoiemQ3c251ZTJBbzR5b0VwVVJwaE1pcFZMS2ZTQ1ZMY3UifQ._fCid9IvwunCeddG8VTCkVA5wS9D1B80OlxLZs5cJiw\",\"expireTime\":259200}}', '', 945, '*************', '中国 福建 厦门 电信', '2024-11-14 22:43:30');
INSERT INTO `sys_log` VALUES (85, 'BCY', 1, 'SYS', '/sys/login/logout', 0, '用户注销', 'com.bcy.system.api.login.LoginController.logout()', '{}', '{\"code\":200,\"data\":\"退出成功\"}', '', 3, '*************', '中国 福建 厦门 电信', '2024-11-14 22:50:13');
INSERT INTO `sys_log` VALUES (86, 'admin', 1, 'SYS', '/sys/login/sys', 0, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"admin\",\"uuid\":\"\"}', '{\"code\":200,\"data\":{\"accessToken\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjEsInJuU3RyIjoiTTQyVFFmeDJSOVJ4cmU0cHl5UmRjdGdtaTZGZWdmekoifQ.GUBEuH3DWz2FImxwvBHSV0iky2bQ0UJxlhLYcJvwbGY\",\"expireTime\":259200}}', '', 276, '*************', '中国 福建 厦门 电信', '2024-11-14 22:50:15');
INSERT INTO `sys_log` VALUES (87, 'BCY', 1, 'SYS', '/sys/login/logout', 0, '用户注销', 'com.bcy.system.api.login.LoginController.logout()', '{}', '{\"code\":200,\"data\":\"退出成功\"}', '', 222, '127.0.0.1', '本机地址 本机地址  ', '2024-11-14 23:03:52');
INSERT INTO `sys_log` VALUES (88, 'bcy', 1, 'SYS', '/sys/login/sys', 0, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"bcy\",\"uuid\":\"\"}', '{\"code\":200,\"data\":{\"accessToken\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjIsInJuU3RyIjoiUTVLTk8xcGVrNDlJcGFidVpLb2VmV3FFRGpxMGhlR28ifQ.E4XEY1exdTnwcMoVZhWL3qWjaW3HwaM3HWyAoKiSfUM\",\"expireTime\":259200}}', '', 713, '127.0.0.1', '本机地址 本机地址  ', '2024-11-14 23:03:57');
INSERT INTO `sys_log` VALUES (89, 'BCY', 1, 'SYS', '/sys/login/logout', 0, '用户注销', 'com.bcy.system.api.login.LoginController.logout()', '{}', '{\"code\":200,\"data\":\"退出成功\"}', '', 89, '127.0.0.1', '本机地址 本机地址  ', '2024-11-14 23:04:03');
INSERT INTO `sys_log` VALUES (90, 'admin', 1, 'SYS', '/sys/login/sys', 0, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"admin\",\"uuid\":\"\"}', '{\"code\":200,\"data\":{\"accessToken\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjEsInJuU3RyIjoiTGxkaHkxbldQME9IcnpXVnM0dUgxVDdrbkhIOW1vUkEifQ.-rhE_HrtK5tyQ-XifGauIMV67Gh24MWBirjaEkMPdrQ\",\"expireTime\":259200}}', '', 462, '127.0.0.1', '本机地址 本机地址  ', '2024-11-14 23:04:06');
INSERT INTO `sys_log` VALUES (91, 'BCY', 1, 'SYS', '/sys/login/logout', 0, '用户注销', 'com.bcy.system.api.login.LoginController.logout()', '{}', '{\"code\":200,\"data\":\"退出成功\"}', '', 69, '127.0.0.1', '本机地址 本机地址  ', '2024-11-14 23:04:25');
INSERT INTO `sys_log` VALUES (92, 'bcy', 1, 'SYS', '/sys/login/sys', 0, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"bcy\",\"uuid\":\"\"}', '{\"code\":200,\"data\":{\"accessToken\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjIsInJuU3RyIjoiWXFJdVVYdFRTbjdaRkNWeFViOEhuMXlzbkVBMkFGTzkifQ.0_C94nm3Gz1nlKHaTB_xHnOoiyt5QhKl1Vj6sUUHPLk\",\"expireTime\":259200}}', '', 533, '127.0.0.1', '本机地址 本机地址  ', '2024-11-14 23:04:29');
INSERT INTO `sys_log` VALUES (93, 'BCY', 1, 'SYS', '/sys/login/logout', 0, '用户注销', 'com.bcy.system.api.login.LoginController.logout()', '{}', '{\"code\":200,\"data\":\"退出成功\"}', '', 278, '127.0.0.1', '本机地址 本机地址  ', '2024-11-14 23:19:18');
INSERT INTO `sys_log` VALUES (94, 'admin', 1, 'SYS', '/sys/login/sys', 0, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"admin\",\"uuid\":\"\"}', '{\"code\":200,\"data\":{\"accessToken\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjEsInJuU3RyIjoiVnVSNkhHUEVUNlY4dHpBUGFqMHpNaWFPTzNVY2xtUm8ifQ.hm3rrQajC0PmQj6jDrVGue53cnswC2vGzseyKfbL2u4\",\"expireTime\":259200}}', '', 685, '127.0.0.1', '本机地址 本机地址  ', '2024-11-14 23:19:20');
INSERT INTO `sys_log` VALUES (95, 'admin', 1, 'SYS', '/sys/login/sys', 0, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"admin\",\"uuid\":\"\"}', '{\"code\":200,\"data\":{\"accessToken\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjEsInJuU3RyIjoieDRydDRDOTk5QlQyd01jZENHVE85Mk91WWUxRkdBbjMifQ.PluQulSC0ndd4XedPRmTWqBgOQ9AR3mCZcPr5sPpNuw\",\"expireTime\":259200}}', '', 526, '127.0.0.1', '本机地址 本机地址  ', '2024-11-14 23:19:24');
INSERT INTO `sys_log` VALUES (96, 'BCY', 1, 'SYS', '/sys/login/logout', 0, '用户注销', 'com.bcy.system.api.login.LoginController.logout()', '{}', '{\"code\":200,\"data\":\"退出成功\"}', '', 81, '127.0.0.1', '本机地址 本机地址  ', '2024-11-14 23:19:39');
INSERT INTO `sys_log` VALUES (97, 'bcy', 1, 'SYS', '/sys/login/sys', 0, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"bcy\",\"uuid\":\"\"}', '{\"code\":200,\"data\":{\"accessToken\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjIsInJuU3RyIjoiR0dTRk9ZdFgyb3ZmbGpjSXhSZElLTTdMbmREQ2ZwUGoifQ.szxNXZE4pvNNdlpypsGm1MGccaDPiHc8CJfSPCTugGI\",\"expireTime\":259200}}', '', 534, '127.0.0.1', '本机地址 本机地址  ', '2024-11-14 23:19:43');
INSERT INTO `sys_log` VALUES (98, 'BCY', 1, 'SYS', '/sys/login/logout', 0, '用户注销', 'com.bcy.system.api.login.LoginController.logout()', '{}', '{\"code\":200,\"data\":\"退出成功\"}', '', 96, '127.0.0.1', '本机地址 本机地址  ', '2024-11-14 23:43:10');
INSERT INTO `sys_log` VALUES (99, 'admin', 1, 'SYS', '/sys/login/sys', 0, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"admin\",\"uuid\":\"\"}', '{\"code\":200,\"data\":{\"accessToken\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjEsInJuU3RyIjoiWkxHRExTM3lXZk5CdmM0eXFuT2Q3ZGtEbUdPMmNONGUifQ.izGr0CAkMzxlFK75jzyM0Y1UMsgpyd3Ss04aeQA1MGo\",\"expireTime\":259200}}', '', 554, '127.0.0.1', '本机地址 本机地址  ', '2024-11-14 23:43:12');
INSERT INTO `sys_log` VALUES (100, 'admin', 1, 'SYS', '/sys/login/sys', 0, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"admin\",\"uuid\":\"\"}', '{\"code\":200,\"data\":{\"accessToken\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjEsInJuU3RyIjoid0NwZGhmbWFzbGVwcklZM1djSk1lclhhdllRUTUyZGIifQ.P7C23B4RX-oNbaaac2HMJYDqh5xMbehdipB9j4qVAGM\",\"expireTime\":259200}}', '', 548, '127.0.0.1', '本机地址 本机地址  ', '2024-11-14 23:43:17');
INSERT INTO `sys_log` VALUES (101, 'BCY', 1, 'SYS', '/sys/login/sys', 1, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"admin\",\"uuid\":\"\"}', '{\"code\":500,\"errMsg\":\"用户或密码错误\",\"module\":\"BCY\",\"stackTrace\":[{\"fileName\":\"LoginService.java\",\"lineNumber\":101,\"className\":\"com.bcy.system.business.login.LoginService\",\"methodName\":\"login\"},{\"fileName\":\"LoginController.java\",\"lineNumber\":71,\"className\":\"com.bcy.system.api.login.LoginController\",\"methodName\":\"login\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":-2,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke0\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":77,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"DelegatingMethodAccessorImpl.java\",\"lineNumber\":43,\"className\":\"jdk.internal.reflect.DelegatingMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"Method.java\",\"lineNumber\":568,\"className\":\"java.lang.reflect.Method\",\"methodName\":\"invoke\"},{\"fileName\":\"AopUtils.java\",\"lineNumber\":355,\"className\":\"org.springframework.aop.support.AopUtils\",\"methodName\":\"invokeJoinpointUsingReflection\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":196,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"invokeJoinpoint\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":163,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"MethodBeforeAdviceInterceptor.java\",\"lineNumber\":58,\"className\":\"org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":173,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName', '', 273, '0:0:0:0:0:0:0:1', '', '2024-11-15 10:35:24');
INSERT INTO `sys_log` VALUES (102, 'BCY', 1, 'SYS', '/sys/login/sys', 1, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"admin\",\"uuid\":\"\"}', '{\"code\":500,\"errMsg\":\"用户或密码错误\",\"module\":\"BCY\",\"stackTrace\":[{\"fileName\":\"LoginService.java\",\"lineNumber\":101,\"className\":\"com.bcy.system.business.login.LoginService\",\"methodName\":\"login\"},{\"fileName\":\"LoginController.java\",\"lineNumber\":71,\"className\":\"com.bcy.system.api.login.LoginController\",\"methodName\":\"login\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":-2,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke0\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":77,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"DelegatingMethodAccessorImpl.java\",\"lineNumber\":43,\"className\":\"jdk.internal.reflect.DelegatingMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"Method.java\",\"lineNumber\":568,\"className\":\"java.lang.reflect.Method\",\"methodName\":\"invoke\"},{\"fileName\":\"AopUtils.java\",\"lineNumber\":355,\"className\":\"org.springframework.aop.support.AopUtils\",\"methodName\":\"invokeJoinpointUsingReflection\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":196,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"invokeJoinpoint\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":163,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"MethodBeforeAdviceInterceptor.java\",\"lineNumber\":58,\"className\":\"org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":173,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName', '', 37, '0:0:0:0:0:0:0:1', '', '2024-11-15 10:35:27');
INSERT INTO `sys_log` VALUES (103, 'BCY', 1, 'SYS', '/sys/login/sys', 1, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"admin\",\"uuid\":\"\"}', '{\"code\":500,\"errMsg\":\"用户或密码错误\",\"module\":\"BCY\",\"stackTrace\":[{\"fileName\":\"LoginService.java\",\"lineNumber\":101,\"className\":\"com.bcy.system.business.login.LoginService\",\"methodName\":\"login\"},{\"fileName\":\"LoginController.java\",\"lineNumber\":71,\"className\":\"com.bcy.system.api.login.LoginController\",\"methodName\":\"login\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":-2,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke0\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":77,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"DelegatingMethodAccessorImpl.java\",\"lineNumber\":43,\"className\":\"jdk.internal.reflect.DelegatingMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"Method.java\",\"lineNumber\":568,\"className\":\"java.lang.reflect.Method\",\"methodName\":\"invoke\"},{\"fileName\":\"AopUtils.java\",\"lineNumber\":355,\"className\":\"org.springframework.aop.support.AopUtils\",\"methodName\":\"invokeJoinpointUsingReflection\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":196,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"invokeJoinpoint\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":163,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"MethodBeforeAdviceInterceptor.java\",\"lineNumber\":58,\"className\":\"org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":173,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName', '', 35, '0:0:0:0:0:0:0:1', '', '2024-11-15 10:35:29');
INSERT INTO `sys_log` VALUES (104, 'BCY', 1, 'SYS', '/sys/login/sys', 1, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"admin\",\"uuid\":\"\"}', '{\"code\":500,\"errMsg\":\"用户或密码错误\",\"module\":\"BCY\",\"stackTrace\":[{\"fileName\":\"LoginService.java\",\"lineNumber\":101,\"className\":\"com.bcy.system.business.login.LoginService\",\"methodName\":\"login\"},{\"fileName\":\"LoginController.java\",\"lineNumber\":71,\"className\":\"com.bcy.system.api.login.LoginController\",\"methodName\":\"login\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":-2,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke0\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":77,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"DelegatingMethodAccessorImpl.java\",\"lineNumber\":43,\"className\":\"jdk.internal.reflect.DelegatingMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"Method.java\",\"lineNumber\":568,\"className\":\"java.lang.reflect.Method\",\"methodName\":\"invoke\"},{\"fileName\":\"AopUtils.java\",\"lineNumber\":355,\"className\":\"org.springframework.aop.support.AopUtils\",\"methodName\":\"invokeJoinpointUsingReflection\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":196,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"invokeJoinpoint\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":163,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"MethodBeforeAdviceInterceptor.java\",\"lineNumber\":58,\"className\":\"org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":173,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName', '', 34, '0:0:0:0:0:0:0:1', '', '2024-11-15 10:35:30');
INSERT INTO `sys_log` VALUES (105, 'BCY', 1, 'SYS', '/sys/login/sys', 1, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"admin\",\"uuid\":\"\"}', '{\"code\":500,\"errMsg\":\"用户或密码错误\",\"module\":\"BCY\",\"stackTrace\":[{\"fileName\":\"LoginService.java\",\"lineNumber\":101,\"className\":\"com.bcy.system.business.login.LoginService\",\"methodName\":\"login\"},{\"fileName\":\"LoginController.java\",\"lineNumber\":71,\"className\":\"com.bcy.system.api.login.LoginController\",\"methodName\":\"login\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":-2,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke0\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":77,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"DelegatingMethodAccessorImpl.java\",\"lineNumber\":43,\"className\":\"jdk.internal.reflect.DelegatingMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"Method.java\",\"lineNumber\":568,\"className\":\"java.lang.reflect.Method\",\"methodName\":\"invoke\"},{\"fileName\":\"AopUtils.java\",\"lineNumber\":355,\"className\":\"org.springframework.aop.support.AopUtils\",\"methodName\":\"invokeJoinpointUsingReflection\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":196,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"invokeJoinpoint\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":163,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"MethodBeforeAdviceInterceptor.java\",\"lineNumber\":58,\"className\":\"org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":173,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName', '', 34, '0:0:0:0:0:0:0:1', '', '2024-11-15 10:35:30');
INSERT INTO `sys_log` VALUES (106, 'BCY', 1, 'SYS', '/sys/login/sys', 1, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"admin\",\"uuid\":\"\"}', '{\"code\":500,\"errMsg\":\"用户或密码错误\",\"module\":\"BCY\",\"stackTrace\":[{\"fileName\":\"LoginService.java\",\"lineNumber\":101,\"className\":\"com.bcy.system.business.login.LoginService\",\"methodName\":\"login\"},{\"fileName\":\"LoginController.java\",\"lineNumber\":71,\"className\":\"com.bcy.system.api.login.LoginController\",\"methodName\":\"login\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":-2,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke0\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":77,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"DelegatingMethodAccessorImpl.java\",\"lineNumber\":43,\"className\":\"jdk.internal.reflect.DelegatingMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"Method.java\",\"lineNumber\":568,\"className\":\"java.lang.reflect.Method\",\"methodName\":\"invoke\"},{\"fileName\":\"AopUtils.java\",\"lineNumber\":355,\"className\":\"org.springframework.aop.support.AopUtils\",\"methodName\":\"invokeJoinpointUsingReflection\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":196,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"invokeJoinpoint\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":163,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"MethodBeforeAdviceInterceptor.java\",\"lineNumber\":58,\"className\":\"org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":173,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName', '', 34, '0:0:0:0:0:0:0:1', '', '2024-11-15 10:35:31');
INSERT INTO `sys_log` VALUES (107, 'BCY', 1, 'SYS', '/sys/login/sys', 1, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"admin\",\"uuid\":\"\"}', '{\"code\":500,\"errMsg\":\"用户或密码错误\",\"module\":\"BCY\",\"stackTrace\":[{\"fileName\":\"LoginService.java\",\"lineNumber\":101,\"className\":\"com.bcy.system.business.login.LoginService\",\"methodName\":\"login\"},{\"fileName\":\"LoginController.java\",\"lineNumber\":71,\"className\":\"com.bcy.system.api.login.LoginController\",\"methodName\":\"login\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":-2,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke0\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":77,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"DelegatingMethodAccessorImpl.java\",\"lineNumber\":43,\"className\":\"jdk.internal.reflect.DelegatingMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"Method.java\",\"lineNumber\":568,\"className\":\"java.lang.reflect.Method\",\"methodName\":\"invoke\"},{\"fileName\":\"AopUtils.java\",\"lineNumber\":355,\"className\":\"org.springframework.aop.support.AopUtils\",\"methodName\":\"invokeJoinpointUsingReflection\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":196,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"invokeJoinpoint\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":163,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"MethodBeforeAdviceInterceptor.java\",\"lineNumber\":58,\"className\":\"org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":173,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName', '', 34, '0:0:0:0:0:0:0:1', '', '2024-11-15 10:35:32');
INSERT INTO `sys_log` VALUES (108, 'BCY', 1, 'SYS', '/sys/login/sys', 1, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"admin\",\"uuid\":\"\"}', '{\"code\":500,\"errMsg\":\"用户或密码错误\",\"module\":\"BCY\",\"stackTrace\":[{\"fileName\":\"LoginService.java\",\"lineNumber\":101,\"className\":\"com.bcy.system.business.login.LoginService\",\"methodName\":\"login\"},{\"fileName\":\"LoginController.java\",\"lineNumber\":71,\"className\":\"com.bcy.system.api.login.LoginController\",\"methodName\":\"login\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":-2,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke0\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":77,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"DelegatingMethodAccessorImpl.java\",\"lineNumber\":43,\"className\":\"jdk.internal.reflect.DelegatingMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"Method.java\",\"lineNumber\":568,\"className\":\"java.lang.reflect.Method\",\"methodName\":\"invoke\"},{\"fileName\":\"AopUtils.java\",\"lineNumber\":355,\"className\":\"org.springframework.aop.support.AopUtils\",\"methodName\":\"invokeJoinpointUsingReflection\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":196,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"invokeJoinpoint\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":163,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"MethodBeforeAdviceInterceptor.java\",\"lineNumber\":58,\"className\":\"org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":173,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName', '', 33, '0:0:0:0:0:0:0:1', '', '2024-11-15 10:35:33');
INSERT INTO `sys_log` VALUES (109, 'BCY', 1, 'SYS', '/sys/login/sys', 1, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"admin\",\"uuid\":\"\"}', '{\"code\":500,\"errMsg\":\"用户或密码错误\",\"module\":\"BCY\",\"stackTrace\":[{\"fileName\":\"LoginService.java\",\"lineNumber\":101,\"className\":\"com.bcy.system.business.login.LoginService\",\"methodName\":\"login\"},{\"fileName\":\"LoginController.java\",\"lineNumber\":71,\"className\":\"com.bcy.system.api.login.LoginController\",\"methodName\":\"login\"},{\"lineNumber\":-1,\"className\":\"jdk.internal.reflect.GeneratedMethodAccessor186\",\"methodName\":\"invoke\"},{\"fileName\":\"DelegatingMethodAccessorImpl.java\",\"lineNumber\":43,\"className\":\"jdk.internal.reflect.DelegatingMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"Method.java\",\"lineNumber\":568,\"className\":\"java.lang.reflect.Method\",\"methodName\":\"invoke\"},{\"fileName\":\"AopUtils.java\",\"lineNumber\":355,\"className\":\"org.springframework.aop.support.AopUtils\",\"methodName\":\"invokeJoinpointUsingReflection\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":196,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"invokeJoinpoint\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":163,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"MethodBeforeAdviceInterceptor.java\",\"lineNumber\":58,\"className\":\"org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":173,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"AspectJAfterThrowingAdvice.java\",\"lineNumber\":64,\"className\":\"org.springframework.aop.aspectj.AspectJAfterThrowingAdvice\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocati', '', 33, '0:0:0:0:0:0:0:1', '', '2024-11-15 10:36:35');
INSERT INTO `sys_log` VALUES (110, 'BCY', 1, 'SYS', '/sys/login/sys', 1, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"admin\",\"uuid\":\"\"}', '{\"code\":500,\"errMsg\":\"用户或密码错误\",\"module\":\"BCY\",\"stackTrace\":[{\"fileName\":\"LoginService.java\",\"lineNumber\":101,\"className\":\"com.bcy.system.business.login.LoginService\",\"methodName\":\"login\"},{\"fileName\":\"LoginController.java\",\"lineNumber\":71,\"className\":\"com.bcy.system.api.login.LoginController\",\"methodName\":\"login\"},{\"lineNumber\":-1,\"className\":\"jdk.internal.reflect.GeneratedMethodAccessor186\",\"methodName\":\"invoke\"},{\"fileName\":\"DelegatingMethodAccessorImpl.java\",\"lineNumber\":43,\"className\":\"jdk.internal.reflect.DelegatingMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"Method.java\",\"lineNumber\":568,\"className\":\"java.lang.reflect.Method\",\"methodName\":\"invoke\"},{\"fileName\":\"AopUtils.java\",\"lineNumber\":355,\"className\":\"org.springframework.aop.support.AopUtils\",\"methodName\":\"invokeJoinpointUsingReflection\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":196,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"invokeJoinpoint\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":163,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"MethodBeforeAdviceInterceptor.java\",\"lineNumber\":58,\"className\":\"org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":173,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"AspectJAfterThrowingAdvice.java\",\"lineNumber\":64,\"className\":\"org.springframework.aop.aspectj.AspectJAfterThrowingAdvice\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocati', '', 32, '0:0:0:0:0:0:0:1', '', '2024-11-15 10:36:35');
INSERT INTO `sys_log` VALUES (111, 'BCY', 1, 'SYS', '/sys/login/sys', 1, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"admin\",\"uuid\":\"\"}', '{\"code\":500,\"errMsg\":\"账号已被锁定,请联系管理员\",\"module\":\"BCY\",\"stackTrace\":[{\"fileName\":\"LoginService.java\",\"lineNumber\":83,\"className\":\"com.bcy.system.business.login.LoginService\",\"methodName\":\"login\"},{\"fileName\":\"LoginController.java\",\"lineNumber\":71,\"className\":\"com.bcy.system.api.login.LoginController\",\"methodName\":\"login\"},{\"lineNumber\":-1,\"className\":\"jdk.internal.reflect.GeneratedMethodAccessor186\",\"methodName\":\"invoke\"},{\"fileName\":\"DelegatingMethodAccessorImpl.java\",\"lineNumber\":43,\"className\":\"jdk.internal.reflect.DelegatingMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"Method.java\",\"lineNumber\":568,\"className\":\"java.lang.reflect.Method\",\"methodName\":\"invoke\"},{\"fileName\":\"AopUtils.java\",\"lineNumber\":355,\"className\":\"org.springframework.aop.support.AopUtils\",\"methodName\":\"invokeJoinpointUsingReflection\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":196,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"invokeJoinpoint\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":163,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"MethodBeforeAdviceInterceptor.java\",\"lineNumber\":58,\"className\":\"org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":173,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"AspectJAfterThrowingAdvice.java\",\"lineNumber\":64,\"className\":\"org.springframework.aop.aspectj.AspectJAfterThrowingAdvice\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInv', '', 12, '0:0:0:0:0:0:0:1', '', '2024-11-15 10:36:37');
INSERT INTO `sys_log` VALUES (112, 'BCY', 1, 'SYS', '/sys/login/sys', 1, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"admin\",\"uuid\":\"\"}', '{\"code\":500,\"errMsg\":\"账号已被锁定,请联系管理员\",\"module\":\"BCY\",\"stackTrace\":[{\"fileName\":\"LoginService.java\",\"lineNumber\":78,\"className\":\"com.bcy.system.business.login.LoginService\",\"methodName\":\"login\"},{\"fileName\":\"LoginController.java\",\"lineNumber\":71,\"className\":\"com.bcy.system.api.login.LoginController\",\"methodName\":\"login\"},{\"lineNumber\":-1,\"className\":\"jdk.internal.reflect.GeneratedMethodAccessor186\",\"methodName\":\"invoke\"},{\"fileName\":\"DelegatingMethodAccessorImpl.java\",\"lineNumber\":43,\"className\":\"jdk.internal.reflect.DelegatingMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"Method.java\",\"lineNumber\":568,\"className\":\"java.lang.reflect.Method\",\"methodName\":\"invoke\"},{\"fileName\":\"AopUtils.java\",\"lineNumber\":355,\"className\":\"org.springframework.aop.support.AopUtils\",\"methodName\":\"invokeJoinpointUsingReflection\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":196,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"invokeJoinpoint\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":163,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"MethodBeforeAdviceInterceptor.java\",\"lineNumber\":58,\"className\":\"org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":173,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"AspectJAfterThrowingAdvice.java\",\"lineNumber\":64,\"className\":\"org.springframework.aop.aspectj.AspectJAfterThrowingAdvice\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInv', '', 8, '0:0:0:0:0:0:0:1', '', '2024-11-15 10:37:16');
INSERT INTO `sys_log` VALUES (113, 'admin', 1, 'SYS', '/sys/login/sys', 0, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"admin\",\"uuid\":\"\"}', '{\"code\":200,\"data\":{\"accessToken\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjEsInJuU3RyIjoienhXaWJpeWFpZkFjRE9IQkdRVERWcmNVcnVjTExicjYifQ.XSp6Dc8mM0mnEhOJSW8jOmCeazmrmiFw5Kj6hWO3Nt8\",\"expireTime\":259200}}', '', 2030, '0:0:0:0:0:0:0:1', '', '2024-11-15 10:39:06');
INSERT INTO `sys_log` VALUES (114, 'BCY', 1, 'SYS', '/sys/login/sys', 1, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"test\",\"uuid\":\"\"}', '{\"code\":500,\"errMsg\":\"用户或密码错误\",\"module\":\"BCY\",\"stackTrace\":[{\"fileName\":\"LoginService.java\",\"lineNumber\":96,\"className\":\"com.bcy.system.business.login.LoginService\",\"methodName\":\"login\"},{\"fileName\":\"LoginController.java\",\"lineNumber\":71,\"className\":\"com.bcy.system.api.login.LoginController\",\"methodName\":\"login\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":-2,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke0\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":77,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"DelegatingMethodAccessorImpl.java\",\"lineNumber\":43,\"className\":\"jdk.internal.reflect.DelegatingMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"Method.java\",\"lineNumber\":568,\"className\":\"java.lang.reflect.Method\",\"methodName\":\"invoke\"},{\"fileName\":\"AopUtils.java\",\"lineNumber\":355,\"className\":\"org.springframework.aop.support.AopUtils\",\"methodName\":\"invokeJoinpointUsingReflection\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":196,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"invokeJoinpoint\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":163,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"MethodBeforeAdviceInterceptor.java\",\"lineNumber\":58,\"className\":\"org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":173,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\"', '', 39, '0:0:0:0:0:0:0:1', '', '2024-11-15 10:39:19');
INSERT INTO `sys_log` VALUES (115, 'BCY', 1, 'SYS', '/sys/login/sys', 1, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"test\",\"uuid\":\"\"}', '{\"code\":500,\"errMsg\":\"用户或密码错误\",\"module\":\"BCY\",\"stackTrace\":[{\"fileName\":\"LoginService.java\",\"lineNumber\":96,\"className\":\"com.bcy.system.business.login.LoginService\",\"methodName\":\"login\"},{\"fileName\":\"LoginController.java\",\"lineNumber\":71,\"className\":\"com.bcy.system.api.login.LoginController\",\"methodName\":\"login\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":-2,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke0\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":77,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"DelegatingMethodAccessorImpl.java\",\"lineNumber\":43,\"className\":\"jdk.internal.reflect.DelegatingMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"Method.java\",\"lineNumber\":568,\"className\":\"java.lang.reflect.Method\",\"methodName\":\"invoke\"},{\"fileName\":\"AopUtils.java\",\"lineNumber\":355,\"className\":\"org.springframework.aop.support.AopUtils\",\"methodName\":\"invokeJoinpointUsingReflection\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":196,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"invokeJoinpoint\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":163,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"MethodBeforeAdviceInterceptor.java\",\"lineNumber\":58,\"className\":\"org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":173,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\"', '', 33, '0:0:0:0:0:0:0:1', '', '2024-11-15 10:39:20');
INSERT INTO `sys_log` VALUES (116, 'BCY', 1, 'SYS', '/sys/login/sys', 1, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"test\",\"uuid\":\"\"}', '{\"code\":500,\"errMsg\":\"用户或密码错误\",\"module\":\"BCY\",\"stackTrace\":[{\"fileName\":\"LoginService.java\",\"lineNumber\":96,\"className\":\"com.bcy.system.business.login.LoginService\",\"methodName\":\"login\"},{\"fileName\":\"LoginController.java\",\"lineNumber\":71,\"className\":\"com.bcy.system.api.login.LoginController\",\"methodName\":\"login\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":-2,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke0\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":77,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"DelegatingMethodAccessorImpl.java\",\"lineNumber\":43,\"className\":\"jdk.internal.reflect.DelegatingMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"Method.java\",\"lineNumber\":568,\"className\":\"java.lang.reflect.Method\",\"methodName\":\"invoke\"},{\"fileName\":\"AopUtils.java\",\"lineNumber\":355,\"className\":\"org.springframework.aop.support.AopUtils\",\"methodName\":\"invokeJoinpointUsingReflection\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":196,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"invokeJoinpoint\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":163,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"MethodBeforeAdviceInterceptor.java\",\"lineNumber\":58,\"className\":\"org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":173,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\"', '', 32, '0:0:0:0:0:0:0:1', '', '2024-11-15 10:39:20');
INSERT INTO `sys_log` VALUES (117, 'BCY', 1, 'SYS', '/sys/login/sys', 1, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"test\",\"uuid\":\"\"}', '{\"code\":500,\"errMsg\":\"用户或密码错误\",\"module\":\"BCY\",\"stackTrace\":[{\"fileName\":\"LoginService.java\",\"lineNumber\":96,\"className\":\"com.bcy.system.business.login.LoginService\",\"methodName\":\"login\"},{\"fileName\":\"LoginController.java\",\"lineNumber\":71,\"className\":\"com.bcy.system.api.login.LoginController\",\"methodName\":\"login\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":-2,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke0\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":77,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"DelegatingMethodAccessorImpl.java\",\"lineNumber\":43,\"className\":\"jdk.internal.reflect.DelegatingMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"Method.java\",\"lineNumber\":568,\"className\":\"java.lang.reflect.Method\",\"methodName\":\"invoke\"},{\"fileName\":\"AopUtils.java\",\"lineNumber\":355,\"className\":\"org.springframework.aop.support.AopUtils\",\"methodName\":\"invokeJoinpointUsingReflection\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":196,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"invokeJoinpoint\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":163,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"MethodBeforeAdviceInterceptor.java\",\"lineNumber\":58,\"className\":\"org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":173,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\"', '', 32, '0:0:0:0:0:0:0:1', '', '2024-11-15 10:39:21');
INSERT INTO `sys_log` VALUES (118, 'BCY', 1, 'SYS', '/sys/login/sys', 1, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"test\",\"uuid\":\"\"}', '{\"code\":430,\"errMsg\":\"请勿重复点击\",\"module\":\"BCY\",\"stackTrace\":[{\"fileName\":\"RepeatAspect.java\",\"lineNumber\":103,\"className\":\"com.bcy.aspect.RepeatAspect\",\"methodName\":\"noRepeat\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":-2,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke0\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":77,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"DelegatingMethodAccessorImpl.java\",\"lineNumber\":43,\"className\":\"jdk.internal.reflect.DelegatingMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"Method.java\",\"lineNumber\":568,\"className\":\"java.lang.reflect.Method\",\"methodName\":\"invoke\"},{\"fileName\":\"AbstractAspectJAdvice.java\",\"lineNumber\":637,\"className\":\"org.springframework.aop.aspectj.AbstractAspectJAdvice\",\"methodName\":\"invokeAdviceMethodWithGivenArgs\"},{\"fileName\":\"AbstractAspectJAdvice.java\",\"lineNumber\":620,\"className\":\"org.springframework.aop.aspectj.AbstractAspectJAdvice\",\"methodName\":\"invokeAdviceMethod\"},{\"fileName\":\"AspectJMethodBeforeAdvice.java\",\"lineNumber\":44,\"className\":\"org.springframework.aop.aspectj.AspectJMethodBeforeAdvice\",\"methodName\":\"before\"},{\"fileName\":\"MethodBeforeAdviceInterceptor.java\",\"lineNumber\":57,\"className\":\"org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":173,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"AspectJAfterThrowingAdvice.java\",\"lineNumber\":64,\"className\":\"org.springframework.aop.aspectj.AspectJAfterThrowingAdvice\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":173,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"', '', 3, '0:0:0:0:0:0:0:1', '', '2024-11-15 10:39:22');
INSERT INTO `sys_log` VALUES (119, 'BCY', 1, 'SYS', '/sys/login/sys', 1, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"test\",\"uuid\":\"\"}', '{\"code\":500,\"errMsg\":\"用户或密码错误\",\"module\":\"BCY\",\"stackTrace\":[{\"fileName\":\"LoginService.java\",\"lineNumber\":96,\"className\":\"com.bcy.system.business.login.LoginService\",\"methodName\":\"login\"},{\"fileName\":\"LoginController.java\",\"lineNumber\":71,\"className\":\"com.bcy.system.api.login.LoginController\",\"methodName\":\"login\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":-2,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke0\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":77,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"DelegatingMethodAccessorImpl.java\",\"lineNumber\":43,\"className\":\"jdk.internal.reflect.DelegatingMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"Method.java\",\"lineNumber\":568,\"className\":\"java.lang.reflect.Method\",\"methodName\":\"invoke\"},{\"fileName\":\"AopUtils.java\",\"lineNumber\":355,\"className\":\"org.springframework.aop.support.AopUtils\",\"methodName\":\"invokeJoinpointUsingReflection\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":196,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"invokeJoinpoint\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":163,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"MethodBeforeAdviceInterceptor.java\",\"lineNumber\":58,\"className\":\"org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":173,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\"', '', 32, '0:0:0:0:0:0:0:1', '', '2024-11-15 10:39:23');
INSERT INTO `sys_log` VALUES (120, 'BCY', 1, 'SYS', '/sys/login/sys', 1, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"test\",\"uuid\":\"\"}', '{\"code\":500,\"errMsg\":\"用户或密码错误\",\"module\":\"BCY\",\"stackTrace\":[{\"fileName\":\"LoginService.java\",\"lineNumber\":96,\"className\":\"com.bcy.system.business.login.LoginService\",\"methodName\":\"login\"},{\"fileName\":\"LoginController.java\",\"lineNumber\":71,\"className\":\"com.bcy.system.api.login.LoginController\",\"methodName\":\"login\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":-2,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke0\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":77,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"DelegatingMethodAccessorImpl.java\",\"lineNumber\":43,\"className\":\"jdk.internal.reflect.DelegatingMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"Method.java\",\"lineNumber\":568,\"className\":\"java.lang.reflect.Method\",\"methodName\":\"invoke\"},{\"fileName\":\"AopUtils.java\",\"lineNumber\":355,\"className\":\"org.springframework.aop.support.AopUtils\",\"methodName\":\"invokeJoinpointUsingReflection\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":196,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"invokeJoinpoint\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":163,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"MethodBeforeAdviceInterceptor.java\",\"lineNumber\":58,\"className\":\"org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":173,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\"', '', 30, '0:0:0:0:0:0:0:1', '', '2024-11-15 10:39:24');
INSERT INTO `sys_log` VALUES (121, 'BCY', 1, 'SYS', '/sys/login/sys', 1, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"test\",\"uuid\":\"\"}', '{\"code\":500,\"errMsg\":\"用户或密码错误\",\"module\":\"BCY\",\"stackTrace\":[{\"fileName\":\"LoginService.java\",\"lineNumber\":96,\"className\":\"com.bcy.system.business.login.LoginService\",\"methodName\":\"login\"},{\"fileName\":\"LoginController.java\",\"lineNumber\":71,\"className\":\"com.bcy.system.api.login.LoginController\",\"methodName\":\"login\"},{\"lineNumber\":-1,\"className\":\"jdk.internal.reflect.GeneratedMethodAccessor87\",\"methodName\":\"invoke\"},{\"fileName\":\"DelegatingMethodAccessorImpl.java\",\"lineNumber\":43,\"className\":\"jdk.internal.reflect.DelegatingMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"Method.java\",\"lineNumber\":568,\"className\":\"java.lang.reflect.Method\",\"methodName\":\"invoke\"},{\"fileName\":\"AopUtils.java\",\"lineNumber\":355,\"className\":\"org.springframework.aop.support.AopUtils\",\"methodName\":\"invokeJoinpointUsingReflection\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":196,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"invokeJoinpoint\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":163,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"MethodBeforeAdviceInterceptor.java\",\"lineNumber\":58,\"className\":\"org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":173,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"AspectJAfterThrowingAdvice.java\",\"lineNumber\":64,\"className\":\"org.springframework.aop.aspectj.AspectJAfterThrowingAdvice\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocation', '', 30, '0:0:0:0:0:0:0:1', '', '2024-11-15 10:39:25');
INSERT INTO `sys_log` VALUES (122, 'BCY', 1, 'SYS', '/sys/login/sys', 1, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"test\",\"uuid\":\"\"}', '{\"code\":500,\"errMsg\":\"用户或密码错误\",\"module\":\"BCY\",\"stackTrace\":[{\"fileName\":\"LoginService.java\",\"lineNumber\":96,\"className\":\"com.bcy.system.business.login.LoginService\",\"methodName\":\"login\"},{\"fileName\":\"LoginController.java\",\"lineNumber\":71,\"className\":\"com.bcy.system.api.login.LoginController\",\"methodName\":\"login\"},{\"lineNumber\":-1,\"className\":\"jdk.internal.reflect.GeneratedMethodAccessor87\",\"methodName\":\"invoke\"},{\"fileName\":\"DelegatingMethodAccessorImpl.java\",\"lineNumber\":43,\"className\":\"jdk.internal.reflect.DelegatingMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"Method.java\",\"lineNumber\":568,\"className\":\"java.lang.reflect.Method\",\"methodName\":\"invoke\"},{\"fileName\":\"AopUtils.java\",\"lineNumber\":355,\"className\":\"org.springframework.aop.support.AopUtils\",\"methodName\":\"invokeJoinpointUsingReflection\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":196,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"invokeJoinpoint\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":163,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"MethodBeforeAdviceInterceptor.java\",\"lineNumber\":58,\"className\":\"org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":173,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"AspectJAfterThrowingAdvice.java\",\"lineNumber\":64,\"className\":\"org.springframework.aop.aspectj.AspectJAfterThrowingAdvice\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocation', '', 31, '0:0:0:0:0:0:0:1', '', '2024-11-15 10:39:25');
INSERT INTO `sys_log` VALUES (123, 'BCY', 1, 'SYS', '/sys/login/sys', 1, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"test\",\"uuid\":\"\"}', '{\"code\":500,\"errMsg\":\"用户或密码错误\",\"module\":\"BCY\",\"stackTrace\":[{\"fileName\":\"LoginService.java\",\"lineNumber\":96,\"className\":\"com.bcy.system.business.login.LoginService\",\"methodName\":\"login\"},{\"fileName\":\"LoginController.java\",\"lineNumber\":71,\"className\":\"com.bcy.system.api.login.LoginController\",\"methodName\":\"login\"},{\"lineNumber\":-1,\"className\":\"jdk.internal.reflect.GeneratedMethodAccessor87\",\"methodName\":\"invoke\"},{\"fileName\":\"DelegatingMethodAccessorImpl.java\",\"lineNumber\":43,\"className\":\"jdk.internal.reflect.DelegatingMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"Method.java\",\"lineNumber\":568,\"className\":\"java.lang.reflect.Method\",\"methodName\":\"invoke\"},{\"fileName\":\"AopUtils.java\",\"lineNumber\":355,\"className\":\"org.springframework.aop.support.AopUtils\",\"methodName\":\"invokeJoinpointUsingReflection\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":196,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"invokeJoinpoint\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":163,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"MethodBeforeAdviceInterceptor.java\",\"lineNumber\":58,\"className\":\"org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":173,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"AspectJAfterThrowingAdvice.java\",\"lineNumber\":64,\"className\":\"org.springframework.aop.aspectj.AspectJAfterThrowingAdvice\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocation', '', 29, '0:0:0:0:0:0:0:1', '', '2024-11-15 10:39:26');
INSERT INTO `sys_log` VALUES (124, 'BCY', 1, 'SYS', '/sys/login/sys', 1, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"test\",\"uuid\":\"\"}', '{\"code\":500,\"errMsg\":\"用户或密码错误\",\"module\":\"BCY\",\"stackTrace\":[{\"fileName\":\"LoginService.java\",\"lineNumber\":96,\"className\":\"com.bcy.system.business.login.LoginService\",\"methodName\":\"login\"},{\"fileName\":\"LoginController.java\",\"lineNumber\":71,\"className\":\"com.bcy.system.api.login.LoginController\",\"methodName\":\"login\"},{\"lineNumber\":-1,\"className\":\"jdk.internal.reflect.GeneratedMethodAccessor87\",\"methodName\":\"invoke\"},{\"fileName\":\"DelegatingMethodAccessorImpl.java\",\"lineNumber\":43,\"className\":\"jdk.internal.reflect.DelegatingMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"Method.java\",\"lineNumber\":568,\"className\":\"java.lang.reflect.Method\",\"methodName\":\"invoke\"},{\"fileName\":\"AopUtils.java\",\"lineNumber\":355,\"className\":\"org.springframework.aop.support.AopUtils\",\"methodName\":\"invokeJoinpointUsingReflection\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":196,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"invokeJoinpoint\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":163,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"MethodBeforeAdviceInterceptor.java\",\"lineNumber\":58,\"className\":\"org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":173,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"AspectJAfterThrowingAdvice.java\",\"lineNumber\":64,\"className\":\"org.springframework.aop.aspectj.AspectJAfterThrowingAdvice\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocation', '', 30, '0:0:0:0:0:0:0:1', '', '2024-11-15 10:39:27');
INSERT INTO `sys_log` VALUES (125, 'BCY', 1, 'SYS', '/sys/login/sys', 1, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"test\",\"uuid\":\"\"}', '{\"code\":500,\"errMsg\":\"账号已被锁定,请联系管理员\",\"module\":\"BCY\",\"stackTrace\":[{\"fileName\":\"LoginService.java\",\"lineNumber\":78,\"className\":\"com.bcy.system.business.login.LoginService\",\"methodName\":\"login\"},{\"fileName\":\"LoginController.java\",\"lineNumber\":71,\"className\":\"com.bcy.system.api.login.LoginController\",\"methodName\":\"login\"},{\"lineNumber\":-1,\"className\":\"jdk.internal.reflect.GeneratedMethodAccessor87\",\"methodName\":\"invoke\"},{\"fileName\":\"DelegatingMethodAccessorImpl.java\",\"lineNumber\":43,\"className\":\"jdk.internal.reflect.DelegatingMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"Method.java\",\"lineNumber\":568,\"className\":\"java.lang.reflect.Method\",\"methodName\":\"invoke\"},{\"fileName\":\"AopUtils.java\",\"lineNumber\":355,\"className\":\"org.springframework.aop.support.AopUtils\",\"methodName\":\"invokeJoinpointUsingReflection\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":196,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"invokeJoinpoint\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":163,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"MethodBeforeAdviceInterceptor.java\",\"lineNumber\":58,\"className\":\"org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":173,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"AspectJAfterThrowingAdvice.java\",\"lineNumber\":64,\"className\":\"org.springframework.aop.aspectj.AspectJAfterThrowingAdvice\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvo', '', 7, '0:0:0:0:0:0:0:1', '', '2024-11-15 10:39:28');
INSERT INTO `sys_log` VALUES (126, 'BCY', 1, 'SYS', '/sys/login/sys', 1, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"test\",\"uuid\":\"\"}', '{\"code\":430,\"errMsg\":\"请勿重复点击\",\"module\":\"BCY\",\"stackTrace\":[{\"fileName\":\"RepeatAspect.java\",\"lineNumber\":103,\"className\":\"com.bcy.aspect.RepeatAspect\",\"methodName\":\"noRepeat\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":-2,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke0\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":77,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"DelegatingMethodAccessorImpl.java\",\"lineNumber\":43,\"className\":\"jdk.internal.reflect.DelegatingMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"Method.java\",\"lineNumber\":568,\"className\":\"java.lang.reflect.Method\",\"methodName\":\"invoke\"},{\"fileName\":\"AbstractAspectJAdvice.java\",\"lineNumber\":637,\"className\":\"org.springframework.aop.aspectj.AbstractAspectJAdvice\",\"methodName\":\"invokeAdviceMethodWithGivenArgs\"},{\"fileName\":\"AbstractAspectJAdvice.java\",\"lineNumber\":620,\"className\":\"org.springframework.aop.aspectj.AbstractAspectJAdvice\",\"methodName\":\"invokeAdviceMethod\"},{\"fileName\":\"AspectJMethodBeforeAdvice.java\",\"lineNumber\":44,\"className\":\"org.springframework.aop.aspectj.AspectJMethodBeforeAdvice\",\"methodName\":\"before\"},{\"fileName\":\"MethodBeforeAdviceInterceptor.java\",\"lineNumber\":57,\"className\":\"org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":173,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"AspectJAfterThrowingAdvice.java\",\"lineNumber\":64,\"className\":\"org.springframework.aop.aspectj.AspectJAfterThrowingAdvice\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":173,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"', '', 4, '0:0:0:0:0:0:0:1', '', '2024-11-15 10:39:29');
INSERT INTO `sys_log` VALUES (127, 'BCY', 1, 'SYS', '/sys/login/sys', 1, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"test\",\"uuid\":\"\"}', '{\"code\":500,\"errMsg\":\"账号已被锁定,请联系管理员\",\"module\":\"BCY\",\"stackTrace\":[{\"fileName\":\"LoginService.java\",\"lineNumber\":78,\"className\":\"com.bcy.system.business.login.LoginService\",\"methodName\":\"login\"},{\"fileName\":\"LoginController.java\",\"lineNumber\":71,\"className\":\"com.bcy.system.api.login.LoginController\",\"methodName\":\"login\"},{\"lineNumber\":-1,\"className\":\"jdk.internal.reflect.GeneratedMethodAccessor87\",\"methodName\":\"invoke\"},{\"fileName\":\"DelegatingMethodAccessorImpl.java\",\"lineNumber\":43,\"className\":\"jdk.internal.reflect.DelegatingMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"Method.java\",\"lineNumber\":568,\"className\":\"java.lang.reflect.Method\",\"methodName\":\"invoke\"},{\"fileName\":\"AopUtils.java\",\"lineNumber\":355,\"className\":\"org.springframework.aop.support.AopUtils\",\"methodName\":\"invokeJoinpointUsingReflection\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":196,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"invokeJoinpoint\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":163,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"MethodBeforeAdviceInterceptor.java\",\"lineNumber\":58,\"className\":\"org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":173,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"AspectJAfterThrowingAdvice.java\",\"lineNumber\":64,\"className\":\"org.springframework.aop.aspectj.AspectJAfterThrowingAdvice\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvo', '', 8, '0:0:0:0:0:0:0:1', '', '2024-11-15 10:39:30');
INSERT INTO `sys_log` VALUES (128, 'admin', 1, 'SYS', '/sys/login/sys', 0, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"admin\",\"uuid\":\"\"}', '{\"code\":200,\"data\":{\"accessToken\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjEsInJuU3RyIjoiZnFSTzdMWG5wZTZPaks3YzJzb1NWMWVBaWd0bmlPeGwifQ.y-xzgCOcfhK5DSSGFBBLAXfv0Ac7EFFVvmCOdQ38gzs\",\"expireTime\":259200}}', '', 840, '110.81.123.67', '中国 福建 泉州 电信', '2024-11-18 11:41:54');
INSERT INTO `sys_log` VALUES (129, 'BCY', 1, 'SYS', '/sys/login/logout', 0, '用户注销', 'com.bcy.system.api.login.LoginController.logout()', '{}', '{\"code\":200,\"data\":\"退出成功\"}', '', 156, '110.81.123.230', '中国 福建 泉州 电信', '2024-11-19 10:08:05');
INSERT INTO `sys_log` VALUES (130, 'BCY', 1, 'SYS', '/sys/login/sys', 1, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"admin\",\"uuid\":\"\"}', '{\"code\":500,\"errMsg\":\"用户密码错误\",\"module\":\"BCY\",\"stackTrace\":[{\"fileName\":\"LoginService.java\",\"lineNumber\":88,\"className\":\"com.bcy.system.business.login.LoginService\",\"methodName\":\"login\"},{\"fileName\":\"LoginController.java\",\"lineNumber\":75,\"className\":\"com.bcy.system.api.login.LoginController\",\"methodName\":\"login\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":-2,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke0\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":77,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"DelegatingMethodAccessorImpl.java\",\"lineNumber\":43,\"className\":\"jdk.internal.reflect.DelegatingMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"Method.java\",\"lineNumber\":568,\"className\":\"java.lang.reflect.Method\",\"methodName\":\"invoke\"},{\"fileName\":\"AopUtils.java\",\"lineNumber\":355,\"className\":\"org.springframework.aop.support.AopUtils\",\"methodName\":\"invokeJoinpointUsingReflection\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":196,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"invokeJoinpoint\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":163,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"MethodBeforeAdviceInterceptor.java\",\"lineNumber\":58,\"className\":\"org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":173,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":', '', 42, '110.81.123.230', '中国 福建 泉州 电信', '2024-11-19 10:08:07');
INSERT INTO `sys_log` VALUES (131, 'admin', 1, 'SYS', '/sys/login/sys', 0, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"admin\",\"uuid\":\"bcy1731982087327\"}', '{\"code\":200,\"data\":{\"accessToken\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjEsInJuU3RyIjoiVklzaFVoNjF5aUFZNmN4NjhpOGV1dXhvVFoxc3NmbnUifQ.6o38km-wAGtKJr9YGUfWKvNC8LYez6yxrWkC678mqYQ\",\"expireTime\":259200}}', '', 823, '110.81.123.230', '中国 福建 泉州 电信', '2024-11-19 10:08:11');
INSERT INTO `sys_log` VALUES (132, 'BCY', 1, 'SYS', '/sys/login/logout', 0, '用户注销', 'com.bcy.system.api.login.LoginController.logout()', '{}', '{\"code\":200,\"data\":\"退出成功\"}', '', 4, '110.81.123.230', '中国 福建 泉州 电信', '2024-11-19 10:08:29');
INSERT INTO `sys_log` VALUES (133, 'BCY', 1, 'SYS', '/sys/login/sys', 1, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"admin\",\"uuid\":\"\"}', '{\"code\":500,\"errMsg\":\"用户密码错误\",\"module\":\"BCY\",\"stackTrace\":[{\"fileName\":\"LoginService.java\",\"lineNumber\":88,\"className\":\"com.bcy.system.business.login.LoginService\",\"methodName\":\"login\"},{\"fileName\":\"LoginController.java\",\"lineNumber\":75,\"className\":\"com.bcy.system.api.login.LoginController\",\"methodName\":\"login\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":-2,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke0\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":77,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"DelegatingMethodAccessorImpl.java\",\"lineNumber\":43,\"className\":\"jdk.internal.reflect.DelegatingMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"Method.java\",\"lineNumber\":568,\"className\":\"java.lang.reflect.Method\",\"methodName\":\"invoke\"},{\"fileName\":\"AopUtils.java\",\"lineNumber\":355,\"className\":\"org.springframework.aop.support.AopUtils\",\"methodName\":\"invokeJoinpointUsingReflection\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":196,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"invokeJoinpoint\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":163,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"MethodBeforeAdviceInterceptor.java\",\"lineNumber\":58,\"className\":\"org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":173,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":', '', 8, '110.81.123.230', '中国 福建 泉州 电信', '2024-11-19 10:08:30');
INSERT INTO `sys_log` VALUES (134, 'BCY', 1, 'SYS', '/sys/login/sys', 1, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"admin\",\"uuid\":\"bcy1731982110043\"}', '{\"code\":500,\"errMsg\":\"用户密码错误\",\"module\":\"BCY\",\"stackTrace\":[{\"fileName\":\"LoginService.java\",\"lineNumber\":88,\"className\":\"com.bcy.system.business.login.LoginService\",\"methodName\":\"login\"},{\"fileName\":\"LoginController.java\",\"lineNumber\":75,\"className\":\"com.bcy.system.api.login.LoginController\",\"methodName\":\"login\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":-2,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke0\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":77,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"DelegatingMethodAccessorImpl.java\",\"lineNumber\":43,\"className\":\"jdk.internal.reflect.DelegatingMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"Method.java\",\"lineNumber\":568,\"className\":\"java.lang.reflect.Method\",\"methodName\":\"invoke\"},{\"fileName\":\"AopUtils.java\",\"lineNumber\":355,\"className\":\"org.springframework.aop.support.AopUtils\",\"methodName\":\"invokeJoinpointUsingReflection\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":196,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"invokeJoinpoint\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":163,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"MethodBeforeAdviceInterceptor.java\",\"lineNumber\":58,\"className\":\"org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":173,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":', '', 7, '110.81.123.230', '中国 福建 泉州 电信', '2024-11-19 10:08:33');
INSERT INTO `sys_log` VALUES (135, 'admin', 1, 'SYS', '/sys/login/sys', 0, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"admin\",\"uuid\":\"bcy1731982113567\"}', '{\"code\":200,\"data\":{\"accessToken\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjEsInJuU3RyIjoiMDNMeTc3R3EzWGx4M3JsRllhNkRBTTZUQ2hiWWtUUEQifQ.Rrw52y0wdRtoFsOtfllNqkVr-LVVxg--Sww4y_tVViQ\",\"expireTime\":259200}}', '', 134, '110.81.123.230', '中国 福建 泉州 电信', '2024-11-19 10:08:38');
INSERT INTO `sys_log` VALUES (136, 'admin', 1, 'SYS', '/sys/login/sys', 0, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"admin\",\"uuid\":\"\"}', '{\"code\":200,\"data\":{\"accessToken\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjEsInJuU3RyIjoiZjE1d2tUZFBhaVFtQlBuMWpnWmtuY3hrU0hCMVl6cUIifQ.mJIn38bYVtMRV5Ib0T13CsiFaMXkJrq6jn5D_c04t6E\",\"expireTime\":259200}}', '', 2083, '112.48.52.178', '中国 福建 厦门 移动', '2024-11-21 22:59:35');
INSERT INTO `sys_log` VALUES (137, 'BCY', 1, 'SYS', '/sys/login/logout', 0, '用户注销', 'com.bcy.system.api.login.LoginController.logout()', '{}', '{\"code\":200,\"data\":\"退出成功\"}', '', 7, '112.48.52.178', '中国 福建 厦门 移动', '2024-11-21 23:17:48');
INSERT INTO `sys_log` VALUES (138, 'admin', 1, 'SYS', '/sys/login/sys', 0, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"admin\",\"uuid\":\"\"}', '{\"code\":200,\"data\":{\"accessToken\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjEsInJuU3RyIjoiZFUyT3dsOHhMMHlIWmRrWXd5SlpQRkxiYmwyMENGQVgifQ.XibH46ztblcxdIBwb66MjP7jZI3UJAG29WzbrNgwB7Y\",\"expireTime\":259200}}', '', 38, '112.48.52.178', '中国 福建 厦门 移动', '2024-11-21 23:17:50');
INSERT INTO `sys_log` VALUES (139, 'BCY', 1, 'SYS', '/sys/login/sys', 1, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"admin\",\"uuid\":\"\"}', '{\"code\":500,\"errMsg\":\"用户密码错误\",\"module\":\"BCY\",\"stackTrace\":[{\"fileName\":\"LoginService.java\",\"lineNumber\":88,\"className\":\"com.bcy.system.business.login.LoginService\",\"methodName\":\"login\"},{\"fileName\":\"LoginController.java\",\"lineNumber\":75,\"className\":\"com.bcy.system.api.login.LoginController\",\"methodName\":\"login\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":-2,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke0\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":77,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"DelegatingMethodAccessorImpl.java\",\"lineNumber\":43,\"className\":\"jdk.internal.reflect.DelegatingMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"Method.java\",\"lineNumber\":568,\"className\":\"java.lang.reflect.Method\",\"methodName\":\"invoke\"},{\"fileName\":\"AopUtils.java\",\"lineNumber\":355,\"className\":\"org.springframework.aop.support.AopUtils\",\"methodName\":\"invokeJoinpointUsingReflection\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":196,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"invokeJoinpoint\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":163,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"MethodBeforeAdviceInterceptor.java\",\"lineNumber\":58,\"className\":\"org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":173,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":', '', 297, '110.81.121.214', '中国 福建 泉州 电信', '2024-11-21 23:50:16');
INSERT INTO `sys_log` VALUES (140, 'admin', 1, 'SYS', '/sys/login/sys', 0, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"\",\"userName\":\"admin\",\"uuid\":\"bcy1732204219977\"}', '{\"code\":200,\"data\":{\"accessToken\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjEsInJuU3RyIjoiSGNxZnVpTXR6U2lGU01ERVA4WkdtOHloWERDOVY3Nm0ifQ.WNIYpe2yZoB6TS42yFsw8JYBn-Ig12Fny1GbyBfa_F0\",\"expireTime\":259200}}', '', 1750, '110.81.121.214', '中国 福建 泉州 电信', '2024-11-21 23:50:27');
INSERT INTO `sys_log` VALUES (141, 'admin', 1, 'SYS', '/sys/config/update', 0, '修改配置', 'com.bcy.system.api.sys.SysConfigController.update()', '{\"id\":1,\"name\":\"登录页展示\",\"paramsKey\":\"SYS_SETTING\",\"paramsValue\":\"{\\n\\t\\\"title\\\": \\\"博昌云科技\\\",\\n\\t\\\"version\\\": \\\"多租户\\\",\\n\\t\\\"isTenant\\\": 1,\\n\\t\\\"notice\\\": \\\"Copyright © 2023 - 2024 博昌云科技 All Rights Reserved.\\\",\\n\\t\\\"beiAnNumber\\\": \\\"\\\",\\n\\t\\\"number\\\": \\\"\\\",\\n\\t\\\"websocket\\\": \\\"wss://api.bcycloud.com/sys/websocket\\\",\\n\\t\\\"isCode\\\": 1,\\n}\",\"remark\":\"\",\"status\":0}', '{\"code\":200,\"data\":\"更新成功\"}', '', 28, '110.81.121.214', '中国 福建 泉州 电信', '2024-11-22 10:43:45');
INSERT INTO `sys_log` VALUES (142, 'BCY', 1, 'SYS', '/sys/login/logout', 0, '用户注销', 'com.bcy.system.api.login.LoginController.logout()', '{}', '{\"code\":200,\"data\":\"退出成功\"}', '', 5, '110.81.121.214', '中国 福建 泉州 电信', '2024-11-22 10:43:48');
INSERT INTO `sys_log` VALUES (143, 'BCY', 1, 'SYS', '/sys/login/sys', 1, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"5y7dx\",\"userName\":\"admin\",\"uuid\":\"bcy1732243428852\"}', '{\"code\":500,\"errMsg\":\"用户密码错误\",\"module\":\"BCY\",\"stackTrace\":[{\"fileName\":\"LoginService.java\",\"lineNumber\":88,\"className\":\"com.bcy.system.business.login.LoginService\",\"methodName\":\"login\"},{\"fileName\":\"LoginController.java\",\"lineNumber\":75,\"className\":\"com.bcy.system.api.login.LoginController\",\"methodName\":\"login\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":-2,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke0\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":77,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"DelegatingMethodAccessorImpl.java\",\"lineNumber\":43,\"className\":\"jdk.internal.reflect.DelegatingMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"Method.java\",\"lineNumber\":568,\"className\":\"java.lang.reflect.Method\",\"methodName\":\"invoke\"},{\"fileName\":\"AopUtils.java\",\"lineNumber\":355,\"className\":\"org.springframework.aop.support.AopUtils\",\"methodName\":\"invokeJoinpointUsingReflection\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":196,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"invokeJoinpoint\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":163,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"MethodBeforeAdviceInterceptor.java\",\"lineNumber\":58,\"className\":\"org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":173,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":', '', 8, '110.81.121.214', '中国 福建 泉州 电信', '2024-11-22 10:43:58');
INSERT INTO `sys_log` VALUES (144, 'admin', 1, 'SYS', '/sys/login/sys', 0, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"bxnxm\",\"userName\":\"admin\",\"uuid\":\"bcy1732243438065\"}', '{\"code\":200,\"data\":{\"accessToken\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjEsInJuU3RyIjoiV2RETlBnbGlFWTlDY1RWRU5POGd4ZFE0T3BCaDJYYUYifQ.ve-iFsd2Ks4wrpuEOLhEszkwAoDF8XY4miF4RU_Pcdw\",\"expireTime\":259200}}', '', 29, '110.81.121.214', '中国 福建 泉州 电信', '2024-11-22 10:44:06');
INSERT INTO `sys_log` VALUES (145, 'BCY', 1, 'SYS', '/sys/login/logout', 0, '用户注销', 'com.bcy.system.api.login.LoginController.logout()', '{}', '{\"code\":200,\"data\":\"退出成功\"}', '', 3, '110.81.121.214', '中国 福建 泉州 电信', '2024-11-22 10:50:23');
INSERT INTO `sys_log` VALUES (146, 'admin', 1, 'SYS', '/sys/login/sys', 0, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"ygnd3\",\"userName\":\"admin\",\"uuid\":\"bcy1732243823799\"}', '{\"code\":200,\"data\":{\"accessToken\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjEsInJuU3RyIjoiZEpUYm84Vktha3p5S0lqTmlvVkh1T3ZzT24yQUtEcVMifQ.cbFBdehAyJrYsBKJAMgbMvhC_lUqmyTSg0EMbqwxnSk\",\"expireTime\":259200}}', '', 25, '110.81.121.214', '中国 福建 泉州 电信', '2024-11-22 10:50:31');
INSERT INTO `sys_log` VALUES (147, 'admin', 1, 'SYS', '/sys/login/sys', 0, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"wy48n\",\"userName\":\"admin\",\"uuid\":\"bcy1732244348053\"}', '{\"code\":200,\"data\":{\"accessToken\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjEsInJuU3RyIjoiUjNZTXNvV3NpemhQVzdpRWhWUWJia0R1YTR1S2FpVEYifQ.QT7tDe4hn3iN-rc1ylUMQp8L5BXT5Ibnq4dpw6BYFh4\",\"expireTime\":259200}}', '', 25, '110.81.121.214', '中国 福建 泉州 电信', '2024-11-22 10:59:18');
INSERT INTO `sys_log` VALUES (148, 'BCY', 1, 'bcy-admin', '/sys/api/web/seekAdvice', 1, '发送简单邮件信息', 'com.bcy.system.spi.BcyAdminSpiImpl.sendMail()', '{\"content\":\"用户：[13205016587]已提交号码，请及时回访！\",\"from\":\"博昌云科技<<EMAIL>>\",\"subject\":\"领取通知\",\"to\":\"<EMAIL>\"}', '{\"cause\":{\"localizedMessage\":\"530 Login fail. A secure connection is requiered(such as ssl). More information at https://help.mail.qq.com/detail/0/1010\\n\",\"message\":\"530 Login fail. A secure connection is requiered(such as ssl). More information at https://help.mail.qq.com/detail/0/1010\\n\",\"stackTrace\":[{\"fileName\":\"SMTPTransport.java\",\"lineNumber\":954,\"className\":\"org.eclipse.angus.mail.smtp.SMTPTransport$Authenticator\",\"methodName\":\"authenticate\"},{\"fileName\":\"SMTPTransport.java\",\"lineNumber\":865,\"className\":\"org.eclipse.angus.mail.smtp.SMTPTransport\",\"methodName\":\"authenticate\"},{\"fileName\":\"SMTPTransport.java\",\"lineNumber\":769,\"className\":\"org.eclipse.angus.mail.smtp.SMTPTransport\",\"methodName\":\"protocolConnect\"},{\"fileName\":\"Service.java\",\"lineNumber\":345,\"className\":\"jakarta.mail.Service\",\"methodName\":\"connect\"},{\"fileName\":\"JavaMailSenderImpl.java\",\"lineNumber\":480,\"className\":\"org.springframework.mail.javamail.JavaMailSenderImpl\",\"methodName\":\"connectTransport\"},{\"fileName\":\"JavaMailSenderImpl.java\",\"lineNumber\":399,\"className\":\"org.springframework.mail.javamail.JavaMailSenderImpl\",\"methodName\":\"doSend\"},{\"fileName\":\"JavaMailSenderImpl.java\",\"lineNumber\":317,\"className\":\"org.springframework.mail.javamail.JavaMailSenderImpl\",\"methodName\":\"send\"},{\"fileName\":\"MailSender.java\",\"lineNumber\":42,\"className\":\"org.springframework.mail.MailSender\",\"methodName\":\"send\"},{\"fileName\":\"MailUtils.java\",\"lineNumber\":53,\"className\":\"com.bcy.utils.MailUtils\",\"methodName\":\"sendGeneralEmail\"},{\"fileName\":\"BcyMsgService.java\",\"lineNumber\":94,\"className\":\"com.bcy.system.business.common.BcyMsgService\",\"methodName\":\"sendMail\"},{\"fileName\":\"BcyAdminSpiImpl.java\",\"lineNumber\":44,\"className\":\"com.bcy.system.spi.BcyAdminSpiImpl\",\"methodName\":\"sendMail\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":-2,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke0\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":77,\"className\":\"jdk.internal.reflec', 'Authentication failed', 518, '110.81.121.214', '中国 福建 泉州 电信', '2024-11-23 21:27:30');
INSERT INTO `sys_log` VALUES (149, 'BCY', 1, 'bcy-admin', '/sys/api/web/seekAdvice', 0, '发送简单邮件信息', 'com.bcy.system.spi.BcyAdminSpiImpl.sendMail()', '{\"content\":\"用户：[***********]已提交号码，请及时回访！\",\"from\":\"民生期货<<EMAIL>>\",\"subject\":\"领取通知\",\"to\":\"<EMAIL>\"}', '\"发送短信成功\"', '', 1641, '110.81.121.214', '中国 福建 泉州 电信', '2024-11-23 21:50:51');
INSERT INTO `sys_log` VALUES (150, 'BCY', 1, 'bcy-admin', '/sys/api/web/seekAdvice', 0, '发送简单邮件信息', 'com.bcy.system.spi.BcyAdminSpiImpl.sendMail()', '{\"content\":\"用户：[13205016581]已提交号码，请及时回访！\",\"from\":\"博昌云科技<<EMAIL>>\",\"subject\":\"领取通知\",\"to\":\"<EMAIL>\"}', '\"发送短信成功\"', '', 1568, '59.61.209.40', '中国 福建 泉州 电信', '2024-11-26 17:16:07');
INSERT INTO `sys_log` VALUES (151, 'BCY', 1, 'bcy-admin', '/sys/api/web/seekAdvice', 0, '发送简单邮件信息', 'com.bcy.system.spi.BcyAdminSpiImpl.sendMail()', '{\"content\":\"用户：[13205016533]已提交号码，请及时回访！\",\"from\":\"博昌云科技<<EMAIL>>\",\"subject\":\"领取通知\",\"to\":\"<EMAIL>\"}', '\"发送短信成功\"', '', 1068, '59.61.209.40', '中国 福建 泉州 电信', '2024-11-26 17:49:51');
INSERT INTO `sys_log` VALUES (152, 'BCY', 1, 'bcy-admin', '/sys/api/web/seekAdvice', 0, '发送简单邮件信息', 'com.bcy.system.spi.BcyAdminSpiImpl.sendMail()', '{\"content\":\"用户：[13205017389]已提交号码，请及时回访！\",\"from\":\"博昌云科技<<EMAIL>>\",\"subject\":\"领取通知\",\"to\":\"<EMAIL>\"}', '\"发送短信成功\"', '', 1121, '183.253.18.27', '中国 福建 厦门 移动', '2024-11-26 22:38:48');
INSERT INTO `sys_log` VALUES (153, 'admin', 1, 'SYS', '/sys/login/sys', 0, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"5bnwy\",\"userName\":\"admin\",\"uuid\":\"bcy1732631937308\"}', '{\"code\":200,\"data\":{\"accessToken\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjEsInJuU3RyIjoiNzVXb3ZiTjI1bWV2UEFsWkowN2VvR1lnMVk3Njh2cEcifQ.SP_eWKqqMWqmSWn-HS-_BsLQPTCwYywOUJLofzNcGZI\",\"expireTime\":259200}}', '', 1930, '183.253.18.27', '中国 福建 厦门 移动', '2024-11-26 22:39:07');
INSERT INTO `sys_log` VALUES (154, 'BCY', 1, 'SYS', '/sys/login/sys', 1, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"g63em\",\"userName\":\"admin\",\"uuid\":\"bcy1732677136868\"}', '{\"code\":500,\"errMsg\":\"验证码校验失败，请重试！\",\"module\":\"BCY\",\"stackTrace\":[{\"fileName\":\"LoginService.java\",\"lineNumber\":82,\"className\":\"com.bcy.system.business.login.LoginService\",\"methodName\":\"login\"},{\"fileName\":\"LoginController.java\",\"lineNumber\":75,\"className\":\"com.bcy.system.api.login.LoginController\",\"methodName\":\"login\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":-2,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke0\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":77,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"DelegatingMethodAccessorImpl.java\",\"lineNumber\":43,\"className\":\"jdk.internal.reflect.DelegatingMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"Method.java\",\"lineNumber\":568,\"className\":\"java.lang.reflect.Method\",\"methodName\":\"invoke\"},{\"fileName\":\"AopUtils.java\",\"lineNumber\":355,\"className\":\"org.springframework.aop.support.AopUtils\",\"methodName\":\"invokeJoinpointUsingReflection\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":196,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"invokeJoinpoint\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":163,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"MethodBeforeAdviceInterceptor.java\",\"lineNumber\":58,\"className\":\"org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":173,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"file', '', 13, '59.61.209.40', '中国 福建 泉州 电信', '2024-11-27 11:30:30');
INSERT INTO `sys_log` VALUES (155, 'admin', 1, 'SYS', '/sys/login/sys', 0, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"nxfym\",\"userName\":\"admin\",\"uuid\":\"bcy1732678230105\"}', '{\"code\":200,\"data\":{\"accessToken\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjEsInJuU3RyIjoiUEpQcVE3c1ZLWmhyZmFsdnQzalh4WUVMTmVIU0VHam4ifQ.dvHdtAC60C_k_C95a4bzlAaFbQz7nTFGfqjnXjK3pSA\",\"expireTime\":259200}}', '', 38, '59.61.209.40', '中国 福建 泉州 电信', '2024-11-27 11:30:35');
INSERT INTO `sys_log` VALUES (156, 'admin', 1, 'SYS', '/sys/menu/insert', 0, '新增菜单', 'com.bcy.system.api.sys.SysMenuController.insert()', '{\"alwaysShow\":0,\"component\":\"web/platform/index\",\"icon\":\"client\",\"keepAlive\":1,\"module\":\"bcy-sys\",\"name\":\"平台管理\",\"parentId\":55,\"path\":\"platform\",\"sort\":1,\"type\":\"MENU\",\"visible\":1}', '{\"code\":200,\"data\":\"新增成功\"}', '', 151, '59.61.209.40', '中国 福建 泉州 电信', '2024-11-27 11:32:00');
INSERT INTO `sys_log` VALUES (157, 'admin', 1, 'SYS', '/sys/login/sys', 0, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"mfa75\",\"userName\":\"admin\",\"uuid\":\"bcy1732687816441\"}', '{\"code\":200,\"data\":{\"accessToken\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjEsInJuU3RyIjoiNXdBUXBUd09UUlRnRWRDTUxsZ0pjZnc4NWxCNXQ0T3IifQ.kFt-UsooE-tAz68hL-2OgkR0C4NbsuuxHP7jh_dR38s\",\"expireTime\":259200}}', '', 22, '59.61.209.40', '中国 福建 泉州 电信', '2024-11-27 14:10:21');
INSERT INTO `sys_log` VALUES (158, 'admin', 1, 'SYS', '/sys/menu/insert', 0, '新增菜单', 'com.bcy.system.api.sys.SysMenuController.insert()', '{\"alwaysShow\":0,\"icon\":\"qq\",\"keepAlive\":1,\"module\":\"bcy-sys\",\"name\":\"宠物知识库\",\"parentId\":0,\"path\":\"pet\",\"sort\":1,\"type\":\"CATALOG\",\"visible\":1}', '{\"code\":200,\"data\":\"新增成功\"}', '', 23, '59.61.209.67', '中国 福建 泉州 电信', '2024-11-28 11:29:38');
INSERT INTO `sys_log` VALUES (159, 'admin', 1, 'SYS', '/sys/menu/update', 0, '修改菜单', 'com.bcy.system.api.sys.SysMenuController.update()', '{\"alwaysShow\":0,\"component\":\"Layout\",\"icon\":\"qq\",\"id\":62,\"keepAlive\":1,\"module\":\"bcy-sys\",\"name\":\"宠物知识库\",\"parentId\":0,\"path\":\"/pet\",\"perm\":\"\",\"redirect\":\"\",\"sort\":4,\"type\":\"CATALOG\",\"visible\":1}', '{\"code\":200,\"data\":\"更新成功\"}', '', 15, '59.61.209.67', '中国 福建 泉州 电信', '2024-11-28 11:29:57');
INSERT INTO `sys_log` VALUES (160, 'admin', 1, 'SYS', '/sys/menu/insert', 0, '新增菜单', 'com.bcy.system.api.sys.SysMenuController.insert()', '{\"alwaysShow\":0,\"component\":\"pet/category/index\",\"icon\":\"el-icon-Dish\",\"keepAlive\":1,\"module\":\"bcy-sys\",\"name\":\"分类管理\",\"parentId\":62,\"path\":\"category\",\"sort\":1,\"type\":\"MENU\",\"visible\":1}', '{\"code\":200,\"data\":\"新增成功\"}', '', 12, '59.61.209.67', '中国 福建 泉州 电信', '2024-11-28 11:31:17');
INSERT INTO `sys_log` VALUES (161, 'admin', 1, 'SYS', '/sys/menu/update', 0, '修改菜单', 'com.bcy.system.api.sys.SysMenuController.update()', '{\"alwaysShow\":0,\"component\":\"Layout\",\"icon\":\"el-icon-Message\",\"id\":43,\"keepAlive\":0,\"module\":\"bcy-api\",\"name\":\"消息中心\",\"parentId\":0,\"path\":\"/msg\",\"perm\":\"\",\"redirect\":\"/msg/message\",\"sort\":2,\"type\":\"CATALOG\",\"visible\":1}', '{\"code\":200,\"data\":\"更新成功\"}', '', 14, '59.61.209.67', '中国 福建 泉州 电信', '2024-11-28 11:31:44');
INSERT INTO `sys_log` VALUES (162, 'admin', 1, 'SYS', '/sys/menu/insert', 0, '新增菜单', 'com.bcy.system.api.sys.SysMenuController.insert()', '{\"alwaysShow\":0,\"keepAlive\":1,\"module\":\"bcy-sys\",\"name\":\"新增宠物分类\",\"parentId\":63,\"perm\":\"pet:category:add\",\"sort\":1,\"type\":\"BUTTON\",\"visible\":1}', '{\"code\":200,\"data\":\"新增成功\"}', '', 13, '59.61.209.67', '中国 福建 泉州 电信', '2024-11-28 15:31:49');
INSERT INTO `sys_log` VALUES (163, 'admin', 1, 'SYS', '/sys/menu/insert', 0, '新增菜单', 'com.bcy.system.api.sys.SysMenuController.insert()', '{\"alwaysShow\":0,\"keepAlive\":1,\"module\":\"bcy-sys\",\"name\":\"编辑宠物分类\",\"parentId\":63,\"perm\":\"pet:category:edit\",\"sort\":1,\"type\":\"BUTTON\",\"visible\":1}', '{\"code\":200,\"data\":\"新增成功\"}', '', 12, '59.61.209.67', '中国 福建 泉州 电信', '2024-11-28 15:32:10');
INSERT INTO `sys_log` VALUES (164, 'admin', 1, 'SYS', '/sys/menu/update', 0, '修改菜单', 'com.bcy.system.api.sys.SysMenuController.update()', '{\"alwaysShow\":0,\"component\":\"pet/category/index\",\"icon\":\"el-icon-Dish\",\"id\":63,\"keepAlive\":1,\"module\":\"bcy-sys\",\"name\":\"宠物分类\",\"parentId\":62,\"path\":\"category\",\"perm\":\"\",\"redirect\":\"\",\"sort\":1,\"type\":\"MENU\",\"visible\":1}', '{\"code\":200,\"data\":\"更新成功\"}', '', 24, '59.61.209.67', '中国 福建 泉州 电信', '2024-11-28 15:32:33');
INSERT INTO `sys_log` VALUES (165, 'admin', 1, 'SYS', '/sys/menu/insert', 0, '新增菜单', 'com.bcy.system.api.sys.SysMenuController.insert()', '{\"alwaysShow\":0,\"keepAlive\":1,\"module\":\"bcy-sys\",\"name\":\"删除宠物分类\",\"parentId\":63,\"perm\":\"pet:category:delete\",\"redirect\":\"\",\"sort\":1,\"type\":\"BUTTON\",\"visible\":1}', '{\"code\":200,\"data\":\"新增成功\"}', '', 12, '59.61.209.67', '中国 福建 泉州 电信', '2024-11-28 15:32:55');
INSERT INTO `sys_log` VALUES (166, 'admin', 1, 'SYS', '/sys/menu/insert', 0, '新增菜单', 'com.bcy.system.api.sys.SysMenuController.insert()', '{\"alwaysShow\":0,\"component\":\"pet/category/data\",\"icon\":\"el-icon-Bowl\",\"keepAlive\":1,\"module\":\"bcy-sys\",\"name\":\"宠物子分类\",\"parentId\":62,\"path\":\"category-data\",\"perm\":\"\",\"redirect\":\"\",\"sort\":1,\"type\":\"MENU\",\"visible\":0}', '{\"code\":200,\"data\":\"新增成功\"}', '', 13, '59.61.209.67', '中国 福建 泉州 电信', '2024-11-28 15:34:04');
INSERT INTO `sys_log` VALUES (167, 'admin', 1, 'SYS', '/sys/menu/insert', 0, '新增菜单', 'com.bcy.system.api.sys.SysMenuController.insert()', '{\"alwaysShow\":0,\"keepAlive\":1,\"module\":\"bcy-sys\",\"name\":\"查看分类详情\",\"parentId\":63,\"perm\":\"pet:category:detail\",\"sort\":1,\"type\":\"BUTTON\",\"visible\":1}', '{\"code\":200,\"data\":\"新增成功\"}', '', 16, '59.61.209.67', '中国 福建 泉州 电信', '2024-11-29 10:53:59');
INSERT INTO `sys_log` VALUES (168, 'admin', 1, 'SYS', '/sys/menu/insert', 0, '新增菜单', 'com.bcy.system.api.sys.SysMenuController.insert()', '{\"alwaysShow\":0,\"component\":\"pet/category/detail\",\"icon\":\"el-icon-Guide\",\"keepAlive\":1,\"module\":\"bcy-sys\",\"name\":\"宠物分类详情\",\"parentId\":63,\"path\":\"category-detail\",\"sort\":1,\"type\":\"MENU\",\"visible\":1}', '{\"code\":200,\"data\":\"新增成功\"}', '', 11, '59.61.209.67', '中国 福建 泉州 电信', '2024-11-29 10:56:52');
INSERT INTO `sys_log` VALUES (169, 'admin', 1, 'SYS', '/sys/menu/update', 0, '修改菜单', 'com.bcy.system.api.sys.SysMenuController.update()', '{\"alwaysShow\":0,\"component\":\"pet/category/detail\",\"icon\":\"el-icon-Guide\",\"id\":69,\"keepAlive\":1,\"module\":\"bcy-sys\",\"name\":\"宠物分类详情\",\"parentId\":62,\"path\":\"category-detail\",\"perm\":\"\",\"redirect\":\"\",\"sort\":1,\"type\":\"MENU\",\"visible\":1}', '{\"code\":200,\"data\":\"更新成功\"}', '', 10, '59.61.209.67', '中国 福建 泉州 电信', '2024-11-29 10:57:59');
INSERT INTO `sys_log` VALUES (170, 'admin', 1, 'SYS', '/sys/menu/update', 0, '修改菜单', 'com.bcy.system.api.sys.SysMenuController.update()', '{\"alwaysShow\":0,\"component\":\"pet/category/detail\",\"icon\":\"el-icon-Guide\",\"id\":69,\"keepAlive\":0,\"module\":\"bcy-sys\",\"name\":\"宠物分类详情\",\"parentId\":62,\"path\":\"category-detail\",\"perm\":\"\",\"redirect\":\"\",\"sort\":1,\"type\":\"MENU\",\"visible\":0}', '{\"code\":200,\"data\":\"更新成功\"}', '', 9, '59.61.209.67', '中国 福建 泉州 电信', '2024-11-29 10:58:13');
INSERT INTO `sys_log` VALUES (171, 'admin', 1, 'SYS', '/sys/menu/insert', 0, '新增菜单', 'com.bcy.system.api.sys.SysMenuController.insert()', '{\"alwaysShow\":0,\"component\":\"pet/content/index\",\"icon\":\"el-icon-Memo\",\"keepAlive\":1,\"module\":\"bcy-sys\",\"name\":\"宠物文章\",\"parentId\":62,\"path\":\"content\",\"sort\":1,\"type\":\"MENU\",\"visible\":1}', '{\"code\":200,\"data\":\"新增成功\"}', '', 130, '59.61.209.67', '中国 福建 泉州 电信', '2024-11-29 15:35:54');
INSERT INTO `sys_log` VALUES (172, 'admin', 1, 'SYS', '/sys/menu/insert', 0, '新增菜单', 'com.bcy.system.api.sys.SysMenuController.insert()', '{\"alwaysShow\":0,\"keepAlive\":1,\"module\":\"bcy-sys\",\"name\":\"新增宠物文章\",\"parentId\":70,\"perm\":\"pet:content:add\",\"sort\":1,\"type\":\"BUTTON\",\"visible\":1}', '{\"code\":200,\"data\":\"新增成功\"}', '', 21, '59.61.209.67', '中国 福建 泉州 电信', '2024-11-29 15:36:42');
INSERT INTO `sys_log` VALUES (173, 'admin', 1, 'SYS', '/sys/menu/insert', 0, '新增菜单', 'com.bcy.system.api.sys.SysMenuController.insert()', '{\"alwaysShow\":0,\"keepAlive\":1,\"module\":\"bcy-sys\",\"name\":\"编辑宠物文章\",\"parentId\":70,\"perm\":\"pet:content:edit\",\"sort\":2,\"type\":\"BUTTON\",\"visible\":1}', '{\"code\":200,\"data\":\"新增成功\"}', '', 17, '59.61.209.67', '中国 福建 泉州 电信', '2024-11-29 15:37:37');
INSERT INTO `sys_log` VALUES (174, 'admin', 1, 'SYS', '/sys/menu/insert', 0, '新增菜单', 'com.bcy.system.api.sys.SysMenuController.insert()', '{\"alwaysShow\":0,\"keepAlive\":1,\"module\":\"bcy-sys\",\"name\":\"删除宠物文章\",\"parentId\":70,\"perm\":\"pet:content:delete\",\"sort\":3,\"type\":\"BUTTON\",\"visible\":1}', '{\"code\":200,\"data\":\"新增成功\"}', '', 20, '59.61.209.67', '中国 福建 泉州 电信', '2024-11-29 15:37:54');
INSERT INTO `sys_log` VALUES (175, 'admin', 1, 'SYS', '/sys/menu/insert', 0, '新增菜单', 'com.bcy.system.api.sys.SysMenuController.insert()', '{\"alwaysShow\":0,\"component\":\"pet/content/data\",\"icon\":\"el-icon-Reading\",\"keepAlive\":0,\"module\":\"bcy-sys\",\"name\":\"宠物文章详情\",\"parentId\":62,\"path\":\"content-data\",\"sort\":1,\"type\":\"MENU\",\"visible\":0}', '{\"code\":200,\"data\":\"新增成功\"}', '', 21, '59.61.209.67', '中国 福建 泉州 电信', '2024-11-29 15:38:54');
INSERT INTO `sys_log` VALUES (176, 'admin', 1, 'SYS', '/sys/login/sys', 0, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"ncgcy\",\"userName\":\"admin\",\"uuid\":\"bcy1733102683763\"}', '{\"code\":200,\"data\":{\"accessToken\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjEsInJuU3RyIjoialZ4MjdjdUlKbTJGeGVTOUJ0QmVnVW9hSEpIUGVIa3EifQ.zG5vjAlpX1EVgDvyKgVXiTWjp2-STYRWM__y2cPYo-I\",\"expireTime\":259200}}', '', 1451, '59.61.209.67', '中国 福建 泉州 电信', '2024-12-02 09:24:54');
INSERT INTO `sys_log` VALUES (177, 'admin', 1, 'SYS', '/sys/login/sys', 0, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"82wfp\",\"userName\":\"admin\",\"uuid\":\"bcy1733133154960\"}', '{\"code\":200,\"data\":{\"accessToken\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjEsInJuU3RyIjoiTkNQUWZwMkQwZTl5cmZPdVFrMGVkMVVkT0FLOEhSNlcifQ.wpvWNid1kPZzVZpJ4qJjmZApJL7_pnuzKqrnHzBGB_s\",\"expireTime\":259200}}', '', 2266, '59.61.209.67', '中国 福建 泉州 电信', '2024-12-02 17:52:43');
INSERT INTO `sys_log` VALUES (178, 'BCY', 1, 'SYS', '/sys/login/sys', 1, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"2enc8\",\"userName\":\"admin\",\"uuid\":\"bcy1733911077721\"}', '{\"code\":500,\"errMsg\":\"用户密码错误\",\"module\":\"BCY\",\"stackTrace\":[{\"fileName\":\"LoginService.java\",\"lineNumber\":88,\"className\":\"com.bcy.system.business.login.LoginService\",\"methodName\":\"login\"},{\"fileName\":\"LoginController.java\",\"lineNumber\":75,\"className\":\"com.bcy.system.api.login.LoginController\",\"methodName\":\"login\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":-2,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke0\"},{\"fileName\":\"NativeMethodAccessorImpl.java\",\"lineNumber\":77,\"className\":\"jdk.internal.reflect.NativeMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"DelegatingMethodAccessorImpl.java\",\"lineNumber\":43,\"className\":\"jdk.internal.reflect.DelegatingMethodAccessorImpl\",\"methodName\":\"invoke\"},{\"fileName\":\"Method.java\",\"lineNumber\":568,\"className\":\"java.lang.reflect.Method\",\"methodName\":\"invoke\"},{\"fileName\":\"AopUtils.java\",\"lineNumber\":355,\"className\":\"org.springframework.aop.support.AopUtils\",\"methodName\":\"invokeJoinpointUsingReflection\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":196,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"invokeJoinpoint\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":163,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"MethodBeforeAdviceInterceptor.java\",\"lineNumber\":58,\"className\":\"org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor\",\"methodName\":\"invoke\"},{\"fileName\":\"ReflectiveMethodInvocation.java\",\"lineNumber\":173,\"className\":\"org.springframework.aop.framework.ReflectiveMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":\"CglibAopProxy.java\",\"lineNumber\":768,\"className\":\"org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation\",\"methodName\":\"proceed\"},{\"fileName\":', '', 83, '59.61.209.116', '中国 福建 泉州 电信', '2024-12-11 17:58:07');
INSERT INTO `sys_log` VALUES (179, 'admin', 1, 'SYS', '/sys/login/sys', 0, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"cdmn3\",\"userName\":\"admin\",\"uuid\":\"bcy1733911087056\"}', '{\"code\":200,\"data\":{\"accessToken\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjEsInJuU3RyIjoiVzhpRk9QWWZYZkVCRE1xSFVKcFJtRXNXZmNsVlpPSEUifQ.Rr9IGM-7QOn1Wmc77cj7-vSpZ_YOiNOcXTGl-s7yyaA\",\"expireTime\":259200}}', '', 32, '59.61.209.116', '中国 福建 泉州 电信', '2024-12-11 17:58:24');
INSERT INTO `sys_log` VALUES (180, 'admin', 1, 'SYS', '/sys/login/sys', 0, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"88pdd\",\"userName\":\"admin\",\"uuid\":\"bcy1733911689793\"}', '{\"code\":200,\"data\":{\"accessToken\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjEsInJuU3RyIjoiYkZWM3E3YmlkNU4xU1VCOE9seGdrYVBUbVpRQ2NHYjEifQ.bFNU1IrgCvcPfOuJDMnVtv9u9QX3NSMbxkZDvLMtXbU\",\"expireTime\":259200}}', '', 22, '59.61.209.116', '中国 福建 泉州 电信', '2024-12-11 18:08:14');
INSERT INTO `sys_log` VALUES (181, 'admin', 1, 'SYS', '/sys/login/sys', 0, '用户登陆', 'com.bcy.system.api.login.LoginController.login()', '{\"captcha\":\"nnf44\",\"userName\":\"admin\",\"uuid\":\"bcy1735797121781\"}', '{\"code\":200,\"data\":{\"accessToken\":\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOjEsInJuU3RyIjoiRWFoT0NoSVFOVWpkMnhlYWI1S3cwdmZSeGQ1YXNON2sifQ.EzAcy9hjRchhv_-ektzEFJLTaqRdUfZ7G6mOp6keVmA\",\"expireTime\":259200}}', '', 1784, '110.81.120.192', '', '2025-01-02 13:52:07');

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `parent_id` bigint NOT NULL DEFAULT 0 COMMENT '父菜单ID',
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '菜单名称',
  `type` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '菜单类型(MENU:菜单 CATALOG:目录 BUTTON:按钮 EXTLINK:外链)',
  `path` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '路由路径(浏览器地址栏路径)',
  `component` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '组件路径(vue页面完整路径，省略.vue后缀)',
  `perm` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '权限标识',
  `visible` tinyint(1) NOT NULL DEFAULT 1 COMMENT '显示状态(1:显示;0:隐藏)',
  `sort` int NOT NULL DEFAULT 0 COMMENT '排序',
  `icon` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '菜单图标',
  `redirect` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '跳转路径',
  `always_show` tinyint NOT NULL DEFAULT 0 COMMENT '【目录】只有一个子路由是否始终显示(1:是 0:否)',
  `keep_alive` tinyint NOT NULL DEFAULT 0 COMMENT '【菜单】是否开启页面缓存(1:是 0:否)',
  `module` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模块服务',
  `tenant_id` bigint NOT NULL DEFAULT 1 COMMENT '租户ID',
  `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除（0否1是）',
  `create_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建人',
  `update_user` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '更新人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 75 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '菜单管理' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_menu
-- ----------------------------
INSERT INTO `sys_menu` VALUES (1, 0, '系统管理', 'CATALOG', '/system', 'Layout', '', 1, 100, 'system', '/system/user', 0, 0, 'bcy-sys', 0, 0, 'BCY', 'BCY', '2024-11-05 13:48:30', '2024-11-05 13:48:30', 0);
INSERT INTO `sys_menu` VALUES (2, 1, '用户管理', 'MENU', 'user', 'system/user/index', '', 1, 1, 'el-icon-Avatar', '', 0, 1, 'bcy-sys', 0, 0, 'BCY', 'BCY', '2024-11-05 13:48:30', '2024-11-05 13:48:30', 0);
INSERT INTO `sys_menu` VALUES (3, 2, '用户新增', 'BUTTON', '', '', 'sys:user:add', 1, 1, '', '', 0, 0, 'bcy-sys', 0, 0, 'BCY', 'BCY', '2024-11-05 13:48:30', '2024-11-05 13:48:30', 0);
INSERT INTO `sys_menu` VALUES (4, 2, '用户编辑', 'BUTTON', '', '', 'sys:user:edit', 1, 2, '', '', 0, 0, 'bcy-sys', 0, 0, 'BCY', 'BCY', '2024-11-05 13:48:30', '2024-11-05 13:48:30', 0);
INSERT INTO `sys_menu` VALUES (5, 2, '用户删除', 'BUTTON', '', '', 'sys:user:delete', 1, 3, '', '', 0, 0, 'bcy-sys', 0, 0, 'BCY', 'BCY', '2024-11-05 13:48:30', '2024-11-05 13:48:30', 0);
INSERT INTO `sys_menu` VALUES (6, 2, '重置密码', 'BUTTON', '', '', 'sys:user:reset_pwd', 1, 4, '', '', 0, 0, 'bcy-sys', 0, 0, 'BCY', 'BCY', '2024-11-05 13:48:31', '2024-11-05 13:48:31', 0);
INSERT INTO `sys_menu` VALUES (7, 1, '部门管理', 'MENU', 'dept', 'system/dept/index', '', 1, 2, 'tree', '', 0, 1, 'bcy-sys', 0, 0, 'BCY', 'BCY', '2024-11-05 13:48:31', '2024-11-05 13:48:31', 0);
INSERT INTO `sys_menu` VALUES (8, 7, '部门新增', 'BUTTON', '', '', 'sys:dept:add', 1, 1, '', '', 0, 0, 'bcy-sys', 0, 0, 'BCY', 'BCY', '2024-11-05 13:48:31', '2024-11-05 13:48:31', 0);
INSERT INTO `sys_menu` VALUES (9, 7, '部门编辑', 'BUTTON', '', '', 'sys:dept:edit', 1, 2, '', '', 0, 0, 'bcy-sys', 0, 0, 'BCY', 'BCY', '2024-11-05 13:48:31', '2024-11-05 13:48:31', 0);
INSERT INTO `sys_menu` VALUES (10, 7, '部门删除', 'BUTTON', '', '', 'sys:dept:delete', 1, 3, '', '', 0, 0, 'bcy-sys', 0, 0, 'BCY', 'BCY', '2024-11-05 13:48:31', '2024-11-05 13:48:31', 0);
INSERT INTO `sys_menu` VALUES (11, 1, '租户管理', 'MENU', 'tenant', 'system/tenant/index', '', 1, 7, 'el-icon-User', '', 0, 0, 'bcy-sys', 0, 0, 'BCY', 'BCY', '2024-11-05 13:48:31', '2024-11-05 13:48:31', 0);
INSERT INTO `sys_menu` VALUES (12, 11, '租户新增', 'BUTTON', '', '', 'sys:tenant:add', 1, 1, '', '', 0, 0, 'bcy-sys', 1, 0, 'BCY', 'BCY', '2024-11-05 13:48:31', '2024-11-05 13:48:31', 0);
INSERT INTO `sys_menu` VALUES (13, 11, '租户编辑', 'BUTTON', '', '', 'sys:tenant:edit', 1, 2, '', '', 0, 0, 'bcy-sys', 1, 0, 'BCY', 'BCY', '2024-11-05 13:48:32', '2024-11-05 13:48:32', 0);
INSERT INTO `sys_menu` VALUES (14, 11, '租户删除', 'BUTTON', '', '', 'sys:tenant:delete', 1, 3, '', '', 0, 0, 'bcy-sys', 1, 0, 'BCY', 'BCY', '2024-11-05 13:48:32', '2024-11-05 13:48:32', 0);
INSERT INTO `sys_menu` VALUES (15, 1, '角色管理', 'MENU', 'role', 'system/role/index', '', 1, 3, 'role', '', 0, 1, 'bcy-sys', 0, 0, 'BCY', 'BCY', '2024-11-05 13:48:32', '2024-11-05 13:48:32', 0);
INSERT INTO `sys_menu` VALUES (16, 15, '角色新增', 'BUTTON', '', '', 'sys:role:add', 1, 1, '', '', 0, 0, 'bcy-sys', 0, 0, 'BCY', 'BCY', '2024-11-05 13:48:32', '2024-11-05 13:48:32', 0);
INSERT INTO `sys_menu` VALUES (17, 15, '角色编辑', 'BUTTON', '', '', 'sys:role:edit', 1, 2, '', '', 0, 0, 'bcy-sys', 0, 0, 'BCY', 'BCY', '2024-11-05 13:48:32', '2024-11-05 13:48:32', 0);
INSERT INTO `sys_menu` VALUES (18, 15, '角色删除', 'BUTTON', '', '', 'sys:role:delete', 1, 3, '', '', 0, 0, 'bcy-sys', 0, 0, 'BCY', 'BCY', '2024-11-05 13:48:32', '2024-11-05 13:48:32', 0);
INSERT INTO `sys_menu` VALUES (19, 1, '菜单管理', 'MENU', 'menu', 'system/menu/index', '', 1, 4, 'menu', '', 0, 1, 'bcy-sys', 0, 0, 'BCY', 'BCY', '2024-11-05 13:48:32', '2024-11-05 13:48:32', 0);
INSERT INTO `sys_menu` VALUES (20, 19, '菜单新增', 'BUTTON', '', '', 'sys:menu:add', 1, 1, '', '', 0, 0, 'bcy-sys', 0, 0, 'BCY', 'BCY', '2024-11-05 13:48:33', '2024-11-05 13:48:33', 0);
INSERT INTO `sys_menu` VALUES (21, 19, '菜单编辑', 'BUTTON', '', '', 'sys:menu:edit', 1, 2, '', '', 0, 0, 'bcy-sys', 0, 0, 'BCY', 'BCY', '2024-11-05 13:48:33', '2024-11-05 13:48:33', 0);
INSERT INTO `sys_menu` VALUES (22, 19, '菜单删除', 'BUTTON', '', '', 'sys:menu:delete', 1, 3, '', '', 0, 0, 'bcy-sys', 0, 0, 'BCY', 'BCY', '2024-11-05 13:50:30', '2024-11-05 13:50:30', 0);
INSERT INTO `sys_menu` VALUES (23, 1, '模块管理', 'MENU', 'module', 'system/module/index', '', 1, 8, 'el-icon-School', '', 0, 1, 'bcy-sys', 1, 0, 'BCY', 'BCY', '2024-11-05 13:50:30', '2024-11-05 13:50:30', 0);
INSERT INTO `sys_menu` VALUES (24, 23, '模块新增', 'BUTTON', '', '', 'sys:module:add', 1, 1, '', '', 0, 1, 'bcy-sys', 1, 0, 'BCY', 'BCY', '2024-11-05 13:50:30', '2024-11-05 13:50:30', 0);
INSERT INTO `sys_menu` VALUES (25, 23, '模块编辑', 'BUTTON', '', '', 'sys:module:edit', 1, 2, '', '', 0, 1, 'bcy-sys', 1, 0, 'BCY', 'BCY', '2024-11-05 13:50:30', '2024-11-05 13:50:30', 0);
INSERT INTO `sys_menu` VALUES (26, 23, '模块删除', 'BUTTON', '', '', 'sys:module:delete', 1, 3, '', '', 0, 1, 'bcy-sys', 1, 0, 'BCY', 'BCY', '2024-11-05 13:50:30', '2024-11-05 13:50:30', 0);
INSERT INTO `sys_menu` VALUES (27, 1, '字典管理', 'MENU', 'dict', 'system/dict/index', '', 1, 5, 'dict', '', 0, 1, 'bcy-sys', 0, 0, 'BCY', 'BCY', '2024-11-05 13:50:31', '2024-11-05 13:50:31', 0);
INSERT INTO `sys_menu` VALUES (28, 1, '字典项管理', 'MENU', 'dict-data', 'system/dict/data', '', 0, 6, 'dict', '', 0, 0, 'bcy-sys', 0, 0, 'BCY', 'admin', '2024-11-05 13:50:31', '2024-11-08 23:48:44', 3);
INSERT INTO `sys_menu` VALUES (29, 27, '字典新增', 'BUTTON', '', '', 'sys:dict:add', 1, 1, '', '', 0, 0, 'bcy-sys', 0, 0, 'BCY', 'BCY', '2024-11-05 13:50:31', '2024-11-05 13:50:31', 0);
INSERT INTO `sys_menu` VALUES (30, 27, '字典编辑', 'BUTTON', '', '', 'sys:dict:edit', 1, 2, '', '', 0, 0, 'bcy-sys', 0, 0, 'BCY', 'BCY', '2024-11-05 13:50:31', '2024-11-05 13:50:31', 0);
INSERT INTO `sys_menu` VALUES (31, 27, '字典删除', 'BUTTON', '', '', 'sys:dict:delete', 1, 3, '', '', 0, 0, 'bcy-sys', 0, 0, 'BCY', 'BCY', '2024-11-05 13:50:31', '2024-11-05 13:50:31', 0);
INSERT INTO `sys_menu` VALUES (33, 53, '系统日志', 'MENU', 'syslog', 'logs/syslog/index', '', 1, 2, 'table', '', 0, 0, 'bcy-sys', 1, 0, 'BCY', 'admin', '2024-11-05 13:50:32', '2024-11-10 19:44:25', 4);
INSERT INTO `sys_menu` VALUES (34, 33, '清除日志', 'BUTTON', '', '', 'sys:log:clean', 1, 1, '', '', 0, 0, 'bcy-sys', 1, 0, 'BCY', 'BCY', '2024-11-05 13:50:32', '2024-11-05 13:50:32', 0);
INSERT INTO `sys_menu` VALUES (35, 53, 'API日志', 'MENU', 'apilog', 'logs/apilog/index', '', 1, 3, 'api', '', 0, 0, 'bcy-sys', 1, 0, 'BCY', 'admin', '2024-11-05 13:50:32', '2024-11-10 19:44:36', 3);
INSERT INTO `sys_menu` VALUES (36, 35, '清除API日志', 'BUTTON', '', '', 'sys:apiLog:clean', 1, 1, '', '', 0, 1, 'bcy-sys', 1, 0, 'BCY', 'BCY', '2024-11-05 13:50:32', '2024-11-05 13:50:32', 0);
INSERT INTO `sys_menu` VALUES (37, 1, '定时任务', 'EX_TLINK', 'http://112.48.194.3:8081/bcy-job/', 'Layout', '', 1, 198, 'el-icon-Edit', '', 0, 1, 'bcy-sys', 1, 0, 'BCY', 'BCY', '2024-11-05 13:50:32', '2024-11-05 13:50:32', 0);
INSERT INTO `sys_menu` VALUES (38, 1, '配置管理', 'MENU', 'config', 'system/config/index', '', 1, 6, 'setting', '', 0, 0, 'bcy-sys', 1, 0, 'BCY', 'BCY', '2024-11-05 13:50:32', '2024-11-05 13:50:32', 0);
INSERT INTO `sys_menu` VALUES (39, 38, '配置新增', 'BUTTON', '', '', 'sys:config:add', 1, 1, '', '', 0, 0, 'bcy-sys', 1, 0, 'BCY', 'BCY', '2024-11-05 13:50:32', '2024-11-05 13:50:32', 0);
INSERT INTO `sys_menu` VALUES (40, 38, '配置编辑', 'BUTTON', '', '', 'sys:config:edit', 1, 2, '', '', 0, 0, 'bcy-sys', 1, 0, 'BCY', 'BCY', '2024-11-05 13:50:33', '2024-11-05 13:50:33', 0);
INSERT INTO `sys_menu` VALUES (41, 38, '配置删除', 'BUTTON', '', '', 'sys:config:delete', 1, 3, '', '', 0, 0, 'bcy-sys', 1, 0, 'BCY', 'BCY', '2024-11-05 13:50:33', '2024-11-05 13:50:33', 0);
INSERT INTO `sys_menu` VALUES (42, 38, '配置缓存', 'BUTTON', '', '', 'sys:config:cache', 1, 4, '', '', 0, 0, 'bcy-sys', 1, 0, 'BCY', 'BCY', '2024-11-05 13:50:33', '2024-11-05 13:50:33', 0);
INSERT INTO `sys_menu` VALUES (43, 0, '消息中心', 'CATALOG', '/msg', 'Layout', '', 1, 2, 'el-icon-Message', '/msg/message', 0, 0, 'bcy-api', 1, 0, 'BCY', 'admin', '2024-11-05 13:50:33', '2024-11-28 11:31:44', 1);
INSERT INTO `sys_menu` VALUES (44, 43, '消息管理', 'MENU', 'msg', 'msg/message/index', '', 1, 2, 'el-icon-ChatDotSquare', '', 0, 0, 'bcy-sys', 1, 0, 'BCY', 'BCY', '2024-11-05 13:50:33', '2024-11-05 13:50:33', 0);
INSERT INTO `sys_menu` VALUES (45, 44, '消息新增', 'BUTTON', '', '', 'sys:msg:add', 1, 1, '', '', 0, 0, 'bcy-sys', 1, 0, 'BCY', 'BCY', '2024-11-05 13:50:33', '2024-11-05 13:50:33', 0);
INSERT INTO `sys_menu` VALUES (46, 44, '消息编辑', 'BUTTON', '', '', 'sys:msg:edit', 1, 2, '', '', 0, 0, 'bcy-sys', 1, 0, 'BCY', 'BCY', '2024-11-05 13:50:33', '2024-11-05 13:50:33', 0);
INSERT INTO `sys_menu` VALUES (47, 44, '消息删除', 'BUTTON', '', '', 'sys:msg:delete', 1, 3, '', '', 0, 0, 'bcy-sys', 1, 0, 'BCY', 'BCY', '2024-11-05 13:50:34', '2024-11-05 13:50:34', 0);
INSERT INTO `sys_menu` VALUES (48, 44, '消息发送', 'BUTTON', '', '', 'sys:msg:push', 1, 4, '', '', 0, 0, 'bcy-sys', 1, 0, 'BCY', 'BCY', '2024-11-05 13:50:34', '2024-11-05 13:50:34', 0);
INSERT INTO `sys_menu` VALUES (49, 43, '我的消息', 'MENU', 'todo', 'msg/todo/index', '', 1, 1, 'el-icon-Avatar', '', 0, 0, 'bcy-api', 1, 0, 'BCY', 'BCY', '2024-11-05 13:50:34', '2024-11-05 13:50:34', 0);
INSERT INTO `sys_menu` VALUES (50, 0, '平台文档', 'CATALOG', '/api', 'Layout', '', 1, 99, 'document', '/doc/doc', 1, 0, 'bcy-sys', 1, 0, 'BCY', 'BCY', '2024-11-05 13:50:34', '2024-11-05 13:50:34', 0);
INSERT INTO `sys_menu` VALUES (51, 50, 'apifox', 'MENU', 'api', 'api/apifox', '', 1, 1, 'api', '', 0, 0, 'bcy-sys', 1, 0, 'BCY', 'BCY', '2024-11-05 13:50:34', '2024-11-05 13:50:34', 0);
INSERT INTO `sys_menu` VALUES (52, 50, 'knif4j', 'EX_TLINK', 'https://api.bcycloud.com/sys/doc.html#/home', 'Layout', '', 1, 1, 'document', '', 0, 0, 'bcy-sys', 1, 0, 'BCY', 'BCY', '2024-11-05 13:50:34', '2024-11-05 13:50:34', 0);
INSERT INTO `sys_menu` VALUES (53, 0, '日志管理', 'CATALOG', '/logs', 'Layout', '', 1, 101, 'el-icon-Coffee', '', 0, 0, 'bcy-sys', 1, 0, 'admin', 'admin', '2024-11-10 19:41:00', '2024-11-10 19:43:03', 2);
INSERT INTO `sys_menu` VALUES (54, 53, '文件日志', 'MENU', 'filelog', 'logs/filelog/index', '', 1, 1, 'el-icon-Folder', '', 0, 1, 'bcy-sys', 1, 0, 'admin', 'admin', '2024-11-10 19:42:41', '2024-11-10 19:44:15', 2);
INSERT INTO `sys_menu` VALUES (55, 0, '留资管理', 'CATALOG', '/web', 'Layout', '', 1, 3, 'system', '', 0, 1, 'bcy-sys', 1, 0, 'admin', 'admin', '2024-11-18 09:52:03', '2024-11-18 16:35:36', 0);
INSERT INTO `sys_menu` VALUES (56, 55, '页面管理', 'MENU', 'web', 'web/site/index', '', 1, 1, 'tree', '', 0, 1, 'bcy-sys', 1, 0, 'admin', 'admin', '2024-11-18 09:52:03', '2024-11-18 09:52:03', 0);
INSERT INTO `sys_menu` VALUES (57, 56, '页面新增', 'BUTTON', '', '', 'sys:web:add', 1, 1, '', '', 0, 1, 'bcy-sys', 1, 0, 'admin', 'admin', '2024-11-18 11:51:31', '2024-11-18 11:51:31', 0);
INSERT INTO `sys_menu` VALUES (58, 56, '页面编辑', 'BUTTON', '', '', 'sys:web:edit', 1, 2, '', '', 0, 1, 'bcy-sys', 1, 0, 'admin', 'admin', '2024-11-18 11:51:32', '2024-11-18 11:51:32', 0);
INSERT INTO `sys_menu` VALUES (59, 56, '页面删除', 'BUTTON', '', '', 'sys:web:delete', 1, 3, '', '', 0, 1, 'bcy-sys', 1, 0, 'admin', 'admin', '2024-11-18 11:51:32', '2024-11-18 11:51:32', 0);
INSERT INTO `sys_menu` VALUES (60, 55, '客户管理', 'MENU', 'customer', 'web/customer/index', '', 1, 2, 'role', '', 0, 1, 'bcy-sys', 1, 0, 'admin', 'admin', '2024-11-18 16:37:21', '2024-11-18 16:38:29', 0);
INSERT INTO `sys_menu` VALUES (62, 0, '宠物知识库', 'CATALOG', '/pet', 'Layout', '', 1, 4, 'qq', '', 0, 1, 'bcy-sys', 1, 0, 'admin', 'admin', '2024-11-28 11:29:38', '2024-11-28 11:29:57', 1);
INSERT INTO `sys_menu` VALUES (63, 62, '宠物分类', 'MENU', 'category', 'pet/category/index', '', 1, 1, 'el-icon-Dish', '', 0, 1, 'bcy-sys', 1, 0, 'admin', 'admin', '2024-11-28 11:31:17', '2024-11-28 15:32:33', 1);
INSERT INTO `sys_menu` VALUES (64, 63, '新增宠物分类', 'BUTTON', '', '', 'pet:category:add', 1, 1, '', '', 0, 1, 'bcy-sys', 1, 0, 'admin', 'admin', '2024-11-28 15:31:49', '2024-11-28 15:31:49', 0);
INSERT INTO `sys_menu` VALUES (65, 63, '编辑宠物分类', 'BUTTON', '', '', 'pet:category:edit', 1, 1, '', '', 0, 1, 'bcy-sys', 1, 0, 'admin', 'admin', '2024-11-28 15:32:10', '2024-11-28 15:32:10', 0);
INSERT INTO `sys_menu` VALUES (66, 63, '删除宠物分类', 'BUTTON', '', '', 'pet:category:delete', 1, 1, '', '', 0, 1, 'bcy-sys', 1, 0, 'admin', 'admin', '2024-11-28 15:32:55', '2024-11-28 15:32:55', 0);
INSERT INTO `sys_menu` VALUES (67, 62, '宠物子分类', 'MENU', 'category-data', 'pet/category/data', '', 0, 1, 'el-icon-Bowl', '', 0, 0, 'bcy-sys', 1, 0, 'admin', 'admin', '2024-11-28 15:34:04', '2024-11-28 15:34:04', 0);
INSERT INTO `sys_menu` VALUES (68, 63, '查看分类详情', 'BUTTON', '', '', 'pet:category:detail', 1, 1, '', '', 0, 1, 'bcy-sys', 1, 0, 'admin', 'admin', '2024-11-29 10:53:59', '2024-11-29 10:53:59', 0);
INSERT INTO `sys_menu` VALUES (69, 62, '宠物分类详情', 'MENU', 'category-detail', 'pet/category/detail', '', 0, 1, 'el-icon-Guide', '', 0, 0, 'bcy-sys', 1, 0, 'admin', 'admin', '2024-11-29 10:56:52', '2024-11-29 10:58:13', 2);
INSERT INTO `sys_menu` VALUES (70, 62, '宠物文章', 'MENU', 'content', 'pet/content/index', '', 1, 1, 'el-icon-Memo', '', 0, 1, 'bcy-sys', 1, 0, 'admin', 'admin', '2024-11-29 15:35:54', '2024-11-29 15:35:54', 0);
INSERT INTO `sys_menu` VALUES (71, 70, '新增宠物文章', 'BUTTON', '', '', 'pet:content:add', 1, 1, '', '', 0, 1, 'bcy-sys', 1, 0, 'admin', 'admin', '2024-11-29 15:36:42', '2024-11-29 15:36:42', 0);
INSERT INTO `sys_menu` VALUES (72, 70, '编辑宠物文章', 'BUTTON', '', '', 'pet:content:edit', 1, 2, '', '', 0, 1, 'bcy-sys', 1, 0, 'admin', 'admin', '2024-11-29 15:37:37', '2024-11-29 15:37:37', 0);
INSERT INTO `sys_menu` VALUES (73, 70, '删除宠物文章', 'BUTTON', '', '', 'pet:content:delete', 1, 3, '', '', 0, 1, 'bcy-sys', 1, 0, 'admin', 'admin', '2024-11-29 15:37:54', '2024-11-29 15:37:54', 0);
INSERT INTO `sys_menu` VALUES (74, 62, '宠物文章详情', 'MENU', 'content-data', 'pet/content/data', '', 0, 1, 'el-icon-Reading', '', 0, 0, 'bcy-sys', 1, 0, 'admin', 'admin', '2024-11-29 15:38:54', '2024-11-29 15:38:54', 0);

-- ----------------------------
-- Table structure for sys_message
-- ----------------------------
DROP TABLE IF EXISTS `sys_message`;
CREATE TABLE `sys_message`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '消息Id',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '主题',
  `message` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '消息内容',
  `is_push` tinyint NOT NULL DEFAULT 0 COMMENT '是否推送（0否 1是）',
  `push_time` datetime NULL DEFAULT NULL COMMENT '推送时间',
  `msg_type` tinyint NOT NULL DEFAULT 0 COMMENT '类型（0系统1个人)',
  `notice_type` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '通知类型',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '消息类型',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户Id',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除（0否1是）',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人',
  `update_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_msg_type`(`msg_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统消息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_message
-- ----------------------------
INSERT INTO `sys_message` VALUES (1, '测试', '12345', 1, '2024-11-14 21:11:11', 0, '', '1', 1, 2, 0, '2024-11-12 23:05:32', '2024-11-14 21:34:47', 'admin', 'admin');
INSERT INTO `sys_message` VALUES (2, 'version', '123', 1, '2024-11-14 23:19:37', 0, '', '2', 1, 1, 0, '2024-11-14 21:35:04', '2024-11-14 23:19:37', 'admin', 'admin');

-- ----------------------------
-- Table structure for sys_module
-- ----------------------------
DROP TABLE IF EXISTS `sys_module`;
CREATE TABLE `sys_module`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '模块Id',
  `name` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '模块名称',
  `code` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '模块代码',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '路径',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态（0展示1隐藏）',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '备注',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户Id',
  `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除（0否1是)',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `create_user` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
  `update_user` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '更新人',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '模块表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_module
-- ----------------------------
INSERT INTO `sys_module` VALUES (1, '后台服务', 'bcy-sys', 'https://sys.bcycloud.com/', 0, '博昌云后台管理系统', 1, 0, '2024-11-14 21:26:55', '2024-11-14 21:27:22', 'admin', 'admin', 3);

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色Id',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '角色名称',
  `code` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'USER' COMMENT '角色编码 ADMIN-超级管理员 USER-普通用户 SYSTEM-系统管理员 OTHER-特殊权限用户 TEST-测试人员',
  `remark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '角色备注',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户Id',
  `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除（0否1是）',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `create_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人',
  `update_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_role
-- ----------------------------
INSERT INTO `sys_role` VALUES (1, '测试', 'TEST', '测试人员', 1, 0, 1, 'admin', 'admin', '2024-11-08 22:41:15', '2024-11-08 22:52:22');
INSERT INTO `sys_role` VALUES (2, '演示', 'GUEST', '演示用户，只能查看', 1, 0, 0, 'admin', 'admin', '2024-11-08 22:51:26', '2024-11-08 22:52:15');

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `menu_id` bigint NOT NULL DEFAULT 0 COMMENT '菜单Id',
  `role_id` bigint NOT NULL DEFAULT 0 COMMENT '角色Id',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户Id',
  `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除（0否1是）',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `create_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人',
  `update_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 103 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '菜单角色关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_role_menu
-- ----------------------------
INSERT INTO `sys_role_menu` VALUES (1, 43, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (2, 49, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (3, 44, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (4, 45, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (5, 46, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (6, 47, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (7, 48, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (8, 50, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (9, 52, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (10, 51, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (11, 1, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (12, 2, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (13, 3, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (14, 4, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (15, 5, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (16, 6, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (17, 7, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (18, 8, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (19, 9, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (20, 10, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (21, 15, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (22, 16, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (23, 17, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (24, 18, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (25, 19, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (26, 20, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (27, 21, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (28, 22, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (29, 28, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (30, 27, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (31, 29, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (32, 30, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (33, 31, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (34, 38, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (35, 39, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (36, 40, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (37, 41, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (38, 42, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (39, 11, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (40, 12, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (41, 13, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (42, 14, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (43, 23, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (44, 24, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (45, 25, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (46, 26, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (47, 37, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (48, 33, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (49, 34, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (50, 35, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (51, 36, 1, 1, 1, 0, '2024-11-08 22:49:54', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (52, 50, 1, 1, 1, 0, '2024-11-08 22:50:20', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (53, 52, 1, 1, 1, 0, '2024-11-08 22:50:20', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (54, 51, 1, 1, 1, 0, '2024-11-08 22:50:20', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (55, 1, 1, 1, 1, 0, '2024-11-08 22:50:20', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (56, 2, 1, 1, 1, 0, '2024-11-08 22:50:20', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (57, 3, 1, 1, 1, 0, '2024-11-08 22:50:20', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (58, 4, 1, 1, 1, 0, '2024-11-08 22:50:20', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (59, 5, 1, 1, 1, 0, '2024-11-08 22:50:20', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (60, 6, 1, 1, 1, 0, '2024-11-08 22:50:20', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (61, 7, 1, 1, 1, 0, '2024-11-08 22:50:20', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (62, 8, 1, 1, 1, 0, '2024-11-08 22:50:20', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (63, 9, 1, 1, 1, 0, '2024-11-08 22:50:20', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (64, 10, 1, 1, 1, 0, '2024-11-08 22:50:20', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (65, 15, 1, 1, 1, 0, '2024-11-08 22:50:20', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (66, 16, 1, 1, 1, 0, '2024-11-08 22:50:20', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (67, 17, 1, 1, 1, 0, '2024-11-08 22:50:20', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (68, 18, 1, 1, 1, 0, '2024-11-08 22:50:20', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (69, 19, 1, 1, 1, 0, '2024-11-08 22:50:20', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (70, 20, 1, 1, 1, 0, '2024-11-08 22:50:20', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (71, 21, 1, 1, 1, 0, '2024-11-08 22:50:20', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (72, 22, 1, 1, 1, 0, '2024-11-08 22:50:20', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (73, 28, 1, 1, 1, 0, '2024-11-08 22:50:20', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (74, 27, 1, 1, 1, 0, '2024-11-08 22:50:20', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (75, 29, 1, 1, 1, 0, '2024-11-08 22:50:20', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (76, 30, 1, 1, 1, 0, '2024-11-08 22:50:20', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (77, 31, 1, 1, 1, 0, '2024-11-08 22:50:20', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (78, 38, 1, 1, 1, 0, '2024-11-08 22:50:20', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (79, 39, 1, 1, 1, 0, '2024-11-08 22:50:20', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (80, 40, 1, 1, 1, 0, '2024-11-08 22:50:20', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (81, 41, 1, 1, 1, 0, '2024-11-08 22:50:20', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (82, 42, 1, 1, 1, 0, '2024-11-08 22:50:20', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (83, 11, 1, 1, 1, 0, '2024-11-08 22:50:20', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (84, 12, 1, 1, 1, 0, '2024-11-08 22:50:20', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (85, 13, 1, 1, 1, 0, '2024-11-08 22:50:20', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (86, 14, 1, 1, 1, 0, '2024-11-08 22:50:20', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (87, 23, 1, 1, 1, 0, '2024-11-08 22:50:20', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (88, 24, 1, 1, 1, 0, '2024-11-08 22:50:20', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (89, 25, 1, 1, 1, 0, '2024-11-08 22:50:20', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (90, 26, 1, 1, 1, 0, '2024-11-08 22:50:20', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (91, 37, 1, 1, 1, 0, '2024-11-08 22:50:20', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (92, 33, 1, 1, 1, 0, '2024-11-08 22:50:20', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (93, 34, 1, 1, 1, 0, '2024-11-08 22:50:20', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (94, 35, 1, 1, 1, 0, '2024-11-08 22:50:20', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (95, 36, 1, 1, 1, 0, '2024-11-08 22:50:20', '2024-11-08 22:50:20', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (96, 43, 1, 1, 0, 0, '2024-11-14 23:04:16', '2024-11-14 23:04:16', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (97, 49, 1, 1, 0, 0, '2024-11-14 23:04:16', '2024-11-14 23:04:16', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (98, 44, 1, 1, 0, 0, '2024-11-14 23:04:16', '2024-11-14 23:04:16', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (99, 45, 1, 1, 0, 0, '2024-11-14 23:04:16', '2024-11-14 23:04:16', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (100, 46, 1, 1, 0, 0, '2024-11-14 23:04:16', '2024-11-14 23:04:16', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (101, 47, 1, 1, 0, 0, '2024-11-14 23:04:16', '2024-11-14 23:04:16', 'admin', 'admin');
INSERT INTO `sys_role_menu` VALUES (102, 48, 1, 1, 0, 0, '2024-11-14 23:04:16', '2024-11-14 23:04:16', 'admin', 'admin');

-- ----------------------------
-- Table structure for sys_tenant
-- ----------------------------
DROP TABLE IF EXISTS `sys_tenant`;
CREATE TABLE `sys_tenant`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '租户Id',
  `name` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '租户名称',
  `app_key` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '租户标识符',
  `secret` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '租户应用秘钥',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '备注',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户Id',
  `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除（0否1是)',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `create_user` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
  `update_user` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '更新人',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '租户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_tenant
-- ----------------------------
INSERT INTO `sys_tenant` VALUES (1, '博昌云科技', 'bcy-key', 'e372cd06a63e3b875723c6fda263fc66', '博昌云科技默认租户！', 0, 0, '2024-11-07 21:34:44', '2024-11-07 21:34:44', 'BCY', 'BCY', 0);
INSERT INTO `sys_tenant` VALUES (2, '博昌云测试', 'bcy-test', '0da54386bf3f7507fc63c81a1c402882', '', 1, 0, '2024-11-14 21:28:31', '2024-11-14 21:28:31', 'admin', 'admin', 0);

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户Id',
  `user_name` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '用户名',
  `job_number` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '工号',
  `nick_name` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '姓名',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '是否启用(0是1否)',
  `sex` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '性别',
  `avatar` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '头像路径',
  `type` tinyint NOT NULL DEFAULT 0 COMMENT '类型（0后台1微信2app)',
  `email` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '邮箱',
  `phone` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '手机号',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '密码（md5加密）',
  `open_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT 'openId',
  `login_ip` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '登录IP',
  `login_time` datetime NULL DEFAULT NULL COMMENT '登录时间',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户Id',
  `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除（0否1是）',
  `create_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人',
  `update_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unq_user_name`(`user_name` ASC) USING BTREE,
  INDEX `idx_phone`(`phone` ASC) USING BTREE,
  INDEX `idx_nick_name`(`nick_name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_user
-- ----------------------------
INSERT INTO `sys_user` VALUES (1, 'admin', '', '超级管理员', 0, '1', 'https://api.bcycloud.com/sys/upload/bcy/20240927234628/博昌云logo.png', 0, '<EMAIL>', '', '60e6c8b009a82b8d92c45a43b2667549', '', '110.81.120.192', '2025-01-02 13:52:07', 51, 0, 0, 'BCY', 'BCY', '2024-11-07 21:34:44', '2025-01-02 13:52:07');
INSERT INTO `sys_user` VALUES (2, 'bcy', '0000000002', '博昌云', 0, '2', '', 0, '<EMAIL>', '13205016589', '8ddcff3a80f4189ca1c9d4d902c3c909', '', '127.0.0.1', '2024-11-14 23:19:43', 5, 1, 0, 'admin', 'admin', '2024-11-10 00:22:11', '2024-11-14 23:19:43');
INSERT INTO `sys_user` VALUES (3, 'test', '0000000003', '123', 0, '1', '', 0, '', '13205016581', '8ddcff3a80f4189ca1c9d4d902c3c909', '', '', NULL, 2, 1, 0, 'admin', 'admin', '2024-11-10 00:38:57', '2024-11-14 21:28:53');

-- ----------------------------
-- Table structure for sys_user_info
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_info`;
CREATE TABLE `sys_user_info`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'Id',
  `user_id` bigint NOT NULL DEFAULT 0 COMMENT '用户Id',
  `sign_num` int NOT NULL DEFAULT 0 COMMENT '签到次数',
  `integral` int NOT NULL DEFAULT 0 COMMENT '积分',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户Id',
  `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除（0否1是）',
  `create_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建人',
  `update_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户信息扩展表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_user_info
-- ----------------------------

-- ----------------------------
-- Table structure for sys_user_message
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_message`;
CREATE TABLE `sys_user_message`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户消息关联Id',
  `user_id` bigint NOT NULL DEFAULT 0 COMMENT '用户Id',
  `message_id` bigint NOT NULL DEFAULT 0 COMMENT '消息Id',
  `is_read` tinyint NOT NULL DEFAULT 0 COMMENT '是否已读（0否1是）',
  `read_time` datetime NULL DEFAULT NULL COMMENT '阅读时间',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户Id',
  `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除（0否1是）',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `create_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建者',
  `update_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新者',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户消息关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_user_message
-- ----------------------------
INSERT INTO `sys_user_message` VALUES (1, 3, 1, 0, NULL, 1, 0, 0, 'admin', 'admin', '2024-11-14 21:11:11', '2024-11-14 21:11:11');
INSERT INTO `sys_user_message` VALUES (2, 2, 1, 1, '2024-11-14 23:16:03', 1, 1, 2, 'admin', 'bcy', '2024-11-14 21:11:11', '2024-11-14 23:16:03');
INSERT INTO `sys_user_message` VALUES (3, 2, 2, 1, '2024-11-14 23:42:18', 1, 0, 7, 'admin', 'bcy', '2024-11-14 23:19:37', '2024-11-14 23:42:18');
INSERT INTO `sys_user_message` VALUES (4, 3, 2, 0, NULL, 1, 0, 0, 'admin', 'admin', '2024-11-14 23:19:37', '2024-11-14 23:19:37');

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户角色关联Id',
  `user_id` bigint NOT NULL DEFAULT 0 COMMENT '用户Id',
  `role_id` bigint NOT NULL DEFAULT 0 COMMENT '角色Id',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户Id',
  `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除（0否1是）',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `create_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建者',
  `update_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新者',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户角色关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_user_role
-- ----------------------------
INSERT INTO `sys_user_role` VALUES (1, 2, 1, 1, 1, 0, 'admin', 'admin', '2024-11-10 00:22:11', '2024-11-10 00:22:11');
INSERT INTO `sys_user_role` VALUES (2, 2, 2, 1, 1, 0, 'admin', 'admin', '2024-11-10 00:22:11', '2024-11-10 00:22:11');
INSERT INTO `sys_user_role` VALUES (4, 2, 1, 1, 1, 0, 'admin', 'admin', '2024-11-10 00:24:40', '2024-11-10 00:24:40');
INSERT INTO `sys_user_role` VALUES (5, 3, 1, 1, 1, 0, 'admin', 'admin', '2024-11-10 00:38:57', '2024-11-10 00:38:57');
INSERT INTO `sys_user_role` VALUES (6, 3, 1, 1, 1, 0, 'admin', 'admin', '2024-11-10 01:24:41', '2024-11-10 01:24:41');
INSERT INTO `sys_user_role` VALUES (7, 2, 1, 1, 0, 0, 'admin', 'admin', '2024-11-10 01:24:46', '2024-11-10 01:24:46');
INSERT INTO `sys_user_role` VALUES (8, 3, 1, 1, 0, 0, 'admin', 'admin', '2024-11-14 21:28:53', '2024-11-14 21:28:53');

-- ----------------------------
-- Table structure for sys_user_tenant
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_tenant`;
CREATE TABLE `sys_user_tenant`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户租户关联Id',
  `user_id` bigint NOT NULL DEFAULT 0 COMMENT '用户Id',
  `link_tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '关联租户ID',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户Id',
  `is_delete` tinyint NOT NULL DEFAULT 0 COMMENT '是否删除（0否1是）',
  `version` int NOT NULL DEFAULT 0 COMMENT '版本',
  `create_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '创建者',
  `update_user` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '更新者',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户角色关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sys_user_tenant
-- ----------------------------
INSERT INTO `sys_user_tenant` VALUES (1, 2, 1, 1, 1, 0, 'admin', 'admin', '2024-11-10 00:22:11', '2024-11-10 00:22:11');
INSERT INTO `sys_user_tenant` VALUES (2, 2, 1, 1, 1, 0, 'admin', 'admin', '2024-11-10 00:24:40', '2024-11-10 00:24:40');
INSERT INTO `sys_user_tenant` VALUES (3, 3, 1, 1, 1, 0, 'admin', 'admin', '2024-11-10 00:38:57', '2024-11-10 00:38:57');
INSERT INTO `sys_user_tenant` VALUES (4, 3, 1, 1, 1, 0, 'admin', 'admin', '2024-11-10 01:24:41', '2024-11-10 01:24:41');
INSERT INTO `sys_user_tenant` VALUES (5, 2, 1, 1, 0, 0, 'admin', 'admin', '2024-11-10 01:24:46', '2024-11-10 01:24:46');
INSERT INTO `sys_user_tenant` VALUES (6, 3, 1, 1, 0, 0, 'admin', 'admin', '2024-11-14 21:28:53', '2024-11-14 21:28:53');
INSERT INTO `sys_user_tenant` VALUES (7, 3, 2, 1, 0, 0, 'admin', 'admin', '2024-11-14 21:28:53', '2024-11-14 21:28:53');

SET FOREIGN_KEY_CHECKS = 1;
