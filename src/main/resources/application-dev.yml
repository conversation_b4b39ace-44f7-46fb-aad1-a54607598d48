############## Sa-Token 配置 (文档: https://sa-token.cc) ##############
sa-token:
  # 是否输出操作日志
  is-log: true

spring:
  data:
    redis:
      database: 6
      host: ${bcy.data.ip.address}
      port: 6379
      password: isyfWKmtlu8D
  datasource:
    address: ${bcy.data.ip.address}:3306
    username: bcy
    password: 8MNpxg=1a+Y=
    dynamic:
      primary: admin
  #邮件服务配置
  mail:
    #邮件服务器地址
    host: smtp.qq.com
    #协议
    protocol: smtp
    port: 587
    #发送邮件的邮箱也就是你开通服务的邮箱
    username: <EMAIL>
    #开通服务后得到的授权码
    password: dmohrrkhdmgddada
    # 配置SSL 加密工厂
    properties:
      mail:
        #表示开启 DEBUG 模式，这样，邮件发送过程的日志会在控制台打印出来，方便排查错误
        debug: true

bcy:
  data:
    ip:
      address: **************
  tenant:
    open: true
    tables: sys_module,sys_log,sys_file_log,sys_api_log,sys_api_log_item,sys_tenant,sys_user,sys_user_tenant,sys_menu,sys_config,sys_role,sys_user_role,sys_role_menu,sys_diction,sys_diction_item
  # oss配置
  oss:
    type: local
    bcy-url: https://api.bcycloud.com
    upload-path: /upload/
    aliyun-end-point:
    access-key:
    secret-key:
    bucket-name:
  sms:
    access-key-secret: ******************************
    type: aliyun
    access-key-id: LTAI5tRaxgYJBRxYFwPGKzgR
  wechat:
    token: bcyToken
    app-id: wx59df1d0d48d2e6e6
    secret: 8bf86282e650dd4a57de19686cb4fb1a
  code:
    expirtime: 5
  menu:
    min: 54

######### mybatis-plus 配置 #####
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

####### xxl-job配置 #####
xxl:
  job:
    open: false
    admin:
      # 调度中心部署跟地址 [选填]：如调度中心集群部署存在多个地址则用逗号分隔。执行器将会使用该地址进行"执行器心跳注册"和"任务结果回调"；为空则关闭自动注册；
      addresses: https://127.0.0.1:8081/bcy-job
    executor:
      # 执行器AppName [选填]：执行器心跳注册分组依据；为空则关闭自动注册
      appname: bcy-task-dev
      # 执行器运行日志文件存储磁盘路径 [选填] ：需要对该路径拥有读写权限；为空则使用默认路径；
      logpath: /bcy/logs/bcy-sys/job