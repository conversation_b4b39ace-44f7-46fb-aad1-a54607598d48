#开启端点监控
management:
  endpoints:
    web:
      cors:
        allowed-headers: "*"
        allowed-origins: "*"
      exposure:
        # 开放所有端点health，info，metrics，通过actuator/+端点名就可以获取相应的信息。默认打开health和info
        include: "*"
    enabled-by-default: true
  endpoint:
    health:
      show-details: always

# knife4j的增强配置，不需要增强可以不配
knife4j:
  enable: true
  setting:
    language: zh_cn

springdoc:
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  group-configs:
    - group: ${spring.application.name}
      paths-to-match: '/**'
      packages-to-scan: com.bcy
  api-docs:
    enabled: true
    path: /v3/api-docs

######### mybatis-plus 配置 #####
mybatis-plus:
  # 扫描xml文件路径
  mapper-locations: classpath*:/mapper/**/**/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.*.entity
  global-config:
    # 允许动态sql执行
    enable-sql-runner: true
    # 去除banner展示
    banner: false
    db-config:
      # id采用自增方式进行增加
      id-type: auto
      logic-delete-value: 1   #默认1为删除
      logic-not-delete-value: 0   #默认0为未删除
      table-underline: true
      # 更新情况下可以更新null值
      update-strategy: not_null
      logic-delete-field: is_delete
  configuration:
    # 开启自动驼峰命名规则映射
    map-underscore-to-camel-case: true
    jdbc-type-for-null: null
    call-setters-on-nulls: true


############## Sa-Token 配置 (文档: https://sa-token.cc) ##############
sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: Authorization
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效
  timeout: 259200
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
  active-timeout: -1
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  is-share: true
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: uuid
  # token前缀
  token-prefix: Bearer
  # jwt秘钥
  jwt-secret-key: bcy-token-thinker

spring:
  application:
    name: bcy-admin
  profiles:
    active: dev
  main:
    banner-mode: off
  servlet:
    multipart:
      # 大于该值的文件会被写到磁盘上
      file-size-threshold: 1024MB
      # 最大文件大小
      max-file-size: 10240MB
      # 最大请求大小
      max-request-size: 1024MB
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  ######## redis配置 #############
  data:
    redis:
      timeout: 6000                # 连接超时时长（毫秒）
      jedis:
        pool:
          max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
          max-wait: -1      # 连接池最大阻塞等待时间（使用负值表示没有限制）
          max-idle: 10      # 连接池中的最大空闲连接
          min-idle: 5       # 连接池中的最小空闲连接
  ######## 数据库配置 #############
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      # 连接建立超时时间 单位ms 默认30000
      connection-timeout: 30000
      validation-timeout: 3000
      # 空闲连接超时时间 单位ms 最小10000(10s) 默认600000(10min)
      idle-timeout: 60000
      login-timeout: 5
      # 最大生存期
      max-lifetime: 60000
      # 数据库后端的最大实际连接数
      maximum-pool-size: 10
      minimum-idle: 5
      # 只读模式：默认false
      read-only: false
    dynamic:
      datasource:
        admin:
          type: ${spring.datasource.type}
          driver-class-name: ${spring.datasource.driver-class-name}
          url: jdbc:mysql://${spring.datasource.address}/bcy_project?allowMultiQueries=true&useUnicode=true&characterEncoding=UTF-8&useSSL=true&serverTimezone=Asia/Shanghai&nullCatalogMeansCurrent=true
          username: ${spring.datasource.username}
          password: ${spring.datasource.password}

  #邮件服务配置
  mail:
    #邮件内容的编码
    default-encoding: utf-8
    # 配置SSL 加密工厂
    properties:
      mail:
        smtp:
          socketFactoryClass: javax.net.ssl.SSLSocketFactory
          starttls:
            enable: true
            required: true
bcy:
  auth:
    ignore-url: /doc.html,/api/**,/static/**,/v3/**,/actuator/**,/webjars/**,/login/**,/upload/**,/wechat/**,/favicon.ico

####### xxl-job配置 #####
xxl:
  job:
    # 执行器通讯TOKEN [选填]：非空时启用；
    accessToken: bcy_token
    executor:
      # 执行器注册 [选填]：优先使用该配置作为注册地址，为空时使用内嵌服务 ”IP:PORT“ 作为注册地址。从而更灵活的支持容器类型执行器动态IP和动态映射端口问题。
      address:
      # 执行器IP [选填]：默认为空表示自动获取IP，多网卡时可手动设置指定IP，该IP不会绑定Host仅作为通讯实用；地址信息用于 “执行器注册” 和 “调度中心请求并触发任务”；
      ip:
      # 执行器端口号 [选填]：小于等于0则自动获取；默认端口为9999，单机部署多个执行器时，注意要配置不同执行器端口；
      port: -1
      # 执行器日志文件保存天数 [选填] ： 过期日志自动清理, 限制值大于等于3时生效; 否则, 如-1, 关闭自动清理功能；
      logretentiondays: 30
server:
  servlet:
    context-path: /sys
