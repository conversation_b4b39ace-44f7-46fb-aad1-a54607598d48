package com.bcy.service.site.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcy.entity.site.BcySiteColumnCategoryEntity;
import com.bcy.mapper.site.BcySiteColumnCategoryMapper;
import com.bcy.service.site.BcySiteColumnCategoryService;
import org.springframework.stereotype.Service;

/**
 * 针对表【bcy_site_column_category(网站栏目分类表)】的数据库操作Service实现
 *
 * <AUTHOR>
 * @since 2024-11-13 15:29:11
 */
@Service
public class BcySiteColumnCategoryServiceImpl extends ServiceImpl<BcySiteColumnCategoryMapper, BcySiteColumnCategoryEntity>
        implements BcySiteColumnCategoryService {

}




