package com.bcy.service.site.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcy.entity.site.BcySiteConfigEntity;
import com.bcy.mapper.site.BcySiteConfigMapper;
import com.bcy.service.site.BcySiteConfigService;
import org.springframework.stereotype.Service;

/**
 * 针对表【bcy_site_config(网站配置表)】的数据库操作Service实现
 *
 * <AUTHOR>
 * @since 2024-11-13 15:29:11
 */
@Service
public class BcySiteConfigServiceImpl extends ServiceImpl<BcySiteConfigMapper, BcySiteConfigEntity>
        implements BcySiteConfigService {

}




