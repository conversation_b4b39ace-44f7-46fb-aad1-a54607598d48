package com.bcy.service.log;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bcy.domain.request.PageRequest;
import com.bcy.entity.log.SysFileLogEntity;

/**
 * 针对表【sys_file_log(文件日志表)】的数据库操作Service
 *
 * <AUTHOR>
 * @since 2023-08-22 16:35:28
 */
public interface ISysFileLogService extends IService<SysFileLogEntity> {
    /**
     * 分页获取文件列表
     *
     * @param PageRequest 分页表单
     * @param userId      用户Id
     * @return 分页列表
     */
    IPage<SysFileLogEntity> fileLogPage(PageRequest PageRequest, Long userId);

    /**
     * 根据文件路径删除相应记录
     *
     * @param path 文件路径
     * @return 数量
     */
    int deleteFileLogs(String path);

    /**
     * 根据url获取文件日志
     *
     * @param url 路径
     * @return 文件日志
     */
    SysFileLogEntity getInfoByUrl(String url);
}
