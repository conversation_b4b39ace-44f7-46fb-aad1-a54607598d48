package com.bcy.service.log;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bcy.entity.log.SysApiLogItemEntity;

/**
 * 针对表【sys_api_log_item(API日志详情表)】的数据库操作Service
 *
 * <AUTHOR>
 * @since 2024-07-30 13:36:05
 */
public interface ISysApiLogItemService extends IService<SysApiLogItemEntity> {
    /**
     * 根据日志ID获取详情
     *
     * @param logId 日志ID
     * @return 日志详情
     */
    SysApiLogItemEntity getInfoByLogId(Long logId);

    /**
     * 删除n天前的api日志明细信息
     *
     * @param days 天数
     * @return 删除数量
     */
    int deleteItemLogs(Integer days);
}
