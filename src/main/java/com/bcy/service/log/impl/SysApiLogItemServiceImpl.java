package com.bcy.service.log.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcy.entity.log.SysApiLogItemEntity;
import com.bcy.mapper.log.SysApiLogItemMapper;
import com.bcy.service.log.ISysApiLogItemService;
import com.bcy.utils.BcyDateUtils;
import org.springframework.stereotype.Service;

import java.util.Date;

import static com.bcy.constants.MybatisConstants.QUERY_FIRST;

/**
 * 针对表【sys_api_log_item(API日志详情表)】的数据库操作Service实现
 *
 * <AUTHOR>
 * @since 2024-07-30 13:36:05
 */
@Service
public class SysApiLogItemServiceImpl extends ServiceImpl<SysApiLogItemMapper, SysApiLogItemEntity>
        implements ISysApiLogItemService {

    @Override
    public SysApiLogItemEntity getInfoByLogId(Long logId) {
        return baseMapper.selectOne(new LambdaQueryWrapper<SysApiLogItemEntity>().eq(SysApiLogItemEntity::getLogId, logId)
                .last(QUERY_FIRST));
    }

    @Override
    public int deleteItemLogs(Integer days) {
        return baseMapper.delete(new LambdaQueryWrapper<SysApiLogItemEntity>().le(
                SysApiLogItemEntity::getCreateTime,
                BcyDateUtils.addDateSeconds(new Date(), days * 24 * 3600))
        );
    }
}




