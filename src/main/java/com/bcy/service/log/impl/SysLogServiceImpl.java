package com.bcy.service.log.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcy.domain.request.PageRequest;
import com.bcy.entity.log.SysLogEntity;
import com.bcy.mapper.log.SysLogMapper;
import com.bcy.service.log.ISysLogService;
import com.bcy.utils.BcyDateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

/**
 * 日志表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-07-30 13:36:05
 */
@Service
public class SysLogServiceImpl extends ServiceImpl<SysLogMapper, SysLogEntity> implements ISysLogService {

    @Override
    public IPage<SysLogEntity> sysLogPage(PageRequest PageRequest, Long minId) {
        IPage<SysLogEntity> pages = new Page<>(PageRequest.getPage(), PageRequest.getLimit(), Objects.isNull(minId));
        return baseMapper.selectPage(pages,
                new LambdaQueryWrapper<SysLogEntity>()
                        .between(StringUtils.isNotBlank(PageRequest.getBeginTime()) && StringUtils.isNotBlank(PageRequest.getEndTime())
                                , SysLogEntity::getCreateDate, PageRequest.getBeginTime(), PageRequest.getEndTime())
                        .like(StringUtils.isNotBlank(PageRequest.getName()), SysLogEntity::getOperation, PageRequest.getName())
                        .lt(Objects.nonNull(minId), SysLogEntity::getId, minId)
                        .orderByDesc(SysLogEntity::getCreateDate));
    }

    @Override
    public int deleteLogs(Integer days) {
        return baseMapper.delete(new LambdaQueryWrapper<SysLogEntity>().le(
                SysLogEntity::getCreateDate,
                BcyDateUtils.addDateSeconds(new Date(), days * 24 * 3600))
        );
    }
}
