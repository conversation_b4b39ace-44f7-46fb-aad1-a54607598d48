package com.bcy.service.log.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcy.domain.request.PageRequest;
import com.bcy.entity.log.SysFileLogEntity;
import com.bcy.mapper.log.SysFileLogMapper;
import com.bcy.service.log.ISysFileLogService;
import com.bcy.utils.BcyUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

import static com.bcy.constants.MybatisConstants.QUERY_FIRST;

/**
 * 针对表【sys_file_log(文件日志表)】的数据库操作Service实现
 *
 * <AUTHOR>
 * @since 2023-08-22 16:35:28
 */
@Service
public class SysFileLogServiceImpl extends ServiceImpl<SysFileLogMapper, SysFileLogEntity> implements ISysFileLogService {

    @Override
    public IPage<SysFileLogEntity> fileLogPage(PageRequest PageRequest, Long userId) {
        IPage<SysFileLogEntity> pages = new Page<>(PageRequest.getPage(), PageRequest.getLimit());
        return baseMapper.selectPage(pages, new LambdaQueryWrapper<SysFileLogEntity>()
                .eq(!BcyUtils.isSuperAdmin(userId), SysFileLogEntity::getUserId, userId)
                .eq(Objects.nonNull(PageRequest.getStatus()), SysFileLogEntity::getStatus, PageRequest.getStatus())
                .like(StringUtils.isNotBlank(PageRequest.getName()), SysFileLogEntity::getFileName, PageRequest.getName())
                .between(StringUtils.isNotBlank(PageRequest.getBeginTime()) && StringUtils.isNotBlank(PageRequest.getEndTime()), SysFileLogEntity::getCreateTime
                        , PageRequest.getBeginTime(), PageRequest.getEndTime())
                .orderByDesc(SysFileLogEntity::getCreateTime)
        );
    }

    @Override
    public int deleteFileLogs(String path) {
        return baseMapper.delete(new LambdaQueryWrapper<SysFileLogEntity>()
                .eq(SysFileLogEntity::getFilePath, path));
    }

    @Override
    public SysFileLogEntity getInfoByUrl(String url) {
        return baseMapper.selectOne(new LambdaQueryWrapper<SysFileLogEntity>()
                .eq(SysFileLogEntity::getFileUrl, url)
                .last(QUERY_FIRST));
    }
}




