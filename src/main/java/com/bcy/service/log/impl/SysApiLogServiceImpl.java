package com.bcy.service.log.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcy.domain.request.PageRequest;
import com.bcy.entity.log.SysApiLogEntity;
import com.bcy.mapper.log.SysApiLogMapper;
import com.bcy.service.log.ISysApiLogService;
import com.bcy.utils.BcyDateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

/**
 * 针对表【sys_api_log(API日志表)】的数据库操作Service实现
 *
 * <AUTHOR>
 * @since 2024-07-30 13:36:05
 */
@Service
public class SysApiLogServiceImpl extends ServiceImpl<SysApiLogMapper, SysApiLogEntity>
        implements ISysApiLogService {

    @Override
    public IPage<SysApiLogEntity> sysApiLogPage(PageRequest PageRequest, Long minId) {
        IPage<SysApiLogEntity> pages = new Page<>(PageRequest.getPage(), PageRequest.getLimit(), Objects.isNull(minId));
        return baseMapper.selectPage(pages,
                new LambdaQueryWrapper<SysApiLogEntity>()
                        .between(StringUtils.isNotBlank(PageRequest.getBeginTime()) && StringUtils.isNotBlank(PageRequest.getEndTime()),
                                SysApiLogEntity::getCreateTime, PageRequest.getBeginTime(), PageRequest.getEndTime())
                        .like(StringUtils.isNotBlank(PageRequest.getName()), SysApiLogEntity::getOperatorName, PageRequest.getName())
                        .eq(Objects.nonNull(PageRequest.getStatus()), SysApiLogEntity::getStatus, PageRequest.getStatus())
                        .lt(Objects.nonNull(minId), SysApiLogEntity::getId, minId)
                        .orderByDesc(SysApiLogEntity::getCreateTime));
    }

    @Override
    public int deleteLogs(Integer days) {
        return baseMapper.delete(new LambdaQueryWrapper<SysApiLogEntity>().le(
                SysApiLogEntity::getCreateTime,
                BcyDateUtils.addDateSeconds(new Date(), days * 24 * 3600))
        );
    }
}




