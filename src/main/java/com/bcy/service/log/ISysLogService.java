package com.bcy.service.log;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bcy.domain.request.PageRequest;
import com.bcy.entity.log.SysLogEntity;

/**
 * 日志表 服务类
 *
 * <AUTHOR>
 * @since 2023-08-22 16:35:28
 */
public interface ISysLogService extends IService<SysLogEntity> {
    /**
     * 日志分页数据
     *
     * @param PageRequest 分页表单
     * @param minId      最小ID
     * @return 分页列表
     */
    IPage<SysLogEntity> sysLogPage(PageRequest PageRequest, Long minId);

    /**
     * 删除n天前的日志信息
     *
     * @param days 天数
     * @return 删除数量
     */
    int deleteLogs(Integer days);
}
