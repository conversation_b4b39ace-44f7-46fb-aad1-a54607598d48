package com.bcy.service.log;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bcy.domain.request.PageRequest;
import com.bcy.entity.log.SysApiLogEntity;

/**
 * 针对表【sys_api_log(API日志表)】的数据库操作Service
 *
 * <AUTHOR>
 * @since 2024-07-30 13:36:05
 */
public interface ISysApiLogService extends IService<SysApiLogEntity> {
    /**
     * API日志分页数据
     *
     * @param PageRequest 分页表单
     * @param minId      最小ID
     * @return 分页列表
     */
    IPage<SysApiLogEntity> sysApiLogPage(PageRequest PageRequest, Long minId);

    /**
     * 删除n天前的日志信息
     *
     * @param days 天数
     * @return 删除数量
     */
    int deleteLogs(Integer days);
}
