package com.bcy.service.web;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bcy.domain.request.web.BcyCustomerPageRequest;
import com.bcy.entity.web.BcyCustomerEntity;

/**
 * 针对表【bcy_customer(客户记录表)】的数据库操作Service
 *
 * <AUTHOR>
 * @since 2024-09-13 16:45:57
 */
public interface ICustomerService extends IService<BcyCustomerEntity> {

    /**
     * 分页获取留资客户列表
     *
     * @param pageRequest 分页表单
     * @param minId       最小ID
     * @return 分页表单
     */
    IPage<BcyCustomerEntity> pageList(BcyCustomerPageRequest pageRequest, Long minId);

    /**
     * 根据客户手机号码获取客户信息
     *
     * @param phone  客户手机号
     * @param siteId 站点ID
     * @return 客户信息
     */
    BcyCustomerEntity getCustomerInfoByPhone(String phone, Long siteId);
}
