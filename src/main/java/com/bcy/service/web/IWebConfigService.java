package com.bcy.service.web;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bcy.domain.request.PageRequest;
import com.bcy.entity.web.BcyWebConfigEntity;

/**
 * 针对表【bcy_site_config(留资配置表)】的数据库操作Service
 *
 * <AUTHOR>
 * @since 2024-09-13 16:45:57
 */
public interface IWebConfigService extends IService<BcyWebConfigEntity> {

    /**
     * 分页获取留资站点列表
     *
     * @param pageRequest 分页表单
     * @param minId    最小ID
     * @return 分页表单
     */
    IPage<BcyWebConfigEntity> pageList(PageRequest pageRequest, Long minId);

    /**
     * 根据名称和平台获取站点熟练
     *
     * @param siteName     名称
     * @param platformType 平台类型
     * @return 数量
     */
    long countByNameAndPlatform(String siteName, String platformType);
}
