package com.bcy.service.web.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcy.domain.request.PageRequest;
import com.bcy.entity.web.BcyWebConfigEntity;
import com.bcy.enums.StatusEnum;
import com.bcy.mapper.web.BcyWebConfigMapper;
import com.bcy.service.web.IWebConfigService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 针对表【bcy_site_config(留资配置表)】的数据库操作Service实现
 *
 * <AUTHOR>
 * @since 2024-09-13 16:45:57
 */
@Service
public class WebConfigServiceImpl extends ServiceImpl<BcyWebConfigMapper, BcyWebConfigEntity>
        implements IWebConfigService {

    @Override
    public IPage<BcyWebConfigEntity> pageList(PageRequest pageRequest, Long minId) {
        IPage<BcyWebConfigEntity> pages = new Page<>(pageRequest.getPage(), pageRequest.getLimit());
        return baseMapper.selectPage(pages,
                new LambdaQueryWrapper<BcyWebConfigEntity>()
                        .eq(Objects.nonNull(pageRequest.getStatus()), BcyWebConfigEntity::getStatus, pageRequest.getStatus())
                        .eq(StringUtils.isNotBlank(pageRequest.getType()), BcyWebConfigEntity::getPlatformType, pageRequest.getType())
                        .like(StringUtils.isNotBlank(pageRequest.getName()), BcyWebConfigEntity::getSiteName, pageRequest.getName())
                        .between(StringUtils.isNotBlank(pageRequest.getBeginTime()) && StringUtils.isNotBlank(pageRequest.getEndTime()),
                                BcyWebConfigEntity::getCreateTime, pageRequest.getBeginTime(), pageRequest.getEndTime())
                        .lt(Objects.nonNull(minId), BcyWebConfigEntity::getId, minId)
                        .orderByDesc(BcyWebConfigEntity::getCreateTime));
    }

    @Override
    public long countByNameAndPlatform(String siteName, String platformType) {
        return baseMapper.selectCount(new LambdaQueryWrapper<BcyWebConfigEntity>()
                .eq(BcyWebConfigEntity::getStatus, StatusEnum.ENABLE.getCode())
                .eq(BcyWebConfigEntity::getSiteName, siteName)
                .eq(BcyWebConfigEntity::getPlatformType, platformType));
    }
}




