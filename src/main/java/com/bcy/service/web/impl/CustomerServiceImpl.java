package com.bcy.service.web.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcy.constants.MybatisConstants;
import com.bcy.domain.request.web.BcyCustomerPageRequest;
import com.bcy.entity.web.BcyCustomerEntity;
import com.bcy.mapper.web.BcyCustomerMapper;
import com.bcy.service.web.ICustomerService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 针对表【bcy_customer(客户记录表)】的数据库操作Service实现
 *
 * <AUTHOR>
 * @since 2024-09-13 16:45:57
 */
@Service
public class CustomerServiceImpl extends ServiceImpl<BcyCustomerMapper, BcyCustomerEntity>
        implements ICustomerService {

    @Override
    public IPage<BcyCustomerEntity> pageList(BcyCustomerPageRequest pageRequest, Long minId) {
        IPage<BcyCustomerEntity> pages = new Page<>(pageRequest.getPage(), pageRequest.getLimit());
        return baseMapper.selectPage(pages,
                new LambdaQueryWrapper<BcyCustomerEntity>()
                        .eq(Objects.nonNull(pageRequest.getStatus()), BcyCustomerEntity::getStatus, pageRequest.getStatus())
                        .eq(Objects.nonNull(pageRequest.getAppId()), BcyCustomerEntity::getSiteId, pageRequest.getAppId())
                        .eq(StringUtils.isNotBlank(pageRequest.getType()), BcyCustomerEntity::getPlatformType, pageRequest.getType())
                        .eq(StringUtils.isNotBlank(pageRequest.getAdsId()), BcyCustomerEntity::getAdsId, pageRequest.getAdsId())
                        .like(StringUtils.isNotBlank(pageRequest.getName()), BcyCustomerEntity::getPhone, pageRequest.getName())
                        .between(StringUtils.isNotBlank(pageRequest.getBeginTime()) && StringUtils.isNotBlank(pageRequest.getEndTime()),
                                BcyCustomerEntity::getCreateTime, pageRequest.getBeginTime(), pageRequest.getEndTime())
                        .lt(Objects.nonNull(minId), BcyCustomerEntity::getId, minId)
                        .orderByDesc(BcyCustomerEntity::getCreateTime));
    }

    @Override
    public BcyCustomerEntity getCustomerInfoByPhone(String phone, Long siteId) {
        return baseMapper.selectOne(new LambdaQueryWrapper<BcyCustomerEntity>()
                .eq(BcyCustomerEntity::getPhone, phone)
                .eq(BcyCustomerEntity::getSiteId, siteId)
                .orderByDesc(BcyCustomerEntity::getCreateTime)
                .last(MybatisConstants.QUERY_FIRST)
        );
    }
}




