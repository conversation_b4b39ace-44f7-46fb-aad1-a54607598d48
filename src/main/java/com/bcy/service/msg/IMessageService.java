package com.bcy.service.msg;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bcy.domain.request.MessagePageRequest;
import com.bcy.entity.msg.SysMessageEntity;

import java.util.List;

/**
 * 针对表【sys_message(信息管理)】的数据库操作Service
 *
 * <AUTHOR>
 * @since 2024-10-04
 */
public interface IMessageService extends IService<SysMessageEntity> {
    /**
     * 分页获取消息列表
     *
     * @param messagePageRequest 分页表单
     * @param messageIdList      消息列表
     * @return 分页列表
     */
    IPage<SysMessageEntity> messagePage(MessagePageRequest messagePageRequest, List<Long> messageIdList);
}
