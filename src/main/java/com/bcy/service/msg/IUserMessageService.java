package com.bcy.service.msg;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bcy.entity.msg.SysUserMessageEntity;

import java.util.List;

/**
 * 针对表【sys_user_message(用户信息管理)】的数据库操作Service
 *
 * <AUTHOR>
 * @since 2023/11/11
 */
public interface IUserMessageService extends IService<SysUserMessageEntity> {
    /**
     * 根据用户ID获取用户消息关联列表
     *
     * @param userId 用户Id
     * @param isRead 阅读状态
     * @return 关联列表
     */
    List<SysUserMessageEntity> userMessageListByUserId(Long userId, Integer isRead);

    /**
     * 根据用户ID和消息ID获取关联数据
     *
     * @param userId    用户ID
     * @param messageId 消息ID
     * @return 关联列表
     */
    List<SysUserMessageEntity> userMessageListByUserIdAndMessageId(Long userId, Long messageId);


    /**
     * 一键清除用户已读信息
     *
     * @param loginIdAsLong 登陆用户
     * @param status        状态
     * @return true/false
     */
    boolean removeByUserId(long loginIdAsLong, Integer status);

    /**
     * 删除用户关联信息列表
     *
     * @param userId 登陆用户
     * @param ids    信息ID列表
     * @return true/false
     */
    boolean deleteMsgLink(List<Long> ids, Long userId);

    /**
     * 根据消息ID删除关联信息
     *
     * @param ids 消息id数组
     */
    void removeByMsgIds(List<Long> ids);
}
