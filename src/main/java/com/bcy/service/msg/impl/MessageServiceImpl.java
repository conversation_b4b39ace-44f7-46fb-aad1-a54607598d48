package com.bcy.service.msg.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcy.domain.request.MessagePageRequest;
import com.bcy.entity.msg.SysMessageEntity;
import com.bcy.mapper.msg.SysMessageMapper;
import com.bcy.service.msg.IMessageService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 针对表【sys_message(信息管理)】的数据库操作Service实现
 *
 * <AUTHOR>
 * @since 2024-10-04
 */
@Service
public class MessageServiceImpl extends ServiceImpl<SysMessageMapper, SysMessageEntity> implements IMessageService {

    @Override
    public IPage<SysMessageEntity> messagePage(MessagePageRequest messagePageRequest, List<Long> messageIdList) {
        IPage<SysMessageEntity> pages = new Page<>(messagePageRequest.getPage(), messagePageRequest.getLimit());
        return baseMapper.selectPage(pages, new LambdaQueryWrapper<SysMessageEntity>()
                .like(StringUtils.isNotBlank(messagePageRequest.getName()), SysMessageEntity::getTitle, messagePageRequest.getName())
                .eq(Objects.nonNull(messagePageRequest.getIsPush()), SysMessageEntity::getIsPush, messagePageRequest.getIsPush())
                .eq(Objects.nonNull(messagePageRequest.getMsgType()), SysMessageEntity::getMsgType, messagePageRequest.getMsgType())
                .eq(StringUtils.isNotBlank(messagePageRequest.getType()), SysMessageEntity::getType, messagePageRequest.getType())
                .eq(StringUtils.isNotBlank(messagePageRequest.getNoticeType()), SysMessageEntity::getNoticeType, messagePageRequest.getNoticeType())
                .in(messagePageRequest.getIsManage() == 1, SysMessageEntity::getId, messageIdList)
                .between(StringUtils.isNotBlank(messagePageRequest.getBeginTime()) && StringUtils.isNotBlank(messagePageRequest.getEndTime()), SysMessageEntity::getCreateTime, messagePageRequest.getBeginTime(), messagePageRequest.getEndTime())
                .orderByDesc(SysMessageEntity::getCreateTime)
        );
    }
}




