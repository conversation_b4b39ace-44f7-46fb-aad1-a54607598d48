package com.bcy.service.msg.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcy.entity.msg.SysUserMessageEntity;
import com.bcy.admin.enums.ReadStatusEnum;
import com.bcy.mapper.msg.SysUserMessageMapper;
import com.bcy.service.msg.IUserMessageService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;


/**
 * 针对表【sys_user_message(用户信息管理)】的数据库操作Service实现
 *
 * <AUTHOR>
 * @since 2023/11/11
 */
@Service
public class UserMessageServiceImpl extends ServiceImpl<SysUserMessageMapper, SysUserMessageEntity> implements IUserMessageService {

    @Override
    public List<SysUserMessageEntity> userMessageListByUserId(Long userId, Integer isRead) {
        return baseMapper.selectList(new LambdaQueryWrapper<SysUserMessageEntity>()
                .eq(SysUserMessageEntity::getUserId, userId)
                .eq(Objects.nonNull(isRead), SysUserMessageEntity::getIsRead, isRead)
                .orderByAsc(SysUserMessageEntity::getIsRead)
                .orderByDesc(SysUserMessageEntity::getCreateTime)
        );
    }

    @Override
    public List<SysUserMessageEntity> userMessageListByUserIdAndMessageId(Long userId, Long messageId) {
        return baseMapper.selectList(new LambdaQueryWrapper<SysUserMessageEntity>()
                .eq(SysUserMessageEntity::getUserId, userId)
                .eq(SysUserMessageEntity::getMessageId, messageId)
                .orderByAsc(SysUserMessageEntity::getIsRead)
                .orderByDesc(SysUserMessageEntity::getCreateTime)
        );
    }

    @Override
    public boolean removeByUserId(long loginIdAsLong, Integer status) {
        return baseMapper.delete(new LambdaQueryWrapper<SysUserMessageEntity>()
                .eq(SysUserMessageEntity::getUserId, loginIdAsLong)
                .eq(SysUserMessageEntity::getIsRead, status)) > 0;
    }

    @Override
    public boolean deleteMsgLink(List<Long> ids, Long userId) {
        return baseMapper.delete(new LambdaQueryWrapper<SysUserMessageEntity>()
                .eq(SysUserMessageEntity::getUserId, userId)
                .eq(SysUserMessageEntity::getIsRead, ReadStatusEnum.HAS_READ.getCode())
                .in(SysUserMessageEntity::getMessageId, ids)) > 0;
    }

    @Override
    public void removeByMsgIds(List<Long> ids) {
        baseMapper.delete(new LambdaQueryWrapper<SysUserMessageEntity>().in(SysUserMessageEntity::getMessageId, ids));
    }
}




