package com.bcy.service.pet;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bcy.domain.request.PageRequest;
import com.bcy.entity.pet.BcyPetContentEntity;

/**
 * 针对表【bcy_pet_content(文章)】的数据库操作Service
 *
 * <AUTHOR>
 * @since 2024-10-25 09:43:48
 */
public interface IPetContentService extends IService<BcyPetContentEntity> {

    /**
     * 获取文章分页列表
     *
     * @param pageRequest 分页请求
     * @return 分页列表
     */
    IPage<BcyPetContentEntity> pageList(PageRequest pageRequest);

    /**
     * 根据文章标题获取数量
     *
     * @param title 文章标题
     * @return 数量
     */
    long countByTitle(String title);
}
