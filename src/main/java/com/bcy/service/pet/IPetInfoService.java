package com.bcy.service.pet;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bcy.domain.request.PageRequest;
import com.bcy.entity.pet.BcyPetInfoEntity;

import java.util.List;

/**
 * 针对表【bcy_pet_info(用户宠物信息表)】的数据库操作Service
 *
 * <AUTHOR>
 * @since 2024-10-29 16:12:08
 */
public interface IPetInfoService extends IService<BcyPetInfoEntity> {
    /**
     * 获取用户宠物分页列表
     *
     * @param pageRequest 分页请求
     * @return 分页列表
     */
    IPage<BcyPetInfoEntity> pageList(PageRequest pageRequest);

    /**
    * 根据用户ID获取宠物列表
     * @param userId 用户ID
     * @return  列表
    * */
    List<BcyPetInfoEntity> getByUserId(long userId);
}
