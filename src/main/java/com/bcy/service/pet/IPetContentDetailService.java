package com.bcy.service.pet;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bcy.entity.pet.BcyPetContentDetailEntity;

import java.util.List;

/**
 * 针对表【bcy_pet_content_detail(文章详情)】的数据库操作Service
 *
 * <AUTHOR>
 * @since 2024-10-25 09:43:48
 */
public interface IPetContentDetailService extends IService<BcyPetContentDetailEntity> {

    /**
     * 根据文章Id获取详情
     *
     * @param contentId 文章ID
     * @return 文章详情
     */
    BcyPetContentDetailEntity getByContentId(Long contentId);

    /**
     * 根据文章ID数组批量删除详情
     *
     * @param ids 文章id数组
     */
    void removeByContentIds(List<Long> ids);
}
