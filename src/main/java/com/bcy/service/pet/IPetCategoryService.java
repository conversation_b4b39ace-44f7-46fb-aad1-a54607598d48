package com.bcy.service.pet;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bcy.domain.request.PageRequest;
import com.bcy.entity.pet.BcyPetCategoryEntity;

import java.util.List;

/**
 * 针对表【bcy_pet_category(宠物分类)】的数据库操作Service
 *
 * <AUTHOR>
 * @since 2024-10-25 09:43:48
 */
public interface IPetCategoryService extends IService<BcyPetCategoryEntity> {
    /**
     * 分页获取宠物分类列表
     *
     * @param pageRequest 分页表单
     * @return 分页列表
     */
    IPage<BcyPetCategoryEntity> pageList(PageRequest pageRequest);

    /**
     * 根据父级ID获取子列表
     *
     * @param parentId 父级Id
     * @return 子级列表
     */
    List<BcyPetCategoryEntity> getListByParentId(Long parentId);

    /**
     * 根据名称获取数量
     *
     * @param name 名称
     * @return 数量
     */
    long countByName(String name);
}
