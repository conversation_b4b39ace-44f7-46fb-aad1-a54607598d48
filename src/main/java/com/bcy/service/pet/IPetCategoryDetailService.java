package com.bcy.service.pet;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bcy.domain.request.PageRequest;
import com.bcy.entity.pet.BcyPetCategoryDetailEntity;

import java.util.List;

/**
 * 针对表【bcy_pet_category_detail(类型介绍表)】的数据库操作Service
 *
 * <AUTHOR>
 * @since 2024-10-28 14:54:59
 */
public interface IPetCategoryDetailService extends IService<BcyPetCategoryDetailEntity> {

    /**
     * 根据分类ID获取详情分页列表
     *
     * @param pageRequest 分页请求
     * @return 分页列表
     */
    IPage<BcyPetCategoryDetailEntity> pageList(PageRequest pageRequest);

    /**
     * 根据分类ID和标签获取详情数量
     *
     * @param categoryId 分类ID
     * @param tags       标签
     * @return 数量
     */
    long countByCategoryIdAndTags(Long categoryId, String tags);

    /**
     * 根据分类ID和标签获取详情列表
     *
     * @param categoryId 分类ID
     * @return 详情
     */
    List<BcyPetCategoryDetailEntity> getByCategoryId(Long categoryId);

    /**
     * 根据分类Id删除详情
     *
     * @param id 分类ID
     */
    void deleteByCategoryId(Long id);
}
