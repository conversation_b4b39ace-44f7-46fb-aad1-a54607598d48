package com.bcy.service.pet.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcy.constants.MybatisConstants;
import com.bcy.entity.pet.BcyPetContentDetailEntity;
import com.bcy.mapper.pet.BcyPetContentDetailMapper;
import com.bcy.service.pet.IPetContentDetailService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 针对表【bcy_pet_content_detail(文章详情)】的数据库操作Service实现
 *
 * <AUTHOR>
 * @since 2024-10-25 09:43:48
 */
@Service
public class PetContentDetailServiceImpl extends ServiceImpl<BcyPetContentDetailMapper, BcyPetContentDetailEntity>
        implements IPetContentDetailService {

    @Override
    public BcyPetContentDetailEntity getByContentId(Long contentId) {
        return baseMapper.selectOne(new LambdaQueryWrapper<BcyPetContentDetailEntity>()
                .eq(BcyPetContentDetailEntity::getContentId, contentId)
                .last(MybatisConstants.QUERY_FIRST)
        );
    }

    @Override
    public void removeByContentIds(List<Long> ids) {
        baseMapper.delete(new LambdaQueryWrapper<BcyPetContentDetailEntity>()
                .in(BcyPetContentDetailEntity::getContentId, ids)
        );
    }
}




