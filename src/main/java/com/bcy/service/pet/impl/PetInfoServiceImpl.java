package com.bcy.service.pet.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcy.domain.request.PageRequest;
import com.bcy.entity.pet.BcyPetInfoEntity;
import com.bcy.mapper.pet.BcyPetInfoMapper;
import com.bcy.service.pet.IPetInfoService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 针对表【bcy_pet_info(用户宠物信息表)】的数据库操作Service实现
 *
 * <AUTHOR>
 * @since 2024-10-29 16:12:08
 */
@Service
public class PetInfoServiceImpl extends ServiceImpl<BcyPetInfoMapper, BcyPetInfoEntity>
        implements IPetInfoService {

    @Override
    public IPage<BcyPetInfoEntity> pageList(PageRequest pageRequest) {
        IPage<BcyPetInfoEntity> pages = new Page<>(pageRequest.getPage(), pageRequest.getLimit());
        return baseMapper.selectPage(pages,
                new LambdaQueryWrapper<BcyPetInfoEntity>()
                        .eq(Objects.nonNull(pageRequest.getLinkId()), BcyPetInfoEntity::getCategoryId, pageRequest.getLinkId())
                        .eq(Objects.nonNull(pageRequest.getStatus()), BcyPetInfoEntity::getIsDefault, pageRequest.getStatus())
                        .like(StringUtils.isNotBlank(pageRequest.getName()), BcyPetInfoEntity::getName, pageRequest.getName())
                        .between(StringUtils.isNotBlank(pageRequest.getBeginTime()) && StringUtils.isNotBlank(pageRequest.getEndTime()),
                                BcyPetInfoEntity::getCreateTime, pageRequest.getBeginTime(), pageRequest.getEndTime())
                        .orderByDesc(BcyPetInfoEntity::getCreateTime));
    }

    @Override
    public List<BcyPetInfoEntity> getByUserId(long userId) {
        return baseMapper.selectList(new LambdaQueryWrapper<BcyPetInfoEntity>()
                .eq(BcyPetInfoEntity::getUserId, userId)
                .orderByDesc(BcyPetInfoEntity::getIsDefault));
    }
}




