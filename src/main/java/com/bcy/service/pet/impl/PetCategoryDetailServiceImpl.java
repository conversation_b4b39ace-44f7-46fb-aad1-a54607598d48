package com.bcy.service.pet.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcy.domain.request.PageRequest;
import com.bcy.entity.pet.BcyPetCategoryDetailEntity;
import com.bcy.mapper.pet.BcyPetCategoryDetailMapper;
import com.bcy.service.pet.IPetCategoryDetailService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 针对表【bcy_pet_category_detail(类型介绍表)】的数据库操作Service实现
 *
 * <AUTHOR>
 * @since 2024-10-28 14:54:59
 */
@Service
public class PetCategoryDetailServiceImpl extends ServiceImpl<BcyPetCategoryDetailMapper, BcyPetCategoryDetailEntity>
        implements IPetCategoryDetailService {

    @Override
    public IPage<BcyPetCategoryDetailEntity> pageList(PageRequest pageRequest) {
        IPage<BcyPetCategoryDetailEntity> pages = new Page<>(pageRequest.getPage(), pageRequest.getLimit());
        return baseMapper.selectPage(pages,
                new LambdaQueryWrapper<BcyPetCategoryDetailEntity>()
                        .eq(BcyPetCategoryDetailEntity::getCategoryId, pageRequest.getLinkId())
                        .eq(StringUtils.isNotBlank(pageRequest.getType()), BcyPetCategoryDetailEntity::getTags, pageRequest.getType())
                        .between(StringUtils.isNotBlank(pageRequest.getBeginTime()) && StringUtils.isNotBlank(pageRequest.getEndTime()),
                                BcyPetCategoryDetailEntity::getCreateTime, pageRequest.getBeginTime(), pageRequest.getEndTime())
                        .orderByDesc(BcyPetCategoryDetailEntity::getCreateTime));
    }

    @Override
    public long countByCategoryIdAndTags(Long categoryId, String tags) {
        return baseMapper.selectCount(new LambdaQueryWrapper<BcyPetCategoryDetailEntity>()
                .eq(BcyPetCategoryDetailEntity::getCategoryId, categoryId)
                .eq(BcyPetCategoryDetailEntity::getTags, tags));
    }

    @Override
    public List<BcyPetCategoryDetailEntity> getByCategoryId(Long categoryId) {
        return baseMapper.selectList(new LambdaQueryWrapper<BcyPetCategoryDetailEntity>()
                .eq(BcyPetCategoryDetailEntity::getCategoryId, categoryId));
    }

    @Override
    public void deleteByCategoryId(Long id) {
        baseMapper.delete(new LambdaQueryWrapper<BcyPetCategoryDetailEntity>()
                .eq(BcyPetCategoryDetailEntity::getCategoryId, id));
    }
}




