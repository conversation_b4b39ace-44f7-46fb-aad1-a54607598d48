package com.bcy.service.pet.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcy.entity.pet.BcyPetContentUserEntity;
import com.bcy.mapper.pet.BcyPetContentUserMapper;
import com.bcy.service.pet.IPetContentUserService;
import org.springframework.stereotype.Service;

/**
 * 针对表【bcy_pet_content_user(文章用户关联表)】的数据库操作Service实现
 *
 * <AUTHOR>
 * @since 2024-10-25 09:43:48
 */
@Service
public class PetContentUserServiceImpl extends ServiceImpl<BcyPetContentUserMapper, BcyPetContentUserEntity>
        implements IPetContentUserService {

}




