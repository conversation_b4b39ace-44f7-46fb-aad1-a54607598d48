package com.bcy.service.pet.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcy.domain.request.PageRequest;
import com.bcy.entity.pet.BcyPetContentEntity;
import com.bcy.mapper.pet.BcyPetContentMapper;
import com.bcy.service.pet.IPetContentService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 针对表【bcy_pet_content(文章)】的数据库操作Service实现
 *
 * <AUTHOR>
 * @since 2024-10-25 09:43:48
 */
@Service
public class PetContentServiceImpl extends ServiceImpl<BcyPetContentMapper, BcyPetContentEntity>
        implements IPetContentService {

    @Override
    public IPage<BcyPetContentEntity> pageList(PageRequest pageRequest) {
        IPage<BcyPetContentEntity> pages = new Page<>(pageRequest.getPage(), pageRequest.getLimit());
        return baseMapper.selectPage(pages,
                new LambdaQueryWrapper<BcyPetContentEntity>()
                        .eq(Objects.nonNull(pageRequest.getLinkId()), BcyPetContentEntity::getCategoryId, pageRequest.getLinkId())
                        .eq(Objects.nonNull(pageRequest.getStatus()), BcyPetContentEntity::getStatus, pageRequest.getStatus())
                        .like(StringUtils.isNotBlank(pageRequest.getName()), BcyPetContentEntity::getTitle, pageRequest.getName())
                        .eq(StringUtils.isNotBlank(pageRequest.getType()), BcyPetContentEntity::getType, pageRequest.getType())
                        .between(StringUtils.isNotBlank(pageRequest.getBeginTime()) && StringUtils.isNotBlank(pageRequest.getEndTime()),
                                BcyPetContentEntity::getCreateTime, pageRequest.getBeginTime(), pageRequest.getEndTime())
                        .orderByAsc(BcyPetContentEntity::getSort)
                        .orderByDesc(BcyPetContentEntity::getCreateTime));
    }

    @Override
    public long countByTitle(String title) {
        return baseMapper.selectCount(new LambdaQueryWrapper<BcyPetContentEntity>()
                .eq(BcyPetContentEntity::getTitle, title));
    }

}




