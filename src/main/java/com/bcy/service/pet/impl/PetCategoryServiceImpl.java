package com.bcy.service.pet.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcy.domain.request.PageRequest;
import com.bcy.entity.pet.BcyPetCategoryEntity;
import com.bcy.mapper.pet.BcyPetCategoryMapper;
import com.bcy.service.pet.IPetCategoryService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * 针对表【bcy_pet_category(宠物分类)】的数据库操作Service实现
 *
 * <AUTHOR>
 * @since 2024-10-25 09:43:48
 */
@Service
public class PetCategoryServiceImpl extends ServiceImpl<BcyPetCategoryMapper, BcyPetCategoryEntity>
        implements IPetCategoryService {

    @Override
    public IPage<BcyPetCategoryEntity> pageList(PageRequest pageRequest) {
        IPage<BcyPetCategoryEntity> pages = new Page<>(pageRequest.getPage(), pageRequest.getLimit());
        return baseMapper.selectPage(pages,
                new LambdaQueryWrapper<BcyPetCategoryEntity>()
                        .eq(Objects.nonNull(pageRequest.getStatus()), BcyPetCategoryEntity::getStatus, pageRequest.getStatus())
                        .eq(Objects.nonNull(pageRequest.getLinkId()), BcyPetCategoryEntity::getParentId, pageRequest.getLinkId())
                        .eq(Objects.isNull(pageRequest.getLinkId()), BcyPetCategoryEntity::getParentId, BigDecimal.ZERO.longValue())
                        .like(StringUtils.isNotBlank(pageRequest.getName()), BcyPetCategoryEntity::getName, pageRequest.getName())
                        .orderByAsc(BcyPetCategoryEntity::getSort));
    }

    @Override
    public List<BcyPetCategoryEntity> getListByParentId(Long parentId) {
        return baseMapper.selectList(new LambdaQueryWrapper<BcyPetCategoryEntity>()
                .eq(BcyPetCategoryEntity::getParentId, parentId)
                .orderByAsc(BcyPetCategoryEntity::getSort));
    }

    @Override
    public long countByName(String name) {
        return baseMapper.selectCount((new LambdaQueryWrapper<BcyPetCategoryEntity>()
                .eq(BcyPetCategoryEntity::getName, name)));
    }
}




