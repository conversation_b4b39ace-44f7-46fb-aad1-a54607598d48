package com.bcy.service.points;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bcy.domain.request.PageRequest;
import com.bcy.entity.points.BcyUserAddressEntity;

import java.util.List;

/**
 * 用户收货地址表 Service
 */
public interface UserAddressService extends IService<BcyUserAddressEntity> {
    /**
     * 分页获取用户收货地址列表
     *
     * @param pageRequest 分页表单
     * @return 分页列表
     */
    IPage<BcyUserAddressEntity> queryPage(PageRequest pageRequest);

    /**
     * 设置默认用户地址
     *
     * @param userId 用户ID
     * @return true/false
     */
    List<BcyUserAddressEntity> listByUserId(Long userId);
}