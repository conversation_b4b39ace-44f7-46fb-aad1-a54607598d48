package com.bcy.service.points;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bcy.domain.request.PageRequest;
import com.bcy.entity.points.BcyPointsTaskEntity;

/**
 * 任务表 Service
 */
public interface PointsTaskService extends IService<BcyPointsTaskEntity> {
    /**
     * 分页查询
     *
     * @param pageRequest 分页表单
     * @return 分页列表
     */
    IPage<BcyPointsTaskEntity> queryPage(PageRequest pageRequest);

    /**
     * 根据任务名称获取任务数量
     *
     * @param name 任务名称
     * @return 任务数量
     */
    long countByName(String name);
}