package com.bcy.service.points;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bcy.domain.request.PageRequest;
import com.bcy.entity.points.BcyPointsGoodsEntity;

/**
 * 积分商品表 Service
 */
public interface PointsGoodsService extends IService<BcyPointsGoodsEntity> {
    /**
     * 积分商品分页列表
     *
     * @param pageRequest 分页表单
     * @return 分页列表
     */
    IPage<BcyPointsGoodsEntity> queryPage(PageRequest pageRequest);

    /**
     * 根据商品名称获取商品
     * @param name 商品名称
     * @return 商品实体
     */
    BcyPointsGoodsEntity getByName(String name);
} 