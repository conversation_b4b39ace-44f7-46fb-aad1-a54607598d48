package com.bcy.service.points.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcy.domain.request.PageRequest;
import com.bcy.entity.points.BcyUserAddressEntity;
import com.bcy.mapper.points.BcyUserAddressMapper;
import com.bcy.service.points.UserAddressService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 用户收货地址表 ServiceImpl
 */
@Service
public class UserAddressServiceImpl extends ServiceImpl<BcyUserAddressMapper, BcyUserAddressEntity>
        implements UserAddressService {
    @Override
    public IPage<BcyUserAddressEntity> queryPage(PageRequest pageRequest) {
        IPage<BcyUserAddressEntity> pages = new Page<>(pageRequest.getPage(), pageRequest.getLimit());
        return baseMapper.selectPage(pages,
                new LambdaQueryWrapper<BcyUserAddressEntity>()
                        .eq(Objects.nonNull(pageRequest.getStatus()), BcyUserAddressEntity::getIsDefault, pageRequest.getStatus())
                        .eq(Objects.nonNull(pageRequest.getLinkId()), BcyUserAddressEntity::getUserId, pageRequest.getLinkId())
                        .like(StringUtils.isNotBlank(pageRequest.getName()), BcyUserAddressEntity::getReceiverName, pageRequest.getName())
                        .orderByAsc(BcyUserAddressEntity::getCreateTime));
    }

    @Override
    public List<BcyUserAddressEntity> listByUserId(Long userId) {
        return baseMapper.selectList(new LambdaQueryWrapper<BcyUserAddressEntity>()
                .eq(BcyUserAddressEntity::getUserId, userId)
        );
    }
}