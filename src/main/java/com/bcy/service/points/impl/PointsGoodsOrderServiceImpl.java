package com.bcy.service.points.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcy.entity.points.BcyPointsGoodsOrderEntity;
import com.bcy.mapper.points.BcyPointsGoodsOrderMapper;
import org.springframework.stereotype.Service;
import com.bcy.service.points.PointsGoodsOrderService;

import java.util.List;

/**
 * 积分商品兑换记录表 ServiceImpl
 */
@Service
public class PointsGoodsOrderServiceImpl extends ServiceImpl<BcyPointsGoodsOrderMapper, BcyPointsGoodsOrderEntity>
        implements PointsGoodsOrderService {
    @Override
    public List<BcyPointsGoodsOrderEntity> listByGoodsId(Long id) {
        return baseMapper.selectList(new LambdaQueryWrapper<BcyPointsGoodsOrderEntity>()
                .eq(BcyPointsGoodsOrderEntity::getGoodsId, id));
    }
}