package com.bcy.service.points.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcy.domain.request.PageRequest;
import com.bcy.entity.points.BcyUserPointsLogEntity;
import com.bcy.mapper.points.BcyUserPointsLogMapper;
import com.bcy.service.points.UserPointsLogService;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 积分明细表 ServiceImpl
 */
@Service
public class UserPointsLogServiceImpl extends ServiceImpl<BcyUserPointsLogMapper, BcyUserPointsLogEntity>
        implements UserPointsLogService {

    @Override
    public IPage<BcyUserPointsLogEntity> queryPage(PageRequest pageRequest) {
        Page<BcyUserPointsLogEntity> pages = new Page<>(pageRequest.getPage(), pageRequest.getLimit());
        return baseMapper.selectPage(pages,
                new LambdaQueryWrapper<BcyUserPointsLogEntity>()
                        .eq(Objects.nonNull(pageRequest.getLinkId()), BcyUserPointsLogEntity::getUserId, pageRequest.getLinkId())
                        .orderByAsc(BcyUserPointsLogEntity::getCreateTime));
    }
}
 