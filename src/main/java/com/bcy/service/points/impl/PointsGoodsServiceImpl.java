package com.bcy.service.points.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcy.domain.request.PageRequest;
import com.bcy.entity.points.BcyPointsGoodsEntity;
import com.bcy.mapper.points.BcyPointsGoodsMapper;
import com.bcy.service.points.PointsGoodsService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

import static com.bcy.constants.MybatisConstants.QUERY_FIRST;

/**
 * 积分商品表 ServiceImpl
 */
@Service
public class PointsGoodsServiceImpl extends ServiceImpl<BcyPointsGoodsMapper, BcyPointsGoodsEntity>
        implements PointsGoodsService {

    @Override
    public IPage<BcyPointsGoodsEntity> queryPage(PageRequest pageRequest) {
        Page<BcyPointsGoodsEntity> page = new Page<>(pageRequest.getPage(), pageRequest.getLimit());
        return baseMapper.selectPage(page,
                new LambdaQueryWrapper<BcyPointsGoodsEntity>()
                        .eq(Objects.nonNull(pageRequest.getStatus()), BcyPointsGoodsEntity::getIsActive, pageRequest.getStatus())
                        .like(StringUtils.isNotBlank(pageRequest.getName()), BcyPointsGoodsEntity::getName, pageRequest.getName())
                        .orderByAsc(BcyPointsGoodsEntity::getCreateTime));
    }

    @Override
    public BcyPointsGoodsEntity getByName(String name) {
        return baseMapper.selectOne(new LambdaQueryWrapper<BcyPointsGoodsEntity>()
                .eq(BcyPointsGoodsEntity::getName, name)
                .last(QUERY_FIRST));
    }
} 