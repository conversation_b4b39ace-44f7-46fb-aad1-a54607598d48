package com.bcy.service.points.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcy.domain.request.PageRequest;
import com.bcy.entity.points.BcyPointsTaskEntity;
import com.bcy.mapper.points.BcyPointsTaskMapper;
import com.bcy.service.points.PointsTaskService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 任务表 ServiceImpl
 */
@Service
public class PointsTaskServiceImpl extends ServiceImpl<BcyPointsTaskMapper, BcyPointsTaskEntity>
        implements PointsTaskService {

    @Override
    public IPage<BcyPointsTaskEntity> queryPage(PageRequest pageRequest) {
        Page<BcyPointsTaskEntity> pages = new Page<>(pageRequest.getPage(), pageRequest.getLimit());
        return baseMapper.selectPage(pages,
                new LambdaQueryWrapper<BcyPointsTaskEntity>()
                        .eq(Objects.nonNull(pageRequest.getStatus()), BcyPointsTaskEntity::getIsActive, pageRequest.getStatus())
                        .eq(Objects.nonNull(pageRequest.getType()), BcyPointsTaskEntity::getType, pageRequest.getType())
                        .like(StringUtils.isNotBlank(pageRequest.getName()), BcyPointsTaskEntity::getName, pageRequest.getName())
                        .orderByAsc(BcyPointsTaskEntity::getCreateTime));
    }

    @Override
    public long countByName(String name) {
        return baseMapper.selectCount(new LambdaQueryWrapper<BcyPointsTaskEntity>().eq(BcyPointsTaskEntity::getName, name));
    }
} 