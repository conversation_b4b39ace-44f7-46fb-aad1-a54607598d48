package com.bcy.service.points.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcy.entity.points.BcyUserInviteEntity;
import com.bcy.mapper.points.BcyUserInviteMapper;
import org.springframework.stereotype.Service;
import com.bcy.service.points.UserInviteService;

/**
 * 用户邀请表 ServiceImpl
 */
@Service
public class UserInviteServiceImpl extends ServiceImpl<BcyUserInviteMapper, BcyUserInviteEntity>
        implements UserInviteService {
    // 可扩展自定义方法
} 