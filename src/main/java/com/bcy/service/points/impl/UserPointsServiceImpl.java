package com.bcy.service.points.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcy.domain.request.PageRequest;
import com.bcy.entity.points.BcyUserPointsEntity;
import com.bcy.mapper.points.BcyUserPointsMapper;
import com.bcy.service.points.UserPointsService;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 用户积分表 ServiceImpl
 */
@Service
public class UserPointsServiceImpl extends ServiceImpl<BcyUserPointsMapper, BcyUserPointsEntity>
        implements UserPointsService {

    @Override
    public IPage<BcyUserPointsEntity> queryPage(PageRequest pageRequest) {
        Page<BcyUserPointsEntity> pages = new Page<>(pageRequest.getPage(), pageRequest.getLimit());
        return baseMapper.selectPage(pages,
                new LambdaQueryWrapper<BcyUserPointsEntity>()
                        .eq(Objects.nonNull(pageRequest.getLinkId()), BcyUserPointsEntity::getUserId, pageRequest.getLinkId())
                        .orderByAsc(BcyUserPointsEntity::getCreateTime));
    }

    @Override
    public BcyUserPointsEntity searchUserPoints(long userId) {
        return this.lambdaQuery().eq(BcyUserPointsEntity::getUserId, userId).one();
    }
} 