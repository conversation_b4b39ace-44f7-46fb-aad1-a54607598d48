package com.bcy.service.points;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bcy.domain.request.PageRequest;
import com.bcy.entity.points.BcyUserPointsLogEntity;

/**
 * 积分明细表 Service
 */
public interface UserPointsLogService extends IService<BcyUserPointsLogEntity> {
    /**
     * 分页获取用户积分明细表列表
     *
     * @param pageRequest 分页表单
     * @return 分页列表
     */
    IPage<BcyUserPointsLogEntity> queryPage(PageRequest pageRequest);
} 