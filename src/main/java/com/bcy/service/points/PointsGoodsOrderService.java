package com.bcy.service.points;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bcy.entity.points.BcyPointsGoodsOrderEntity;

import java.util.List;

/**
 * 积分商品兑换记录表 Service
 */
public interface PointsGoodsOrderService extends IService<BcyPointsGoodsOrderEntity> {
    /**
     * 根据兑换商品ID获取兑换列表
     *
     * @param id 商品ID
     * @return 兑换列表
     */
    List<BcyPointsGoodsOrderEntity> listByGoodsId(Long id);
}