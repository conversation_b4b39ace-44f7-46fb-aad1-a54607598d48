package com.bcy.service.points;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bcy.domain.request.PageRequest;
import com.bcy.entity.points.BcyUserPointsEntity;

/**
 * 用户积分表 Service
 */
public interface UserPointsService extends IService<BcyUserPointsEntity> {
    /**
     * 用户积分分页列表
     *
     * @param pageRequest 分页表单
     * @return 分页列表
     */
    IPage<BcyUserPointsEntity> queryPage(PageRequest pageRequest);

    /**
     * 根据用户ID获取用户积分
     *
     * @param userId 用户ID
     * @return 用户积分
     */
    BcyUserPointsEntity searchUserPoints(long userId);
}