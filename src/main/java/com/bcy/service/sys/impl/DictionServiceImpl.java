package com.bcy.service.sys.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcy.domain.request.PageRequest;
import com.bcy.entity.sys.SysDictionEntity;
import com.bcy.mapper.sys.SysDictionMapper;
import com.bcy.service.sys.IDictionService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 针对表【sys_diction(字典表)】的数据库操作Service实现
 *
 * <AUTHOR>
 * @since 2024-09-28 23:10:49
 */
@Service
public class DictionServiceImpl extends ServiceImpl<SysDictionMapper, SysDictionEntity> implements IDictionService {

    @Override
    public IPage<SysDictionEntity> dictionPage(PageRequest pageRequest) {
        IPage<SysDictionEntity> pages = new Page<>(pageRequest.getPage(), pageRequest.getLimit());
        return baseMapper.selectPage(pages, new LambdaQueryWrapper<SysDictionEntity>()
                .like(StringUtils.isNotBlank(pageRequest.getName()), SysDictionEntity::getName, pageRequest.getName())
                .eq(StringUtils.isNotBlank(pageRequest.getType()), SysDictionEntity::getDataCode, pageRequest.getType())
                .orderByDesc(SysDictionEntity::getCreateTime));
    }

    @Override
    public long listByKey(String dataCode) {
        return baseMapper.selectCount(new LambdaQueryWrapper<SysDictionEntity>()
                .eq(SysDictionEntity::getDataCode, dataCode));
    }
}




