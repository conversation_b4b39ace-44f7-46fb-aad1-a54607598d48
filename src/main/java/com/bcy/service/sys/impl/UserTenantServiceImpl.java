package com.bcy.service.sys.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcy.entity.sys.SysUserTenantEntity;
import com.bcy.mapper.sys.SysUserTenantMapper;
import com.bcy.service.sys.IUserTenantService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 针对表【sys_user_tenant(用户角色关联表)】的数据库操作Service实现
 *
 * <AUTHOR>
 * @since 2023-10-17 09:37:26
 */
@Service
public class UserTenantServiceImpl extends ServiceImpl<SysUserTenantMapper, SysUserTenantEntity> implements IUserTenantService {

    @Override
    public List<SysUserTenantEntity> tenantListByUserId(Long userId) {
        return baseMapper.selectList(new LambdaQueryWrapper<SysUserTenantEntity>()
                .eq(SysUserTenantEntity::getUserId, userId));
    }

    @Override
    public List<SysUserTenantEntity> userListByTenantId(Long tenantId) {
        return baseMapper.selectList(new LambdaQueryWrapper<SysUserTenantEntity>()
                .eq(SysUserTenantEntity::getLinkTenantId, tenantId));
    }

    @Override
    public long userListByTenantIdList(List<Long> tenantIdList) {
        return baseMapper.selectCount(new LambdaQueryWrapper<SysUserTenantEntity>()
                .in(SysUserTenantEntity::getLinkTenantId, tenantIdList));
    }

    @Override
    public long removeByUserIds(List<Long> userIds) {
        return baseMapper.delete(new LambdaQueryWrapper<SysUserTenantEntity>()
                .in(SysUserTenantEntity::getUserId, userIds));
    }

    @Override
    public void removeByUserId(Long userId) {
        baseMapper.delete(new LambdaQueryWrapper<SysUserTenantEntity>().eq(SysUserTenantEntity::getUserId, userId));
    }
}




