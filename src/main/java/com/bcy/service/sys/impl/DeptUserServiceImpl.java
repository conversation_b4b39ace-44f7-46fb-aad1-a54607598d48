package com.bcy.service.sys.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcy.entity.sys.SysDeptUserEntity;
import com.bcy.mapper.sys.SysDeptUserMapper;
import com.bcy.service.sys.IDeptUserService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 针对表【sys_dept_user(部门用户关联表)】的数据库操作Service实现
 *
 * <AUTHOR>
 * @since 2024-09-28 23:10:49
 */
@Service
public class DeptUserServiceImpl extends ServiceImpl<SysDeptUserMapper, SysDeptUserEntity> implements IDeptUserService {

    @Override
    public List<SysDeptUserEntity> listByDeptId(Long deptId) {
        return baseMapper.selectList(new LambdaQueryWrapper<SysDeptUserEntity>().eq(SysDeptUserEntity::getDeptId, deptId));
    }

    @Override
    public List<SysDeptUserEntity> listByUserId(Long userId) {
        return baseMapper.selectList(new LambdaQueryWrapper<SysDeptUserEntity>().eq(SysDeptUserEntity::getUserId, userId));
    }

    @Override
    public long removeByUserIds(List<Long> userIds) {
        return baseMapper.delete(new LambdaQueryWrapper<SysDeptUserEntity>().in(SysDeptUserEntity::getUserId, userIds));
    }

    @Override
    public List<SysDeptUserEntity> listByUserIdList(List<Long> userIdList) {
        return baseMapper.selectList(new LambdaQueryWrapper<SysDeptUserEntity>().in(SysDeptUserEntity::getUserId, userIdList));
    }

    @Override
    public List<SysDeptUserEntity> listByDeptIdList(List<Long> deptIdList) {
        return baseMapper.selectList(new LambdaQueryWrapper<SysDeptUserEntity>().in(SysDeptUserEntity::getDeptId, deptIdList));
    }

    @Override
    public void removeByUserId(Long userId) {
        baseMapper.delete(new LambdaQueryWrapper<SysDeptUserEntity>().eq(SysDeptUserEntity::getUserId, userId));
    }
}




