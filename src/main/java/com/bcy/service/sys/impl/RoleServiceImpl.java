package com.bcy.service.sys.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcy.annotation.DataPerm;
import com.bcy.domain.request.PageRequest;
import com.bcy.entity.sys.SysRoleEntity;
import com.bcy.mapper.sys.SysRoleMapper;
import com.bcy.service.sys.IRoleService;
import com.bcy.utils.BcyInfoUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 角色表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-04
 */
@Service
public class RoleServiceImpl extends ServiceImpl<SysRoleMapper, SysRoleEntity> implements IRoleService {


    @Override
    public IPage<SysRoleEntity> rolePage(PageRequest pageRequest, List<Long> roleIdList) {
        IPage<SysRoleEntity> pages = new Page<>(pageRequest.getPage(), pageRequest.getLimit());
        return baseMapper.selectPage(pages, new LambdaQueryWrapper<SysRoleEntity>()
                .like(StringUtils.isNotBlank(pageRequest.getName()), SysRoleEntity::getName, pageRequest.getName())
                .and(sysRoleEntityLambdaQueryWrapper -> sysRoleEntityLambdaQueryWrapper
                        .in(SysRoleEntity::getId, roleIdList)
                        .or()
                        .eq(SysRoleEntity::getCreateUser, BcyInfoUtils.getUserName()))
                .between(StringUtils.isNotBlank(pageRequest.getBeginTime()) && StringUtils.isNotBlank(pageRequest.getEndTime()),
                        SysRoleEntity::getCreateTime, pageRequest.getBeginTime(), pageRequest.getEndTime())
                .orderByDesc(SysRoleEntity::getCreateTime));
    }

    @Override
    public long roleListByName(String roleName) {
        return baseMapper.selectCount(new LambdaQueryWrapper<SysRoleEntity>()
                .eq(SysRoleEntity::getName, roleName));
    }

    @Override
    @DataPerm
    public List<SysRoleEntity> roleList() {
        return this.list();
    }
}
