package com.bcy.service.sys.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcy.annotation.DataPerm;
import com.bcy.domain.request.PageRequest;
import com.bcy.entity.sys.SysConfigEntity;
import com.bcy.mapper.sys.SysConfigMapper;
import com.bcy.service.sys.IConfigService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;


/**
 * 针对表【sys_config(系统配置管理表)】的数据库操作Service实现
 *
 * <AUTHOR>
 * @since 2024-09-28 23:10:49
 */
@Service
public class ConfigServiceImpl extends ServiceImpl<SysConfigMapper, SysConfigEntity> implements IConfigService {

    @Override
    @DataPerm
    public IPage<SysConfigEntity> configPage(PageRequest pageRequest) {
        IPage<SysConfigEntity> pages = new Page<>(pageRequest.getPage(), pageRequest.getLimit());
        return baseMapper.selectPage(pages,
                new LambdaQueryWrapper<SysConfigEntity>()
                        .eq(Objects.nonNull(pageRequest.getStatus()), SysConfigEntity::getStatus, pageRequest.getStatus())
                        .like(StringUtils.isNotBlank(pageRequest.getName()), SysConfigEntity::getParamsKey, pageRequest.getName())
                        .between(StringUtils.isNotBlank(pageRequest.getBeginTime()) && StringUtils.isNotBlank(pageRequest.getEndTime()),
                                SysConfigEntity::getCreateTime, pageRequest.getBeginTime(), pageRequest.getEndTime())
                        .orderByDesc(SysConfigEntity::getCreateTime));
    }

    @Override
    public List<SysConfigEntity> listByKey(String key, Integer status) {
        return baseMapper.selectList(new LambdaQueryWrapper<SysConfigEntity>()
                .eq(StringUtils.isNotBlank(key), SysConfigEntity::getParamsKey, key)
                .eq(Objects.nonNull(status), SysConfigEntity::getStatus, status)
                .orderByDesc(SysConfigEntity::getCreateTime)
        );
    }

}




