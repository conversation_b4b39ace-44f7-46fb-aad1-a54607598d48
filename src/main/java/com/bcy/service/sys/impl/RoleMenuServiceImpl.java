package com.bcy.service.sys.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcy.entity.sys.SysRoleMenuEntity;
import com.bcy.mapper.sys.SysRoleMenuMapper;
import com.bcy.service.sys.IRoleMenuService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 菜单角色关联表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-04
 */
@Service
public class RoleMenuServiceImpl extends ServiceImpl<SysRoleMenuMapper, SysRoleMenuEntity> implements IRoleMenuService {

    @Override
    public List<SysRoleMenuEntity> roleMenuListByRoleId(Long roleId) {
        return baseMapper.selectList(new LambdaQueryWrapper<SysRoleMenuEntity>().eq(SysRoleMenuEntity::getRoleId, roleId));
    }

    @Override
    public List<SysRoleMenuEntity> roleMenuListByRoleIds(List<Long> roleIds) {
        return baseMapper.selectList(new LambdaQueryWrapper<SysRoleMenuEntity>().in(SysRoleMenuEntity::getRoleId, roleIds));
    }

    @Override
    public long removeByRoleIds(List<Long> roleIdList) {
        return baseMapper.delete(new LambdaQueryWrapper<SysRoleMenuEntity>().in(SysRoleMenuEntity::getRoleId, roleIdList));
    }

    @Override
    public long removeByMenuIdList(List<Long> menuIdList) {
        return baseMapper.delete(new LambdaQueryWrapper<SysRoleMenuEntity>().in(SysRoleMenuEntity::getMenuId, menuIdList));
    }
}
