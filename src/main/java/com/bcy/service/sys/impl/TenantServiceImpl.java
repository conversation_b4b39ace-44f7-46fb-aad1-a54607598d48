package com.bcy.service.sys.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcy.domain.request.PageRequest;
import com.bcy.entity.sys.SysTenantEntity;
import com.bcy.mapper.sys.SysTenantMapper;
import com.bcy.service.sys.ITenantService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 租户表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-04
 */
@Service
public class TenantServiceImpl extends ServiceImpl<SysTenantMapper, SysTenantEntity> implements ITenantService {

    @Override
    public IPage<SysTenantEntity> tenantPage(PageRequest pageRequest, List<Long> tenantIdList) {
        IPage<SysTenantEntity> pages = new Page<>(pageRequest.getPage(), pageRequest.getLimit());
        return baseMapper.selectPage(pages, new LambdaQueryWrapper<SysTenantEntity>()
                .in(SysTenantEntity::getId, tenantIdList)
                .like(StringUtils.isNotBlank(pageRequest.getName()), SysTenantEntity::getName, pageRequest.getName())
                .orderByDesc(SysTenantEntity::getCreateTime));
    }

    @Override
    public long countByKey(String appKey) {
        return baseMapper.selectCount(new LambdaQueryWrapper<SysTenantEntity>()
                .eq(SysTenantEntity::getAppKey, appKey)
        );
    }
}
