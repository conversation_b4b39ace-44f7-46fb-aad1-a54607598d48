package com.bcy.service.sys.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcy.entity.sys.SysUserRoleEntity;
import com.bcy.mapper.sys.SysUserRoleMapper;
import com.bcy.service.sys.IUserRoleService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户角色关联表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-04
 */
@Service
public class UserRoleServiceImpl extends ServiceImpl<SysUserRoleMapper, SysUserRoleEntity> implements IUserRoleService {

    @Override
    public List<SysUserRoleEntity> userRoleListByUserId(Long userId) {
        return baseMapper.selectList(new LambdaQueryWrapper<SysUserRoleEntity>().eq(SysUserRoleEntity::getUserId, userId));
    }

    @Override
    public long removeByUserIds(List<Long> userIds) {
        return baseMapper.delete(new LambdaQueryWrapper<SysUserRoleEntity>().in(SysUserRoleEntity::getUserId, userIds));
    }

    @Override
    public long userRoleListByRoleIdList(List<Long> roleIdList) {
        return baseMapper.selectCount(new LambdaQueryWrapper<SysUserRoleEntity>().in(SysUserRoleEntity::getRoleId, roleIdList));
    }

    @Override
    public void removeByUserId(Long userId) {
        baseMapper.delete(new LambdaQueryWrapper<SysUserRoleEntity>().eq(SysUserRoleEntity::getUserId, userId));
    }
}
