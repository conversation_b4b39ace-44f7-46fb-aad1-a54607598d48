package com.bcy.service.sys.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcy.entity.sys.SysDeptEntity;
import com.bcy.mapper.sys.SysDeptMapper;
import com.bcy.service.sys.IDeptService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

import static com.bcy.constants.MybatisConstants.QUERY_FIRST;

/**
 * 针对表【sys_dept(部门表)】的数据库操作Service实现
 *
 * <AUTHOR>
 * @since 2024-09-28 23:10:49
 */
@Service
public class DeptServiceImpl extends ServiceImpl<SysDeptMapper, SysDeptEntity> implements IDeptService {

    @Override
    public List<SysDeptEntity> childDeptListByDeptId(Long deptId) {
        return baseMapper.selectList(new LambdaQueryWrapper<SysDeptEntity>()
                .eq(SysDeptEntity::getParentId, deptId)
        );
    }

    @Override
    public List<SysDeptEntity> listByDeptName(String deptName) {
        return baseMapper.selectList(new LambdaQueryWrapper<SysDeptEntity>()
                .like(StringUtils.isNotBlank(deptName), SysDeptEntity::getName, deptName)
        );
    }

    @Override
    public SysDeptEntity getParentInfoByTenantId() {
        return baseMapper.selectOne(new LambdaQueryWrapper<SysDeptEntity>().eq(SysDeptEntity::getParentId, BigDecimal.ZERO).last(QUERY_FIRST));
    }
}




