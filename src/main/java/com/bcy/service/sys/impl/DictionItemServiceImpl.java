package com.bcy.service.sys.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcy.domain.request.PageRequest;
import com.bcy.entity.sys.SysDictionItemEntity;
import com.bcy.mapper.sys.SysDictionItemMapper;
import com.bcy.service.sys.IDictionItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.bcy.constants.MybatisConstants.QUERY_FIRST;

/**
 * 针对表【sys_diction_item(字典详情表)】的数据库操作Service实现
 *
 * <AUTHOR>
 * @since 2022-09-28 23:10:49
 */
@Slf4j
@Service
public class DictionItemServiceImpl extends ServiceImpl<SysDictionItemMapper, SysDictionItemEntity> implements IDictionItemService {

    @Override
    public IPage<SysDictionItemEntity> dictionItemPage(PageRequest pageRequest) {
        IPage<SysDictionItemEntity> pages = new Page<>(pageRequest.getPage(), pageRequest.getLimit());
        return baseMapper.selectPage(pages, new LambdaQueryWrapper<SysDictionItemEntity>()
                .eq(SysDictionItemEntity::getDictionId, pageRequest.getLinkId())
                .like(StringUtils.isNotBlank(pageRequest.getName()), SysDictionItemEntity::getName, pageRequest.getName())
                .orderByDesc(SysDictionItemEntity::getCreateTime));
    }

    @Override
    public List<SysDictionItemEntity> listByDictionId(Long dictionId) {
        return baseMapper.selectList(new LambdaQueryWrapper<SysDictionItemEntity>()
                .eq(SysDictionItemEntity::getDictionId, dictionId)
                .orderByAsc(SysDictionItemEntity::getSort));
    }

    @Override
    public long countByDictionIds(List<Long> dictionList) {
        return baseMapper.selectCount(new LambdaQueryWrapper<SysDictionItemEntity>()
                .in(SysDictionItemEntity::getDictionId, dictionList));
    }

    @Override
    public long countByValue(Long dictionId, String value) {
        return baseMapper.selectCount(new LambdaQueryWrapper<SysDictionItemEntity>()
                .eq(SysDictionItemEntity::getValue, value)
                .eq(SysDictionItemEntity::getDictionId, dictionId));
    }

    @Override
    public SysDictionItemEntity getInfByParam(String dataCode, String value) {
        return baseMapper.selectOne(new LambdaQueryWrapper<SysDictionItemEntity>()
                .eq(SysDictionItemEntity::getDataCode, dataCode)
                .eq(SysDictionItemEntity::getValue, value)
                .last(QUERY_FIRST)
        );
    }

}




