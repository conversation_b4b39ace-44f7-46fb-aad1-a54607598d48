package com.bcy.service.sys.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcy.entity.sys.SysMenuEntity;
import com.bcy.mapper.sys.SysMenuMapper;
import com.bcy.service.sys.IMenuService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 菜单表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-04
 */
@Service
public class MenuServiceImpl extends ServiceImpl<SysMenuMapper, SysMenuEntity> implements IMenuService {

    @Override
    public List<SysMenuEntity> listByIdList(List<Long> idList, boolean isAdmin, String name) {
        return baseMapper.selectList(new LambdaQueryWrapper<SysMenuEntity>()
                .in(!isAdmin, SysMenuEntity::getId, idList)
                .like(StringUtils.isNotBlank(name), SysMenuEntity::getName, name)
                .orderByAsc(SysMenuEntity::getSort));
    }

    @Override
    public List<SysMenuEntity> listByParentId(Long parentId) {
        return baseMapper.selectList(new LambdaQueryWrapper<SysMenuEntity>()
                .eq(SysMenuEntity::getParentId, parentId)
                .orderByAsc(SysMenuEntity::getSort));
    }

    @Override
    public long listByName(String name) {
        return baseMapper.selectCount(new LambdaQueryWrapper<SysMenuEntity>()
                .eq(SysMenuEntity::getName, name));
    }

}
