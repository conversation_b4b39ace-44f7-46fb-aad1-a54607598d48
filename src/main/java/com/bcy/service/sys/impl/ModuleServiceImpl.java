package com.bcy.service.sys.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcy.domain.request.PageRequest;
import com.bcy.entity.sys.SysModuleEntity;
import com.bcy.mapper.sys.SysModuleMapper;
import com.bcy.service.sys.IModuleService;
import com.bcy.enums.StatusEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 针对表【sys_module(模块表)】的数据库操作Service实现
 *
 * <AUTHOR>
 * @since 2024-10-24 14:04:17
 */
@Service
public class ModuleServiceImpl extends ServiceImpl<SysModuleMapper, SysModuleEntity>
        implements IModuleService {

    @Override
    public IPage<SysModuleEntity> modulePage(PageRequest pageRequest) {
        IPage<SysModuleEntity> pages = new Page<>(pageRequest.getPage(), pageRequest.getLimit());
        return baseMapper.selectPage(pages,
                new LambdaQueryWrapper<SysModuleEntity>()
                        .eq(Objects.nonNull(pageRequest.getStatus()), SysModuleEntity::getStatus, pageRequest.getStatus())
                        .like(StringUtils.isNotBlank(pageRequest.getName()), SysModuleEntity::getName, pageRequest.getName())
                        .orderByDesc(SysModuleEntity::getCreateTime));
    }

    @Override
    public long countByCode(String code) {
        return baseMapper.selectCount(new LambdaQueryWrapper<SysModuleEntity>().eq(SysModuleEntity::getCode, code));
    }

    @Override
    public List<SysModuleEntity> showList() {
        return baseMapper.selectList(new LambdaQueryWrapper<SysModuleEntity>().eq(SysModuleEntity::getStatus, StatusEnum.ENABLE.getCode()));
    }
}




