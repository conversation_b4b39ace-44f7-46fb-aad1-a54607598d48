package com.bcy.service.sys.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bcy.annotation.DataPerm;
import com.bcy.domain.request.UserPageRequest;
import com.bcy.entity.sys.SysUserEntity;
import com.bcy.mapper.sys.SysUserMapper;
import com.bcy.service.sys.IUserService;
import com.bcy.enums.UserTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

import static com.bcy.constants.MybatisConstants.QUERY_FIRST;

/**
 * 系统用户表 服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-04
 */
@Service
public class UserServiceImpl extends ServiceImpl<SysUserMapper, SysUserEntity> implements IUserService {
    @Override
    @DataPerm
    public IPage<SysUserEntity> userPage(UserPageRequest userPageRequest, Long minId, List<Long> userIds) {
        IPage<SysUserEntity> pages = new Page<>(userPageRequest.getPage(), userPageRequest.getLimit(), Objects.isNull(minId));
        return baseMapper.selectPage(pages,
                new LambdaQueryWrapper<SysUserEntity>()
                        .in(SysUserEntity::getId, userIds)
                        .between(StringUtils.isNotBlank(userPageRequest.getBeginTime()) && StringUtils.isNotBlank(userPageRequest.getEndTime()),
                                SysUserEntity::getCreateTime, userPageRequest.getBeginTime(), userPageRequest.getEndTime())
                        .like(StringUtils.isNotBlank(userPageRequest.getName()), SysUserEntity::getUserName, userPageRequest.getName())
                        .eq(StringUtils.isNotBlank(userPageRequest.getType()), SysUserEntity::getType, userPageRequest.getType())
                        .lt(Objects.nonNull(minId), SysUserEntity::getId, minId)
                        .eq(Objects.nonNull(userPageRequest.getStatus()), SysUserEntity::getStatus, userPageRequest.getStatus())
                        .orderByDesc(SysUserEntity::getCreateTime));
    }

    @Override
    public SysUserEntity getUserInfoByPhoneOrUserName(String key) {
        return baseMapper.selectOne(new LambdaQueryWrapper<SysUserEntity>()
                .eq(SysUserEntity::getPhone, key)
                .or()
                .eq(SysUserEntity::getUserName, key)
                .orderByDesc(SysUserEntity::getCreateTime)
                .last(QUERY_FIRST)
        );
    }

    @Override
    public SysUserEntity listByPhone(String phone) {
        return baseMapper.selectOne(new LambdaQueryWrapper<SysUserEntity>()
                .eq(SysUserEntity::getPhone, phone));
    }

    @Override
    public SysUserEntity listByEmail(String email) {
        return baseMapper.selectOne(new LambdaQueryWrapper<SysUserEntity>()
                .eq(SysUserEntity::getEmail, email));
    }

    @Override
    public long listByUserName(String userName) {
        return baseMapper.selectCount(new LambdaQueryWrapper<SysUserEntity>()
                .eq(SysUserEntity::getUserName, userName));
    }

    @Override
    public List<SysUserEntity> listByUserIdList(List<Long> userIdList, UserTypeEnum userTypeEnum, boolean isAdmin) {
        return baseMapper.selectList(new LambdaQueryWrapper<SysUserEntity>()
                .in(!isAdmin, SysUserEntity::getId, userIdList)
                .eq(SysUserEntity::getType, userTypeEnum.getCode())
        );
    }

    @Override
    public SysUserEntity listByOpenId(String openId) {
        return baseMapper.selectOne(new LambdaQueryWrapper<SysUserEntity>()
                .eq(SysUserEntity::getOpenId, openId));
    }
}
