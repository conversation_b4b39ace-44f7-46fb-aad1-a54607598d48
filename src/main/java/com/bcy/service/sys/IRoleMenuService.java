package com.bcy.service.sys;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bcy.entity.sys.SysRoleMenuEntity;

import java.util.List;

/**
 * 菜单角色关联表 服务类
 *
 * <AUTHOR>
 * @since 2023-11-04
 */
public interface IRoleMenuService extends IService<SysRoleMenuEntity> {
    /**
     * 根据角色ID获取菜单角色关联列表
     *
     * @param roleId 角色Id
     * @return 关联列表
     */
    List<SysRoleMenuEntity> roleMenuListByRoleId(Long roleId);

    /**
     * 根据角色ID数组获取菜单角色关联列表
     *
     * @param roleIds 角色Id数组
     * @return 关联列表
     */
    List<SysRoleMenuEntity> roleMenuListByRoleIds(List<Long> roleIds);

    /**
     * 根据角色ID删除关联的菜单信息
     *
     * @param roleIdList 角色Id列表
     * @return 数量
     */
    long removeByRoleIds(List<Long> roleIdList);

    /**
     * 根据菜单ID列表输出菜单角色关联列表
     *
     * @param menuIdList 菜单Id列表
     * @return 数量
     */
    long removeByMenuIdList(List<Long> menuIdList);

}
