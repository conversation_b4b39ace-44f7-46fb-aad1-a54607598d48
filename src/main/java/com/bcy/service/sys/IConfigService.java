package com.bcy.service.sys;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bcy.domain.request.PageRequest;
import com.bcy.entity.sys.SysConfigEntity;

import java.util.List;

/**
 * 针对表【sys_config(系统配置管理表)】的数据库操作Service
 *
 * <AUTHOR>
 * @since 2024-09-28 23:10:49
 */
public interface IConfigService extends IService<SysConfigEntity> {
    /**
     * 分页获取配置列表
     *
     * @param pageRequest 分页表单
     * @return 分页列表
     */
    IPage<SysConfigEntity> configPage(PageRequest pageRequest);

    /**
     * 根据配置的key获取配置列表
     *
     * @param key    配置key
     * @param status 状态
     * @return 配置列表
     */
    List<SysConfigEntity> listByKey(String key, Integer status);

}
