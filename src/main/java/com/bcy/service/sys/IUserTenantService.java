package com.bcy.service.sys;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bcy.entity.sys.SysUserTenantEntity;

import java.util.List;

/**
 * 针对表【sys_user_tenant(用户角色关联表)】的数据库操作Service
 *
 * <AUTHOR>
 * @since 2023-10-17 09:37:26
 */
public interface IUserTenantService extends IService<SysUserTenantEntity> {
    /**
     * 根据用户ID获取租户用户关联列表
     *
     * @param userId 用户Id
     * @return 租户用户关联列表
     */
    List<SysUserTenantEntity> tenantListByUserId(Long userId);

    /**
     * 根据租户ID获取租户用户关联列表
     *
     * @param tenantId 租户Id
     * @return 租户用户关联列表
     */
    List<SysUserTenantEntity> userListByTenantId(Long tenantId);

    /**
     * 根据租户Id列表获取租户用户关联数量
     *
     * @param tenantIdList 租户Id列表
     * @return 数量
     */
    long userListByTenantIdList(List<Long> tenantIdList);

    /**
     * 根据用户ID数组删除获取租户用户关联列表
     *
     * @param userIds 用户Id列表
     * @return 数量
     */
    long removeByUserIds(List<Long> userIds);

    /**
     * 根据用户Id删除关联信息
     *
     * @param userId 用户ID
     */
    void removeByUserId(Long userId);
}
