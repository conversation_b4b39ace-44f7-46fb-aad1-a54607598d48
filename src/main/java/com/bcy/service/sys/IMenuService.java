package com.bcy.service.sys;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bcy.entity.sys.SysMenuEntity;

import java.util.List;

/**
 * 菜单表 服务类
 *
 * <AUTHOR>
 * @since 2024-10-04
 */
public interface IMenuService extends IService<SysMenuEntity> {
    /**
     * 根据菜单ID列表获取数据
     *
     * @param idList  菜单ID列表
     * @param isAdmin 是否超级管理员
     * @param name    菜单名称
     * @return 菜单列表
     */
    List<SysMenuEntity> listByIdList(List<Long> idList, boolean isAdmin, String name);

    /**
     * 根据父级ID获取子列表数据
     *
     * @param parentId 父ID
     * @return 子菜单列表
     */
    List<SysMenuEntity> listByParentId(Long parentId);

    /**
     * 根据菜单名称获取菜单列表
     *
     * @param name 菜单名称
     * @return 数量
     */
    long listByName(String name);
}
