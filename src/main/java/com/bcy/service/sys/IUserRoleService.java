package com.bcy.service.sys;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bcy.entity.sys.SysUserRoleEntity;

import java.util.List;

/**
 * 用户角色关联表 服务类
 *
 * <AUTHOR>
 * @since 2023-11-04
 */
public interface IUserRoleService extends IService<SysUserRoleEntity> {
    /**
     * 根据用户ID获取用户角色关联列表
     *
     * @param userId 用户Id
     * @return 关联列表
     */
    List<SysUserRoleEntity> userRoleListByUserId(Long userId);

    /**
     * 根据用户Id列表删除用户角色关联列表
     *
     * @param userIds 角色Id
     * @return 数量
     */
    long removeByUserIds(List<Long> userIds);

    /**
     * 根据角色ID列表获取用户角色关联列表
     *
     * @param roleIdList 角色Id列表
     * @return 数量
     */
    long userRoleListByRoleIdList(List<Long> roleIdList);

    /**
     * 根据用户ID删除关联表
     *
     * @param userId 用户Id
     */
    void removeByUserId(Long userId);
}
