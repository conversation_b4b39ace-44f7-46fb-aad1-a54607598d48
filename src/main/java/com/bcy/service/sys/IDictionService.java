package com.bcy.service.sys;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bcy.domain.request.PageRequest;
import com.bcy.entity.sys.SysDictionEntity;

/**
 * 针对表【sys_diction(字典表)】的数据库操作Service
 *
 * <AUTHOR>
 * @since 2024-09-28 23:10:49
 */
public interface IDictionService extends IService<SysDictionEntity> {
    /**
     * 分页获取字典列表
     *
     * @param pageRequest 分页表单
     * @return 分页列表
     */
    IPage<SysDictionEntity> dictionPage(PageRequest pageRequest);

    /**
     * 根据标识符获取字典列表
     *
     * @param dataCode 标识符
     * @return 字典列表
     */
    long listByKey(String dataCode);
}
