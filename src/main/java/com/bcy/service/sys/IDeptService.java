package com.bcy.service.sys;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bcy.entity.sys.SysDeptEntity;

import java.util.List;

/**
 * 针对表【sys_dept(部门表)】的数据库操作Service
 *
 * <AUTHOR>
 * @since 2024-09-28 23:10:49
 */
public interface IDeptService extends IService<SysDeptEntity> {
    /**
     * 根据部门ID获取子部门列表
     *
     * @param deptId 部门ID
     * @return 子部门列表
     */
    List<SysDeptEntity> childDeptListByDeptId(Long deptId);

    /**
     * 根据部门名称获取部门列表
     *
     * @param deptName 部门名称
     * @return 部门列表
     */
    List<SysDeptEntity> listByDeptName(String deptName);

    /**
     * 根据租户ID获取最大级别部门
     *
     * @return 部门
     */
    SysDeptEntity getParentInfoByTenantId();
}
