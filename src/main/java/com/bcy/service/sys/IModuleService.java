package com.bcy.service.sys;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bcy.domain.request.PageRequest;
import com.bcy.entity.sys.SysModuleEntity;

import java.util.List;

/**
 * 针对表【sys_module(模块表)】的数据库操作Service
 *
 * <AUTHOR>
 * @since 2024-10-24 14:04:17
 */
public interface IModuleService extends IService<SysModuleEntity> {

    /**
     * 获取分页模块服务列表
     *
     * @param pageRequest 分页表单
     * @return 分页列表
     */
    IPage<SysModuleEntity> modulePage(PageRequest pageRequest);

    /**
     * 根据服务标识获取模块个数
     *
     * @param code 标识
     * @return 数量
     */
    long countByCode(String code);


    /**
     * 模块服务列表
     *
     * @return 列表
     */
    List<SysModuleEntity> showList();
}
