package com.bcy.service.sys;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bcy.entity.sys.SysDeptUserEntity;

import java.util.List;

/**
 * 针对表【sys_dept_user(部门用户关联表)】的数据库操作Service
 *
 * <AUTHOR>
 * @since 2024-09-28 23:10:49
 */
public interface IDeptUserService extends IService<SysDeptUserEntity> {
    /**
     * 根据部门ID获取绑定的用户ID关联列表
     *
     * @param deptId 部门ID
     * @return 关联列表
     */
    List<SysDeptUserEntity> listByDeptId(Long deptId);

    /**
     * 根据用户ID获取绑定的用户ID关联列表
     *
     * @param userId 部门ID
     * @return 关联列表
     */
    List<SysDeptUserEntity> listByUserId(Long userId);

    /**
     * 根据用户ID数组删除绑定的用户ID关联列表
     *
     * @param userIds 用户Id数组
     * @return 删除数量
     */
    long removeByUserIds(List<Long> userIds);

    /**
     * 根据用户ID列表获取绑定的用户ID关联列表
     *
     * @param userIdList 用户Id列表
     * @return 关联列表
     */
    List<SysDeptUserEntity> listByUserIdList(List<Long> userIdList);

    /**
     * 根据部门ID列表获取绑定的用户ID关联列表
     *
     * @param deptIdList 部门Id列表
     * @return 关联列表
     */
    List<SysDeptUserEntity> listByDeptIdList(List<Long> deptIdList);

    /**
     * 根据用户ID删除关联信息
     *
     * @param userId 用户Id
     */
    void removeByUserId(Long userId);
}
