package com.bcy.service.sys;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bcy.domain.request.UserPageRequest;
import com.bcy.entity.sys.SysUserEntity;
import com.bcy.enums.UserTypeEnum;

import java.util.List;

/**
 * 系统用户表 服务类
 *
 * <AUTHOR>
 * @since 2023-11-04
 */
public interface IUserService extends IService<SysUserEntity> {

    /**
     * 用户分页列表
     *
     * @param userPageRequest 分页表单
     * @param minId           最小ID
     * @param userIds         用户ID列表
     * @return 分页列表
     */
    IPage<SysUserEntity> userPage(UserPageRequest userPageRequest, Long minId, List<Long> userIds);

    /**
     * 根据用户名或者电话获取用户信息
     *
     * @param key 用户名或者电话
     * @return 用户信息
     */
    SysUserEntity getUserInfoByPhoneOrUserName(String key);

    /**
     * 根据手机号获取用户数量
     *
     * @param phone 手机号
     * @return 用户数量
     */
    SysUserEntity listByPhone(String phone);

    /**
     * 根据邮箱获取用户数量
     *
     * @param email 邮箱
     * @return 用户信息
     */
    SysUserEntity listByEmail(String email);

    /**
     * 根据用户名获取用户数量
     *
     * @param userName 用户名
     * @return 用户信息
     */
    long listByUserName(String userName);

    /**
     * 根据用户列表获取用户信息
     *
     * @param userIdList   用户ID列表
     * @param userTypeEnum 用户类型
     * @param isAdmin      是否管理员
     * @return 用户列表
     */
    List<SysUserEntity> listByUserIdList(List<Long> userIdList, UserTypeEnum userTypeEnum, boolean isAdmin);

    /**
     * 根据openId获取用户数量
     *
     * @param openId 用户标识
     * @return 用户信息
     */
    SysUserEntity listByOpenId(String openId);
}
