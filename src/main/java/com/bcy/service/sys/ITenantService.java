package com.bcy.service.sys;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bcy.domain.request.PageRequest;
import com.bcy.entity.sys.SysTenantEntity;

import java.util.List;

/**
 * 租户表 服务类
 *
 * <AUTHOR>
 * @since 2023-11-04
 */
public interface ITenantService extends IService<SysTenantEntity> {
    /**
     * 租户分页列表
     *
     * @param pageRequest  分页表单
     * @param tenantIdList 租户ID列表
     * @return 分页列表
     */
    IPage<SysTenantEntity> tenantPage(PageRequest pageRequest, List<Long> tenantIdList);

    /**
     * 根据标识符获取租户数量
     *
     * @param appKey 标识符
     * @return 数量
     */
    long countByKey(String appKey);


}
