package com.bcy.service.sys;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bcy.domain.request.PageRequest;
import com.bcy.entity.sys.SysDictionItemEntity;

import java.util.List;

/**
 * 针对表【sys_diction_item(字典详情表)】的数据库操作Service
 *
 * <AUTHOR>
 * @since 2024-09-28 23:10:49
 */
public interface IDictionItemService extends IService<SysDictionItemEntity> {
    /**
     * 分页获取字典列表
     *
     * @param pageRequest 分页表单
     * @return 分页列表
     */
    IPage<SysDictionItemEntity> dictionItemPage(PageRequest pageRequest);

    /**
     * 根据字典ID获取字典明细列表
     *
     * @param dictionId 字典ID
     * @return 明细列表
     */
    List<SysDictionItemEntity> listByDictionId(Long dictionId);

    /**
     * 根据字典ID数组获取字典项数量
     *
     * @param dictionList 字典id数组
     */
    long countByDictionIds(List<Long> dictionList);

    /**
     * 根据字典项值获取数量
     *
     * @param dictionId 字典ID
     * @param value     字典项值
     * @return 数量
     */
    long countByValue(Long dictionId, String value);

    /**
     * 根据code和value获取详情
     *
     * @param dataCode 标识符
     * @param value    字典值
     * @return 字典项详情
     */
    SysDictionItemEntity getInfByParam(String dataCode, String value);
}
