package com.bcy.service.sys;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bcy.domain.request.PageRequest;
import com.bcy.entity.sys.SysRoleEntity;

import java.util.List;

/**
 * 角色表 服务类
 *
 * <AUTHOR>
 * @since 2023-11-04
 */
public interface IRoleService extends IService<SysRoleEntity> {
    /**
     * 获取角色分页列表
     *
     * @param pageRequest 分页列表
     * @param roleIdList  角色ID列表
     * @return 分页列表
     */
    IPage<SysRoleEntity> rolePage(PageRequest pageRequest, List<Long> roleIdList);

    /**
     * 根据角色名称获取角色列表
     *
     * @param roleName 角色名称
     * @return 数量
     */
    long roleListByName(String roleName);

    /**
     * 获取自己创建的角色列表
     *
     * @return 角色列表
     */
    List<SysRoleEntity> roleList();
}
