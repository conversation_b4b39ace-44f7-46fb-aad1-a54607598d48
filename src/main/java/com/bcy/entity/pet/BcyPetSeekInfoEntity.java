package com.bcy.entity.pet;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bcy.entity.CommonEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 寻宠信息表
 *
 * <AUTHOR>
 * @since 2024-08-21
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "bcy_pet_seek_info")
@Data
public class BcyPetSeekInfoEntity extends CommonEntity {
    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField(value = "user_id")
    private Long userId;

    /**
     * 宠物ID
     */
    @TableField(value = "pet_id")
    private Long petId;

    /**
     * 分类ID
     */
    @TableField(value = "category_id")
    private Long categoryId;

    /**
     * 宠物名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 寻宠介绍
     */
    @TableField(value = "description")
    private String description;

    /**
     * 图片
     */
    @TableField(value = "image")
    private String image;

    /**
     * 状态(0待审核1驳回2通过)
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 联系电话
     */
    @TableField(value = "phone")
    private String phone;

    /**
     * 联系地址
     */
    @TableField(value = "address")
    private String address;

    /**
     * 驳回原因
     */
    @TableField(value = "msg")
    private String msg;
}