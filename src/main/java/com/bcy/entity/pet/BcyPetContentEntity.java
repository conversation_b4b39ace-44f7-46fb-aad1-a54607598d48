package com.bcy.entity.pet;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bcy.entity.CommonEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 文章
 *
 * <AUTHOR>
 * @since 2024-08-21
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "bcy_pet_content")
@Data
public class BcyPetContentEntity extends CommonEntity {
    /**
     * 文章ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 分类ID
     */
    @TableField(value = "category_id")
    private Long categoryId;

    /**
     * 文章标题
     */
    @TableField(value = "title")
    private String title;

    /**
     * 文章副标题
     */
    @TableField(value = "sub_title")
    private String subTitle;

    /**
     * 简介
     */
    @TableField(value = "short_description")
    private String shortDescription;

    /**
     * 文章缩略图
     */
    @TableField(value = "image")
    private String image;

    /**
     * 文章来源
     */
    @TableField(value = "source")
    private String source;

    /**
     * 文章作者
     */
    @TableField(value = "author")
    private String author;

    /**
     * 是否显示0显示 1不显示
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 文章类型
     */
    @TableField(value = "type")
    private String type;

    /**
     * 发布时间
     */
    @TableField(value = "push_time")
    private Date pushTime;

    /**
     * 自定义顺序
     */
    @TableField(value = "sort")
    private Integer sort;

    /**
     * 文章标签
     */
    @TableField(value = "tags")
    private String tags;
}