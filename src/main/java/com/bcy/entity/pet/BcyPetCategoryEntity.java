package com.bcy.entity.pet;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bcy.entity.CommonEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 宠物分类
 *
 * <AUTHOR>
 * @since 2024-08-21
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "bcy_pet_category")
@Data
public class BcyPetCategoryEntity extends CommonEntity {
    /**
     * 分类ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 父级ID
     */
    @TableField(value = "parent_id")
    private Long parentId;

    /**
     * 分类名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * banner图
     */
    @TableField(value = "images")
    private String images;

    /**
     * 分类图标
     */
    @TableField(value = "ico")
    private String ico;

    /**
     * 是否显示（0是1否）
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 关键字
     */
    @TableField(value = "keyword")
    private String keyword;

    /**
     * 自定义顺序
     */
    @TableField(value = "sort")
    private Integer sort;
}