package com.bcy.entity.pet;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bcy.entity.CommonEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 文章详情
 *
 * <AUTHOR>
 * @since 2024-08-21
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "bcy_pet_content_detail")
@Data
public class BcyPetContentDetailEntity extends CommonEntity {
    /**
     * 文章详情ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 文章ID
     */
    @TableField(value = "content_id")
    private Long contentId;

    /**
     * 文章内容
     */
    @TableField(value = "details")
    private String details;
}