package com.bcy.entity.pet;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bcy.entity.CommonEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 文章用户关联表
 *
 * <AUTHOR>
 * @since 2024-08-21
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "bcy_pet_content_user")
@Data
public class BcyPetContentUserEntity extends CommonEntity {
    /**
     * 关联ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 文章Id
     */
    @TableField(value = "content_id")
    private Long contentId;

    /**
     * 用户Id
     */
    @TableField(value = "user_id")
    private Long userId;

    /**
     * 类型(0点赞1收藏)
     */
    @TableField(value = "type")
    private Integer type;

}