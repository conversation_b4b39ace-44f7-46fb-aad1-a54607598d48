package com.bcy.entity.site;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bcy.entity.CommonEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 网站文章表
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "bcy_site_content")
@Data
public class BcySiteContentEntity extends CommonEntity {
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 所属栏目ID
     */
    @TableField(value = "category_id")
    private Long category_id;

    /**
     * 站点ID
     */
    @TableField(value = "app_id")
    private Long app_id;

    /**
     * 文章内容
     */
    @TableField(value = "details")
    private String details;

    /**
     * 文章标题
     */
    @TableField(value = "title")
    private String title;

    /**
     * 文章副标题
     */
    @TableField(value = "sub_title")
    private String sub_title;

    /**
     * 关键字
     */
    @TableField(value = "keyword")
    private String keyword;

    /**
     * 描述
     */
    @TableField(value = "description")
    private String description;

    /**
     * 文章缩略图
     */
    @TableField(value = "images")
    private String images;

    /**
     * 文章来源
     */
    @TableField(value = "source")
    private String source;

    /**
     * 文章作者
     */
    @TableField(value = "author")
    private String author;

    /**
     * 是否显示(0显示 1不显示)
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 文章类型
     */
    @TableField(value = "type")
    private String type;

    /**
     * 发布时间
     */
    @TableField(value = "push_datetime")
    private Date push_datetime;

    /**
     * 自定义顺序
     */
    @TableField(value = "sort")
    private Integer sort;

}