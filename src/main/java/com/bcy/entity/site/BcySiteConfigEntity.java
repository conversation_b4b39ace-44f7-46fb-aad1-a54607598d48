package com.bcy.entity.site;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bcy.entity.CommonEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 网站配置表
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "bcy_site_config")
@Data
public class BcySiteConfigEntity extends CommonEntity {
    /**
     * 站点id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 站点名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 站点域名，多个回车换行显示
     */
    @TableField(value = "url")
    private String url;

    /**
     * ico图标
     */
    @TableField(value = "icon")
    private String icon;

    /**
     * 网站logo
     */
    @TableField(value = "logo")
    private String logo;

    /**
     * 站点关键字
     */
    @TableField(value = "keyword")
    private String keyword;

    /**
     * 站点版权信息
     */
    @TableField(value = "copyright")
    private String copyright;

    /**
     * 联系电话
     */
    @TableField(value = "link_phone")
    private String link_phone;

    /**
     * 联系地址
     */
    @TableField(value = "link_address")
    private String link_address;

    /**
     * 地图坐标
     */
    @TableField(value = "lat_lng")
    private String lat_lng;

    /**
     * 二维码
     */
    @TableField(value = "qr_code")
    private String qr_code;

    /**
     * 邮箱地址
     */
    @TableField(value = "email")
    private String email;

    /**
     * 备案号
     */
    @TableField(value = "beian")
    private String beian;

    /**
     * 网安备案号
     */
    @TableField(value = "beian_net")
    private String beian_net;

    /**
     * 站点模板
     */
    @TableField(value = "model")
    private String model;

    /**
     * 描述
     */
    @TableField(value = "description")
    private String description;

    /**
     * 0运行中 1已停止
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 网站生成的目录
     */
    @TableField(value = "static_dir")
    private String static_dir;
}