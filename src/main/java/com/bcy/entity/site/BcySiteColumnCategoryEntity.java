package com.bcy.entity.site;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bcy.entity.CommonEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 网站栏目分类表
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "bcy_site_column_category")
@Data
public class BcySiteColumnCategoryEntity extends CommonEntity {
    /**
     * 栏目ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 站点ID
     */
    @TableField(value = "app_id")
    private Long app_id;

    /**
     * 父级ID
     */
    @TableField(value = "parent_id")
    private Long parent_id;

    /**
     * 标题
     */
    @TableField(value = "title")
    private String title;

    /**
     * 副标题
     */
    @TableField(value = "sub_title")
    private String sub_title;

    /**
     * banner图
     */
    @TableField(value = "banner")
    private String banner;

    /**
     * 是否显示（0是1否）
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 栏目管理描述
     */
    @TableField(value = "description")
    private String description;

    /**
     * 栏目管理关键字
     */
    @TableField(value = "keyword")
    private String keyword;

    /**
     * 页面链接
     */
    @TableField(value = "url")
    private String url;

    /**
     * 自定义顺序
     */
    @TableField(value = "sort")
    private Integer sort;
}