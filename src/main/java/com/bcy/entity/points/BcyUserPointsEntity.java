package com.bcy.entity.points;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bcy.entity.CommonEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户积分表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bcy_user_points")
public class BcyUserPointsEntity extends CommonEntity {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;
    /**
     * 当前可用积分
     */
    @TableField("total_points")
    private Integer totalPoints;
    /**
     * 冻结积分
     */
    @TableField("frozen_points")
    private Integer frozenPoints;
    /**
     * 已过期积分
     */
    @TableField("expired_points")
    private Integer expiredPoints;
} 