package com.bcy.entity.points;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bcy.entity.CommonEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 积分明细表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bcy_user_points_log")
public class BcyUserPointsLogEntity extends CommonEntity {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;
    /**
     * 变动类型（1签到、2任务等）
     */
    @TableField("change_type")
    private Integer changeType;
    /**
     * 变动积分（正/负）
     */
    @TableField("change_value")
    private Integer changeValue;
    /**
     * 变动前积分
     */
    @TableField("before_points")
    private Integer beforePoints;
    /**
     * 变动后积分
     */
    @TableField("after_points")
    private Integer afterPoints;
    /**
     * 关联业务ID（如任务ID）
     */
    @TableField("related_id")
    private Long relatedId;
    /**
     * 备注
     */
    @TableField("remark")
    private String remark;
} 