package com.bcy.entity.points;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bcy.entity.CommonEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 发货表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bcy_points_delivery")
public class BcyPointsDeliveryEntity extends CommonEntity {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 订单ID
     */
    @TableField("order_id")
    private Long orderId;
    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;
    /**
     * 收货地址ID
     */
    @TableField("address_id")
    private Long addressId;
    /**
     * 物流公司
     */
    @TableField("delivery_company")
    private String deliveryCompany;
    /**
     * 物流单号
     */
    @TableField("delivery_no")
    private String deliveryNo;
    /**
     * 发货状态（0待发货）
     */
    @TableField("delivery_status")
    private Integer deliveryStatus;
    /**
     * 发货时间
     */
    @TableField("delivery_time")
    private Date deliveryTime;
    /**
     * 签收时间
     */
    @TableField("receive_time")
    private Date receiveTime;
} 