package com.bcy.entity.points;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bcy.entity.CommonEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 任务表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bcy_points_task")
public class BcyPointsTaskEntity extends CommonEntity {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 任务名称
     */
    @TableField("name")
    private String name;
    /**
     * 任务类型（1新手、2日常等）
     */
    @TableField("type")
    private Integer type;
    /**
     * 任务描述
     */
    @TableField("description")
    private String description;
    /**
     * 完成奖励积分
     */
    @TableField("points")
    private Integer points;
    /**
     * 每日完成上限（0为不限）
     */
    @TableField("daily_limit")
    private Integer dailyLimit;
    /**
     * 总完成上限（0为不限）
     */
    @TableField("total_limit")
    private Integer totalLimit;
    /**
     * 是否启用（1启用，0禁用）
     */
    @TableField("is_active")
    private Integer isActive;
    /**
     * 开始时间
     */
    @TableField("start_time")
    private Date startTime;
    /**
     * 结束时间
     */
    @TableField("end_time")
    private Date endTime;
} 