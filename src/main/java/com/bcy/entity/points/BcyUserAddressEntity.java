package com.bcy.entity.points;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bcy.entity.CommonEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户收货地址表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bcy_user_address")
public class BcyUserAddressEntity extends CommonEntity {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;
    /**
     * 收件人姓名
     */
    @TableField("receiver_name")
    private String receiverName;
    /**
     * 收件人手机号
     */
    @TableField("receiver_phone")
    private String receiverPhone;
    /**
     * 省
     */
    @TableField("province")
    private String province;
    /**
     * 市
     */
    @TableField("city")
    private String city;
    /**
     * 区/县
     */
    @TableField("district")
    private String district;
    /**
     * 详细地址
     */
    @TableField("address_detail")
    private String addressDetail;
    /**
     * 是否默认地址（1是0否）
     */
    @TableField("is_default")
    private Integer isDefault;
} 