package com.bcy.entity.points;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bcy.entity.CommonEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 积分商品表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bcy_points_goods")
public class BcyPointsGoodsEntity extends CommonEntity {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 商品名称
     */
    @TableField("name")
    private String name;
    /**
     * 商品描述
     */
    @TableField("description")
    private String description;
    /**
     * 所需积分
     */
    @TableField("points_cost")
    private Integer pointsCost;
    /**
     * 库存
     */
    @TableField("stock")
    private Integer stock;
    /**
     * 每人限兑数量
     */
    @TableField("limit_per_user")
    private Integer limitPerUser;
    /**
     * 是否上架(0否1是)
     */
    @TableField("is_active")
    private Integer isActive;
} 