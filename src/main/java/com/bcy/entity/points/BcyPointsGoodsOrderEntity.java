package com.bcy.entity.points;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bcy.entity.CommonEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 积分商品兑换记录表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bcy_points_goods_order")
public class BcyPointsGoodsOrderEntity extends CommonEntity {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;
    /**
     * 商品ID
     */
    @TableField("goods_id")
    private Long goodsId;
    /**
     * 兑换数量
     */
    @TableField("quantity")
    private Integer quantity;
    /**
     * 消耗积分
     */
    @TableField("total_points")
    private Integer totalPoints;
    /**
     * 状态（0待确认1待发货）
     */
    @TableField("status")
    private Integer status;
    /**
     * 收货地址
     */
    @TableField("address_id")
    private Long addressId;
} 