package com.bcy.entity.points;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bcy.entity.CommonEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 用户邀请表
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bcy_user_invite")
public class BcyUserInviteEntity extends CommonEntity {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 邀请人用户ID
     */
    @TableField("inviter_id")
    private Long inviterId;
    /**
     * 被邀请人用户ID
     */
    @TableField("invitee_id")
    private Long inviteeId;
    /**
     * 邀请时间
     */
    @TableField("invite_time")
    private Date inviteTime;
} 