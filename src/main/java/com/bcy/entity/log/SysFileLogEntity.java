package com.bcy.entity.log;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bcy.entity.CommonEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 文件日志表
 *
 * <AUTHOR>
 * @since 2023-08-22 16:35:28
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sys_file_log")
@Data
public class SysFileLogEntity extends CommonEntity {
    /**
     * 文件日志Id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户Id
     */
    @TableField(value = "user_id")
    private Long userId;

    /**
     * 文件路径
     */
    @TableField(value = "file_url")
    private String fileUrl;

    /**
     * 文件名称
     */
    @TableField(value = "file_name")
    private String fileName;

    /**
     * 文件路径
     */
    @TableField(value = "file_path")
    private String filePath;

    /**
     * 源文件名称
     */
    @TableField(value = "source_file_name")
    private String sourceFileName;

    /**
     * 状态（0未下载1已下载）
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 失败原因
     */
    @TableField(value = "err_msg")
    private String errMsg;

    /**
     * 执行时长(毫秒)
     */
    @TableField(value = "time")
    private Long time;

    /**
     * 导出时间
     */
    @TableField(value = "export_time")
    private Date exportTime;
}
