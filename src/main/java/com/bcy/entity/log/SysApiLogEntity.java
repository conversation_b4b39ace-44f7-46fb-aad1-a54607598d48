package com.bcy.entity.log;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bcy.entity.CommonEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * API日志表
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sys_api_log")
@Data
public class SysApiLogEntity extends CommonEntity {
    /**
     * API日志Id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 操作类别（0:其它,1:后台用户,2:手机端用户）
     */
    @TableField(value = "operator_type")
    private Integer operatorType;

    /**
     * 操作名称
     */
    @TableField(value = "operator_name")
    private String operatorName;

    /**
     * 模块
     */
    @TableField(value = "model")
    private String model;

    /**
     * 请求地址
     */
    @TableField(value = "url")
    private String url;

    /**
     * 操作状态(0:成功,1:失败)
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 请求方法
     */
    @TableField(value = "method")
    private String method;

    /**
     * 执行时长(毫秒)
     */
    @TableField(value = "time")
    private Long time;

    /**
     * IP地址
     */
    @TableField(value = "ip")
    private String ip;

    /**
     * IP地址
     */
    @TableField(value = "ip_address")
    private String ipAddress;
}