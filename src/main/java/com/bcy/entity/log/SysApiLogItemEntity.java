package com.bcy.entity.log;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bcy.entity.CommonEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * API日志详情表
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sys_api_log_item")
@Data
public class SysApiLogItemEntity extends CommonEntity {
    /**
     * API日志详情Id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * API日志ID
     */
    @TableField(value = "log_id")
    private Long logId;

    /**
     * 请求参数
     */
    @TableField(value = "params")
    private String params;

    /**
     * 请求结果
     */
    @TableField(value = "result")
    private String result;

    /**
     * 错误信息
     */
    @TableField(value = "error_msg")
    private String errorMsg;
}