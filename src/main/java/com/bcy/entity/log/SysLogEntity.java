package com.bcy.entity.log;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 系统日志
 *
 * <AUTHOR>
 * @since 2024-11-04
 */
@Data
@TableName("sys_log")
public class SysLogEntity {
    /**
     * 关联ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户名称
     */
    @TableField(value = "user_name")
    private String userName;

    /**
     * 操作名称
     */
    @TableField(value = "operation")
    private String operation;

    /**
     * 操作类别（0:其它,1:后台用户,2:手机端用户）
     */
    @TableField(value = "operator_type")
    private Integer operatorType;

    /**
     * 操作状态(0:正常,1:异常)
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 请求方法
     */
    @TableField(value = "method")
    private String method;

    /**
     * 请求参数
     */
    @TableField(value = "params")
    private String params;

    /**
     * 操作模块
     */
    @TableField(value = "model")
    private String model;

    /**
     * 请求结果
     */
    @TableField(value = "result")
    private String result;

    /**
     * 错误信息
     */
    @TableField(value = "error_msg")
    private String errorMsg;

    /**
     * 请求时间
     */
    @TableField(value = "time")
    private Long time;

    /**
     * ip地址
     */
    @TableField(value = "ip")
    private String ip;

    /**
     * IP地址
     */
    @TableField(value = "ip_address")
    private String ipAddress;

    /**
     * url
     */
    @TableField(value = "url")
    private String url;

    /**
     * 创建时间
     */
    @TableField(value = "create_date")
    private Date createDate;

}
