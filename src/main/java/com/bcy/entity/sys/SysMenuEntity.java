package com.bcy.entity.sys;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bcy.entity.CommonEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 菜单管理
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("sys_menu")
public class SysMenuEntity extends CommonEntity {

    /**
     * 菜单Id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 父菜单ID
     */
    @TableField(value = "parent_id")
    private Long parentId;

    /**
     * 菜单名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 菜单类型(MENU:菜单 CATALOG:目录 BUTTON:按钮 EXTLINK:外链)
     */
    @TableField(value = "type")
    private String type;

    /**
     * 路由路径(浏览器地址栏路径)
     */
    @TableField(value = "path")
    private String path;

    /**
     * 组件路径(vue页面完整路径，省略.vue后缀)
     */
    @TableField(value = "component")
    private String component;

    /**
     * 权限标识
     */
    @TableField(value = "perm")
    private String perm;

    /**
     * 模块服务
     */
    @TableField(value = "module")
    private String module;

    /**
     * 显示状态(1-显示;0-隐藏)
     */
    @TableField(value = "visible")
    private Integer visible;

    /**
     * 排序
     */
    @TableField(value = "sort")
    private Integer sort;

    /**
     * 菜单图标
     */
    @TableField(value = "icon")
    private String icon;

    /**
     * 跳转路径
     */
    @TableField(value = "redirect")
    private String redirect;

    /**
     * 【目录】只有一个子路由是否始终显示(1:是 0:否)
     */
    @TableField(value = "always_show")
    private Integer alwaysShow;

    /**
     * 【菜单】是否开启页面缓存(1:是 0:否)
     */
    @TableField(value = "keep_alive")
    private Integer keepAlive;
}
