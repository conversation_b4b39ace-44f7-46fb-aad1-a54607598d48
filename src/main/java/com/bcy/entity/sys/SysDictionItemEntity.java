package com.bcy.entity.sys;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bcy.entity.CommonEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 字典详情表
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("sys_diction_item")
public class SysDictionItemEntity extends CommonEntity {
    /**
     * 字典详情id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 字典id
     */
    @TableField(value = "diction_id")
    private Long dictionId;

    /**
     * 名称
     */
    @TableField(value = "name")
    private String name;
    /**
     * 字段名
     */
    @TableField(value = "data_code")
    private String dataCode;

    /**
     * 值
     */
    @TableField(value = "value")
    private String value;

    /**
     * 排序
     */
    @TableField(value = "sort")
    private Integer sort;
}
