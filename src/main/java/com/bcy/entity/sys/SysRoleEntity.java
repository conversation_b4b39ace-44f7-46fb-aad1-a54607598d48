package com.bcy.entity.sys;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bcy.entity.CommonEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 角色表
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("sys_role")
public class SysRoleEntity extends CommonEntity {

    /**
     * 角色Id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 角色名称
     */
    @TableField("name")
    private String name;

    /**
     * 角色编码 ADMIN-超级管理员 USER-普通用户 SYSTEM-系统管理员 OTHER-特殊权限用户 TEST-测试人员 GUEST-演示客户
     */
    @TableField("code")
    private String code;

    /**
     * 角色备注
     */
    @TableField("remark")
    private String remark;
}
