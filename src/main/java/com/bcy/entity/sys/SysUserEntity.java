package com.bcy.entity.sys;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bcy.entity.CommonEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 系统用户表
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("sys_user")
public class SysUserEntity extends CommonEntity {

    /**
     * 用户Id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户名
     */
    @TableField("user_name")
    private String userName;

    /**
     * 工号
     */
    @TableField("job_number")
    private String jobNumber;

    /**
     * 姓名
     */
    @TableField("nick_name")
    private String nickName;

    /**
     * 是否启用（0是1否）
     */
    @TableField("status")
    private Integer status;

    /**
     * 性别
     */
    @TableField("sex")
    private String sex;

    /**
     * 类型
     */
    @TableField("type")
    private Integer type;

    /**
     * 头像路径
     */
    @TableField("avatar")
    private String avatar;

    /**
     * 邮箱
     */
    @TableField("email")
    private String email;

    /**
     * 手机号
     */
    @TableField("phone")
    private String phone;

    /**
     * 密码（md5加密）
     */
    @TableField("password")
    private String password;
    /**
     * 微信openId
     */
    @TableField("open_id")
    private String openId;
    /**
     * 最后登录IP
     */
    @TableField("login_ip")
    private String loginIp;

    /**
     * 登录时间
     */
    @TableField("login_time")
    private Date loginTime;

}
