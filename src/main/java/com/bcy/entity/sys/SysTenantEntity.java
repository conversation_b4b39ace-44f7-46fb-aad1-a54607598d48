package com.bcy.entity.sys;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bcy.entity.CommonEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 租户表
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("sys_tenant")
public class SysTenantEntity extends CommonEntity {
    /**
     * 租户Id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户名称
     */
    @TableField("name")
    private String name;

    /**
     * 租户标识符
     */
    @TableField("app_key")
    private String appKey;

    /**
     * 租户秘钥
     */
    @TableField("secret")
    private String secret;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;
}
