package com.bcy.entity.sys;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bcy.entity.CommonEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 字典表
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("sys_diction")
public class SysDictionEntity extends CommonEntity {
    /**
     * 字典Id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 数值
     */
    @TableField(value = "data_code")
    private String dataCode;
}