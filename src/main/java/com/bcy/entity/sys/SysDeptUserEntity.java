package com.bcy.entity.sys;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bcy.entity.CommonEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 部门用户关联表
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("sys_dept_user")
public class SysDeptUserEntity extends CommonEntity {
    /**
     * 关联Id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 部门Id
     */
    @TableField(value = "dept_id")
    private Long deptId;

    /**
     * 用户Id
     */
    @TableField(value = "user_id")
    private Long userId;
}