package com.bcy.entity.sys;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bcy.entity.CommonEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 模块表
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "sys_module")
public class SysModuleEntity extends CommonEntity {
    /**
     * 模块Id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 模块名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 模块代码
     */
    @TableField(value = "code")
    private String code;

    /**
     * 路径
     */
    @TableField(value = "url")
    private String url;

    /**
     * 状态（0展示1隐藏）
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;
}