package com.bcy.entity.msg;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bcy.entity.CommonEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 信息管理
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName(value = "sys_message")
public class SysMessageEntity extends CommonEntity {
    /**
     * 信息Id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 主题
     */
    @TableField(value = "title")
    private String title;

    /**
     * 消息内容
     */
    @TableField(value = "message")
    private String message;

    /**
     * 是否发送 0：否 1：是
     */
    @TableField(value = "is_push")
    private Integer isPush;

    /**
     * 推送时间
     */
    @TableField(value = "push_time")
    private Date pushTime;

    /**
     * 类型
     */
    @TableField(value = "type")
    private String type;

    /**
     * 通知类型
     */
    @TableField(value = "notice_type")
    private String noticeType;

    /**
     * 消息类型（0系统1个人）
     */
    @TableField(value = "msg_type")
    private Integer msgType;
}