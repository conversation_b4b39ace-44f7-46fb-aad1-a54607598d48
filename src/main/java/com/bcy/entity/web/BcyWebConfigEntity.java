package com.bcy.entity.web;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bcy.entity.CommonEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 留资配置表
 *
 * <AUTHOR>
 * @since 2024-08-21
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "bcy_web_config")
@Data
public class BcyWebConfigEntity extends CommonEntity {
    /**
     * 页面配置ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 站点名称
     */
    @TableField(value = "site_name")
    private String siteName;

    /**
     * ico图标
     */
    @TableField(value = "icon")
    private String icon;

    /**
     * 头像
     */
    @TableField(value = "avatar")
    private String avatar;

    /**
     * 上部分图片
     */
    @TableField(value = "head_image")
    private String headImage;

    /**
     * 下部分图片
     */
    @TableField(value = "bottom_image")
    private String bottomImage;

    /**
     * 站点版权信息
     */
    @TableField(value = "site_copyright")
    private String siteCopyright;

    /**
     * 通知人
     */
    @TableField(value = "site_link_phone")
    private String siteLinkPhone;

    /**
     * 平台类型
     */
    @TableField(value = "platform_type")
    private String platformType;

    /**
     * 按钮描述
     */
    @TableField(value = "button_title")
    private String buttonTitle;


    /**
     * 客服链接
     */
    @TableField(value = "link_url")
    private String linkUrl;

    /**
     * 通知内容
     */
    @TableField(value = "notice_msg")
    private String noticeMsg;

    /**
     * 微信模板ID
     */
    @TableField(value = "wx_template_id")
    private String wxTemplateId;

    /**
     * 通知类型
     */
    @TableField(value = "notice_type")
    private String noticeType;

    /**
     * 状态（0启用1禁用）
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 悬浮窗标题
     */
    @TableField(value = "title")
    private String title;

    /**
     * 悬浮窗标题
     */
    @TableField(value = "sub_title")
    private String subTitle;
}