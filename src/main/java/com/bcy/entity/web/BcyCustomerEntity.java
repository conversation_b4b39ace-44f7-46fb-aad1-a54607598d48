package com.bcy.entity.web;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bcy.entity.CommonEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 客户表
 *
 * <AUTHOR>
 * @since 2024-08-21
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "bcy_customer")
@Data
public class BcyCustomerEntity extends CommonEntity {
    /**
     * 客户ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 客户名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 联系电话
     */
    @TableField(value = "phone")
    private String phone;

    /**
     * 客户需求
     */
    @TableField(value = "demand")
    private String demand;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 流量路径（第三方会添加相关参数）
     */
    @TableField(value = "url")
    private String url;

    /**
     * 广告ID
     */
    @TableField(value = "ads_id")
    private String adsId;

    /**
     * 站点ID
     */
    @TableField(value = "site_id")
    private Long siteId;

    /**
     * 平台类型
     */
    @TableField(value = "platform_type")
    private String platformType;

    /**
     * 是否联系（0否1是）
     */
    @TableField(value = "status")
    private Integer status;
}