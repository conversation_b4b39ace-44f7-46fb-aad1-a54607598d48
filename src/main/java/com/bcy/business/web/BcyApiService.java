package com.bcy.business.web;

import com.bcy.admin.domain.form.SysApiLogForm;
import com.bcy.admin.spi.BcyAdminFeignSpi;
import com.bcy.cms.domain.response.oceanengine.OceanengineResponse;
import com.bcy.cms.utils.OceanengineUtils;
import com.bcy.enums.ResultEnum;
import com.bcy.enums.UserTypeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static com.bcy.cms.constants.BcyCmsApiConstant.OCEANENGINE_CONVERSION_URL;


/**
 * 第三方接口服务
 *
 * <AUTHOR>
 * @since 2024-08-29
 */
@Service
@Slf4j
public class BcyApiService {
    @Resource
    private BcyAdminFeignSpi bcyAdminFeignSpi;

    /**
     * 保存巨量引擎接口API日志
     *
     * @param operator 用户操作
     * @param clickId  表头返回
     * @param evenType 操作类型
     */
    public void saveOceanengineApi(String operator, String clickId, String evenType) {
        List<SysApiLogForm> sysLogFormList = new ArrayList<>();
        SysApiLogForm sysApiLogForm = new SysApiLogForm();
        sysApiLogForm.setUrl(OCEANENGINE_CONVERSION_URL);
        sysApiLogForm.setMethod("POST");
        sysApiLogForm.setOperatorType(UserTypeEnum.APP.getCode());
        sysApiLogForm.setOperatorName(operator);

        // 执行巨量引擎回调
        try {
            if (StringUtils.isNotBlank(clickId)) {
                OceanengineResponse response = OceanengineUtils.getResponse(evenType, clickId);
                sysApiLogForm.setParams(response.getParams());
                sysApiLogForm.setStatus(ResultEnum.SUCCESS.getCode());
                sysApiLogForm.setResult(response.getResponse());
            }
        } catch (Exception e) {
            sysApiLogForm.setStatus(ResultEnum.FAIL.getCode());
            sysApiLogForm.setErrorMsg(e.getMessage());
        } finally {
            sysLogFormList.add(sysApiLogForm);
            bcyAdminFeignSpi.saveApiLogs(sysLogFormList);
        }

    }
}
