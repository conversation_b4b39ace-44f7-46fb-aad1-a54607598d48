package com.bcy.business.web;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bcy.admin.spi.BcyAdminFeignSpi;
import com.bcy.annotation.IgnoreTenant;
import com.bcy.domain.form.IdsForm;
import com.bcy.domain.form.web.BcyWebForm;
import com.bcy.domain.request.PageRequest;
import com.bcy.domain.request.web.BcyWebInfoRequest;
import com.bcy.domain.response.page.web.BcyWebConfigPageResponse;
import com.bcy.domain.response.web.BcyWebResponse;
import com.bcy.entity.web.BcyWebConfigEntity;
import com.bcy.enums.StatusEnum;
import com.bcy.exception.BcyServiceException;
import com.bcy.service.web.IWebConfigService;
import com.bcy.utils.BcyAssertUtils;
import com.bcy.utils.PageUtils;
import com.bcy.utils.RedisUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.bcy.cms.constants.BcyCmsConstants.CACHE_WEB_CONFIG;
import static com.bcy.cms.constants.BcyCmsConstants.WEB_CONFIG_URL;


/**
 * 留资站点服务
 *
 * <AUTHOR>
 * @since 2024-09-19
 */
@Service
@Slf4j
public class BcyWebConfigService {
    @Resource
    private IWebConfigService iWebConfigService;
    @Resource
    private BcyAdminFeignSpi bcyAdminFeignSpi;
    @Resource
    private RedisUtils redisUtils;

    /**
     * 分页获取留资站点信息
     *
     * @param pageRequest 分页表单
     * @return 分页列表
     */
    public PageUtils queryPage(PageRequest pageRequest) {
        List<BcyWebConfigPageResponse> responses = new ArrayList<>();
        IPage<BcyWebConfigEntity> selectPage = iWebConfigService.pageList(pageRequest, null);
        List<BcyWebConfigEntity> pageList = selectPage.getRecords();
        if (CollectionUtil.isNotEmpty(pageList)) {
            pageList.forEach(item -> {
                BcyWebConfigPageResponse response = new BcyWebConfigPageResponse();
                BeanUtils.copyProperties(item, response);
                String webUrl = bcyAdminFeignSpi.configByKey(WEB_CONFIG_URL);
                if (StringUtils.isNotBlank(webUrl)) {
                    response.setSiteUrl(String.format("%s%s&site=%s", webUrl, item.getPlatformType(), item.getId()));
                }
                responses.add(response);
            });
        }
        return new PageUtils(responses, selectPage.getTotal(), pageRequest.getLimit(), pageRequest.getPage());
    }

    /**
     * 根据站点id获取站点信息
     *
     * @param id 站点ID
     * @return 站点信息
     */
    public BcyWebResponse getInfoById(Long id) {
        BcyWebConfigEntity bcyWebConfigEntity = iWebConfigService.getById(id);
        BcyAssertUtils.isNull(bcyWebConfigEntity, "该留资站点不存在！");
        BcyWebResponse bcyWebResponse = new BcyWebResponse();
        BeanUtils.copyProperties(bcyWebConfigEntity, bcyWebResponse);
        bcyWebResponse.setBottomImageList(List.of(bcyWebConfigEntity.getBottomImage().split(",")));
        return bcyWebResponse;
    }

    /**
     * 保存留资站点信息
     *
     * @param bcyWebForm 站点表单
     * @return true/false
     */
    public boolean save(BcyWebForm bcyWebForm) {
        long count = iWebConfigService.countByNameAndPlatform(bcyWebForm.getSiteName(), bcyWebForm.getPlatformType());
        if (count > 0) {
            throw new BcyServiceException("同一平台下已存在该名称！");
        }
        BcyWebConfigEntity bcyWebConfigEntity = new BcyWebConfigEntity();
        BeanUtils.copyProperties(bcyWebForm, bcyWebConfigEntity);
        String images = String.join(",", bcyWebForm.getBottomImageList());
        bcyWebConfigEntity.setBottomImage(images);
        boolean flag = iWebConfigService.save(bcyWebConfigEntity);
        BcyWebResponse bcyWebResponse = new BcyWebResponse();
        BeanUtils.copyProperties(bcyWebConfigEntity, bcyWebResponse);
        bcyWebResponse.setBottomImageList(List.of(bcyWebConfigEntity.getBottomImage().split(",")));
        if (StatusEnum.ENABLE.getCode().equals(bcyWebForm.getStatus())) {
            redisUtils.setCacheObject(String.format(CACHE_WEB_CONFIG, bcyWebConfigEntity.getPlatformType(), bcyWebConfigEntity.getId()), bcyWebResponse);
        }
        return flag;
    }

    /**
     * 更新留资站点信息
     *
     * @param bcyWebForm 站点表单
     * @return true/false
     */
    public boolean update(BcyWebForm bcyWebForm) {
        BcyWebConfigEntity bcyWebConfigEntity = iWebConfigService.getById(bcyWebForm.getId());
        BcyAssertUtils.isNull(bcyWebConfigEntity, "该留资站点不存在！");
        BeanUtils.copyProperties(bcyWebForm, bcyWebConfigEntity);
        String images = String.join(",", bcyWebForm.getBottomImageList());
        bcyWebConfigEntity.setBottomImage(images);
        BcyWebResponse bcyWebResponse = new BcyWebResponse();
        BeanUtils.copyProperties(bcyWebConfigEntity, bcyWebResponse);
        bcyWebResponse.setBottomImageList(List.of(bcyWebConfigEntity.getBottomImage().split(",")));
        if (StatusEnum.ENABLE.getCode().equals(bcyWebForm.getStatus())) {
            redisUtils.setCacheObject(String.format(CACHE_WEB_CONFIG, bcyWebConfigEntity.getPlatformType(), bcyWebConfigEntity.getId()), bcyWebResponse);
        } else {
            redisUtils.deleteObject(String.format(CACHE_WEB_CONFIG, bcyWebConfigEntity.getPlatformType(), bcyWebConfigEntity.getId()));
        }
        return iWebConfigService.updateById(bcyWebConfigEntity);
    }


    /**
     * 删除站点信息
     *
     * @param idsForm 站点ID列表
     * @return true/false
     */
    public boolean delete(IdsForm idsForm) {
        return iWebConfigService.removeBatchByIds(idsForm.getIds());
    }

    /**
     * 更新站点状态
     *
     * @param id 站点ID
     * @return true/false
     */
    public boolean updateStatus(Long id) {
        BcyWebConfigEntity bcyWebConfigEntity = iWebConfigService.getById(id);
        BcyAssertUtils.isNull(bcyWebConfigEntity, "该留资站点不存在！");
        if (Objects.equals(StatusEnum.DISABLE.getCode(), bcyWebConfigEntity.getStatus())) {
            bcyWebConfigEntity.setStatus(StatusEnum.ENABLE.getCode());
        } else {
            bcyWebConfigEntity.setStatus(StatusEnum.DISABLE.getCode());
        }
        BcyWebResponse bcyWebResponse = new BcyWebResponse();
        BeanUtils.copyProperties(bcyWebConfigEntity, bcyWebResponse);
        bcyWebResponse.setBottomImageList(List.of(bcyWebConfigEntity.getBottomImage().split(",")));
        if (StatusEnum.ENABLE.getCode().equals(bcyWebConfigEntity.getStatus())) {
            redisUtils.setCacheObject(String.format(CACHE_WEB_CONFIG, bcyWebConfigEntity.getPlatformType(), bcyWebConfigEntity.getId()), bcyWebResponse);
        } else {
            redisUtils.deleteObject(String.format(CACHE_WEB_CONFIG, bcyWebConfigEntity.getPlatformType(), bcyWebConfigEntity.getId()));
        }
        return iWebConfigService.updateById(bcyWebConfigEntity);
    }

    /**
     * 根据id和平台类型获取留资页面信息
     *
     * @param bcyWebInfoRequest 站点请求表单
     * @return 站点信息
     */
    @IgnoreTenant
    public BcyWebResponse getByIdPlatformType(BcyWebInfoRequest bcyWebInfoRequest) {
        BcyWebConfigEntity bcyWebConfigEntity = iWebConfigService.getById(bcyWebInfoRequest.getId());
        BcyAssertUtils.isNull(bcyWebConfigEntity, "该留资站点不存在！");
        BcyWebResponse siteConfig = redisUtils.getCacheObject(String.format(CACHE_WEB_CONFIG, bcyWebConfigEntity.getPlatformType(), bcyWebConfigEntity.getId()));
        if (Objects.nonNull(siteConfig)) {
            return siteConfig;
        }
        if (StatusEnum.DISABLE.getCode().equals(bcyWebConfigEntity.getStatus())) {
            throw new BcyServiceException("该站点已关闭");
        }
        BcyWebResponse bcyWebResponse = new BcyWebResponse();
        BeanUtils.copyProperties(bcyWebConfigEntity, bcyWebResponse);
        bcyWebResponse.setBottomImageList(List.of(bcyWebConfigEntity.getBottomImage().split(",")));
        redisUtils.setCacheObject(String.format(CACHE_WEB_CONFIG, bcyWebConfigEntity.getPlatformType(), bcyWebConfigEntity.getId()), bcyWebResponse);
        return bcyWebResponse;
    }
}
