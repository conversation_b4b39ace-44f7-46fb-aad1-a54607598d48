package com.bcy.business.web;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bcy.admin.constants.BcyAdminConstants;
import com.bcy.admin.domain.form.FileLogForm;
import com.bcy.admin.domain.form.SendSimpleMailForm;
import com.bcy.admin.domain.form.SendSmsForm;
import com.bcy.admin.domain.request.WechatNoticeRequest;
import com.bcy.admin.enums.NoticeTypeEnum;
import com.bcy.admin.enums.SmsModeEnum;
import com.bcy.admin.spi.BcyAdminFeignSpi;
import com.bcy.admin.spi.BcyAdminSpi;
import com.bcy.annotation.IgnoreTenant;
import com.bcy.config.OssClientProperties;
import com.bcy.domain.form.common.WechatCmsTemplateForm;
import com.bcy.domain.form.web.BcyCustomerForm;
import com.bcy.domain.form.web.BcySeekAdviceForm;
import com.bcy.domain.request.web.BcyCustomerPageRequest;
import com.bcy.domain.response.ExportResponse;
import com.bcy.domain.response.page.web.BcyCustomerPageResponse;
import com.bcy.domain.response.web.BcyCustomerResponse;
import com.bcy.entity.web.BcyCustomerEntity;
import com.bcy.entity.web.BcyWebConfigEntity;
import com.bcy.enums.ExportTypeEnum;
import com.bcy.enums.StatusEnum;
import com.bcy.enums.YesOrNoEnum;
import com.bcy.exception.BcyServiceException;
import com.bcy.service.web.ICustomerService;
import com.bcy.service.web.IWebConfigService;
import com.bcy.service.excel.ExportService;
import com.bcy.utils.BcyAssertUtils;
import com.bcy.utils.PageUtils;
import com.bcy.utils.RedisUtils;
import com.bcy.utils.SecurityContextUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.bcy.cms.constants.BcyCmsApiConstant.OCEANENGINE_CUSTOMER_EFFECTIVE;
import static com.bcy.cms.constants.BcyCmsConstants.SITE_PLATFORM_TYPE;
import static com.bcy.constant.BcyConstants.SPOT_SUFFIX_EXCEL_XLSX;


/**
 * 客户服务
 *
 * <AUTHOR>
 * @since 2024-08-29
 */
@Service
@Slf4j
public class BcyCustomerService {
    @Resource
    private ICustomerService iCustomerService;
    @Resource
    private IWebConfigService iWebConfigService;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private BcyAdminFeignSpi bcyAdminFeignSpi;
    @Resource
    private BcyAdminSpi bcyAdminSpi;
    @Resource
    private ExportService exportService;
    @Resource
    private BcyApiService bcyApiService;
    @Resource
    private OssClientProperties ossClientProperties;


    /**
     * 分页获取客户信息
     *
     * @param pageRequest 分页表单
     * @return 分页列表
     */
    public PageUtils queryPage(BcyCustomerPageRequest pageRequest) {
        List<BcyCustomerPageResponse> responses = new ArrayList<>();
        IPage<BcyCustomerEntity> selectPage = iCustomerService.pageList(pageRequest, null);
        List<BcyCustomerEntity> pageList = selectPage.getRecords();
        if (CollectionUtil.isNotEmpty(pageList)) {
            pageList.forEach(item -> {
                BcyCustomerPageResponse response = new BcyCustomerPageResponse();
                BeanUtils.copyProperties(item, response);
                response.setPlatformTypeName(bcyAdminFeignSpi.dictionItemByCode(SITE_PLATFORM_TYPE, item.getPlatformType()));
                responses.add(response);
            });
        }
        return new PageUtils(responses, selectPage.getTotal(), pageRequest.getLimit(), pageRequest.getPage());
    }

    /**
     * 根据客户id获取客户信息
     *
     * @param id 客户ID
     * @return 客户信息
     */
    public BcyCustomerResponse getInfoById(Long id) {
        BcyCustomerEntity bcyCustomerEntity = iCustomerService.getById(id);
        BcyAssertUtils.isNull(bcyCustomerEntity, "该客户不存在！");
        BcyCustomerResponse bcyCustomerResponse = new BcyCustomerResponse();
        BeanUtils.copyProperties(bcyCustomerEntity, bcyCustomerResponse);
        return bcyCustomerResponse;
    }

    /**
     * 更新客户联系状态
     *
     * @param bcyCustomerForm 客户表单
     * @return true/false
     */
    public boolean updateStatus(BcyCustomerForm bcyCustomerForm) {
        BcyCustomerEntity bcyCustomerEntity = iCustomerService.getById(bcyCustomerForm.getId());
        BcyAssertUtils.isNull(bcyCustomerEntity, "该客户不存在！");
        bcyCustomerEntity.setStatus(YesOrNoEnum.YES.getCode());
        bcyCustomerEntity.setRemark(bcyCustomerForm.getRemark());
        return iCustomerService.updateById(bcyCustomerEntity);
    }

    /**
     * 客户提交表单
     *
     * @param bcySeekAdviceForm 提交表单
     * @return true/false
     */
    @IgnoreTenant
    public boolean seekAdvice(BcySeekAdviceForm bcySeekAdviceForm) {
        BcyWebConfigEntity bcyWebConfigEntity = iWebConfigService.getById(bcySeekAdviceForm.getSiteId());
        BcyAssertUtils.isNull(bcyWebConfigEntity, "该留资站点不存在！");
        if (StatusEnum.DISABLE.getCode().equals(bcyWebConfigEntity.getStatus())) {
            throw new BcyServiceException("该站点已关闭");
        }
        // 判断验证码是否一致
        BcyCustomerEntity bcyCustomerEntity = iCustomerService.getCustomerInfoByPhone(bcySeekAdviceForm.getPhone(), bcySeekAdviceForm.getSiteId());
        BcyAssertUtils.isNotNull(bcyCustomerEntity, "用户已领取相关服务！");
        if (!"9999999".equals(bcySeekAdviceForm.getCode())) {
            // 先校验验证码
            String smsCode = redisUtils.getCacheObject(String.format(BcyAdminConstants.PHONE_CODE_VALIDATE, SmsModeEnum.RECEIVE.name(), bcySeekAdviceForm.getPhone()));
            if (StringUtils.isBlank(smsCode) || !bcySeekAdviceForm.getCode().equals(smsCode)) {
                throw new BcyServiceException("验证码校验失败！");
            }
        }
        bcyCustomerEntity = new BcyCustomerEntity();
        BeanUtils.copyProperties(bcySeekAdviceForm, bcyCustomerEntity);
        bcyCustomerEntity.setPlatformType(bcyWebConfigEntity.getPlatformType());
        SecurityContextUtils.setUserTenant(bcyWebConfigEntity.getTenantId());
        boolean flag = iCustomerService.save(bcyCustomerEntity);
        // 是否进行通知
        if (StringUtils.isNotBlank(bcyWebConfigEntity.getNoticeType()) && StringUtils.isNotBlank(bcyWebConfigEntity.getSiteLinkPhone())) {
            sendNotice(bcySeekAdviceForm, bcyWebConfigEntity);
        }
        String url = bcySeekAdviceForm.getUrl();
        String clickId = "";
        if (StringUtils.isNotBlank(url)) {
            String params = url.substring(url.indexOf("?") + 1);
            if (StringUtils.isNotBlank(params)) {
                String[] column = params.split("&");
                for (String s : column) {
                    String id = s.substring(s.indexOf("=") + 1);
                    String name = s.substring(0, s.indexOf("="));
                    switch (name) {
                        case "clickid" -> clickId = id;
                        case "projectid" -> bcyCustomerEntity.setAdsId(id);
                        case "adid" -> bcyCustomerEntity.setAdsId(id);
                    }
                }
            }
        }

        if (StringUtils.isNotBlank(clickId)) {
            bcyApiService.saveOceanengineApi("领取服务", clickId, OCEANENGINE_CUSTOMER_EFFECTIVE);
        }
        return flag;
    }

    /**
     * 发送消息通知
     *
     * @param bcySeekAdviceForm  用户表单
     * @param bcyWebConfigEntity 站点信息
     */
    @Async
    protected void sendNotice(BcySeekAdviceForm bcySeekAdviceForm, BcyWebConfigEntity bcyWebConfigEntity) {
        try {
            // 判断是否需要通知
            if (NoticeTypeEnum.SMS.getCode().equals(bcyWebConfigEntity.getNoticeType())) {
                SendSmsForm sendSmsForm = new SendSmsForm();
                sendSmsForm.setMode(SmsModeEnum.RECEIVE.name());
                sendSmsForm.setPhoneNumber(bcyWebConfigEntity.getSiteLinkPhone());
                sendSmsForm.setOutId(bcySeekAdviceForm.getPhone());
                String param = "{\"phone\":\"" + bcySeekAdviceForm.getPhone() + "\"}";
                sendSmsForm.setParam(param);
                String response = bcyAdminSpi.sendSms(sendSmsForm);
                if (StringUtils.isNotBlank(response)) {
                    log.error(String.format("发送结果为:%s", response));
                }
            } else if (NoticeTypeEnum.WECHAT.getCode().equals(bcyWebConfigEntity.getNoticeType())) {
                WechatCmsTemplateForm wechatTemplateForm = new WechatCmsTemplateForm();
                wechatTemplateForm.setTemplateId(bcyWebConfigEntity.getWxTemplateId());
                JSONObject data = new JSONObject();
                data.put("phrase3", JSONObject.parse("{\"value\":\"" + bcySeekAdviceForm.getName() + "\"}"));
                data.put("phone_number10", JSONObject.parse("{\"value\":\"" + bcySeekAdviceForm.getPhone() + "\"}"));
                data.put("time2", JSONObject.parse("{\"value\":\"" + DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN) + "\"}"));
                data.put("thing8", JSONObject.parse("{\"value\":\"" + bcySeekAdviceForm.getDemand() + "\"}"));
                wechatTemplateForm.setData(JSONObject.toJSONString(data));
                wechatTemplateForm.setToUser(bcyWebConfigEntity.getSiteLinkPhone());
                WechatNoticeRequest wechatNoticeRequest = new WechatNoticeRequest();
                wechatNoticeRequest.setJsonBody(JSONObject.toJSONString(wechatTemplateForm));
                bcyAdminSpi.sendWechatNotice(wechatNoticeRequest);
            } else if (NoticeTypeEnum.MAIL.getCode().equals(bcyWebConfigEntity.getNoticeType())) {
                SendSimpleMailForm sendSimpleMailForm = new SendSimpleMailForm();
                sendSimpleMailForm.setTo(bcyWebConfigEntity.getSiteLinkPhone());
                sendSimpleMailForm.setSubject("领取通知");
                sendSimpleMailForm.setContent("用户：[" + bcySeekAdviceForm.getPhone() + "]" + bcyWebConfigEntity.getNoticeMsg());
                bcyAdminSpi.sendMail(sendSimpleMailForm);
            }
        } catch (Exception e) {
            log.error("发送消息通知失败！", e);
        }
    }

    /**
     * 验证用户是否存在
     *
     * @param phone  手机号
     * @param siteId 站点ID
     * @return true/false
     */
    @IgnoreTenant
    public boolean checkPhone(String phone, Long siteId) {
        BcyWebConfigEntity bcyWebConfigEntity = iWebConfigService.getById(siteId);
        BcyAssertUtils.isNull(bcyWebConfigEntity, "该留资站点不存在！");
        BcyCustomerEntity bcyCustomerEntity = iCustomerService.getCustomerInfoByPhone(phone, siteId);
        return Objects.isNull(bcyCustomerEntity);
    }

    /**
     * 导出客户列表
     *
     * @param bcyCustomerPageRequest 分页表单
     */
    public void export(BcyCustomerPageRequest bcyCustomerPageRequest) {
        long startTime = System.currentTimeMillis();
        bcyCustomerPageRequest.setPage(BigDecimal.ONE.intValue());
        bcyCustomerPageRequest.setLimit(ossClientProperties.getLimit());
        IPage<BcyCustomerEntity> selectPage = iCustomerService.pageList(bcyCustomerPageRequest, null);
        if (selectPage.getTotal() == 0) {
            throw new BcyServiceException("暂无数据，导出失败！");
        }
        List<BcyCustomerPageResponse> responses = new ArrayList<>();
        exportCustomers(bcyCustomerPageRequest, responses, startTime, StpUtil.getLoginIdAsLong());
    }

    @Async
    protected void exportCustomers(BcyCustomerPageRequest bcyCustomerPageRequest, List<BcyCustomerPageResponse> responses, long startTime, Long userId) {
        getCustomerList(bcyCustomerPageRequest, responses, null);
        String fileName = ExportTypeEnum.CUSTOMER_LIST.getDesc();
        ExportResponse exportResponse = exportService.exportFile(fileName, BcyCustomerPageResponse.class, responses);
        long time = System.currentTimeMillis() - startTime;
        FileLogForm fileLogForm = new FileLogForm();
        fileLogForm.setTime(time);
        fileLogForm.setUserId(userId);
        fileLogForm.setFileName(fileName + SPOT_SUFFIX_EXCEL_XLSX);
        fileLogForm.setFilePath(exportResponse.getFilePath());
        fileLogForm.setSourceFileName(fileName + SPOT_SUFFIX_EXCEL_XLSX);
        fileLogForm.setFileUrl(exportResponse.getUrl());
        bcyAdminFeignSpi.saveFileLog(fileLogForm);
    }


    /**
     * 递归获取数据
     *
     * @param bcyCustomerPageRequest 分页表单
     * @param minId                  最小ID
     * @param responses              列表数据
     */
    private void getCustomerList(BcyCustomerPageRequest bcyCustomerPageRequest, List<BcyCustomerPageResponse> responses, Long minId) {
        IPage<BcyCustomerEntity> selectPage = iCustomerService.pageList(bcyCustomerPageRequest, minId);
        List<BcyCustomerEntity> pageList = selectPage.getRecords();
        if (CollectionUtil.isNotEmpty(pageList)) {
            // 最大id
            minId = pageList.get(pageList.size() - 1).getId();
            pageList.forEach(item -> {
                BcyCustomerPageResponse response = new BcyCustomerPageResponse();
                BeanUtils.copyProperties(item, response);
                response.setPlatformTypeName(bcyAdminFeignSpi.dictionItemByCode(SITE_PLATFORM_TYPE, item.getPlatformType()));
                responses.add(response);
            });
            getCustomerList(bcyCustomerPageRequest, responses, minId);
        }
    }

}
