package com.bcy.business.login;

import com.bcy.domain.response.login.CaptchaResponse;
import com.bcy.exception.BcyServiceException;
import com.bcy.utils.RedisUtils;
import com.google.code.kaptcha.Producer;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.util.concurrent.TimeUnit;

import static com.bcy.admin.constants.BcyAdminConstants.LOGIN_IMAGE_CODE;

/**
 * 验证码相关服务
 *
 * <AUTHOR>
 * @since 2023/3/27-21:38
 */
@Service
@Slf4j
public class CodeService {
    @Value("${bcy.code.expirtime}")
    private long expirtime = 5;

    @Resource
    public RedisUtils redisUtils;
    @Resource
    private Producer producer;

    /**
     * 获取验证码
     *
     * @param uuid 随机数
     * @return 验证码响应
     */
    public CaptchaResponse getCaptcha(String uuid) {
        CaptchaResponse captchaResponse = new CaptchaResponse();
        ByteArrayOutputStream stream = new ByteArrayOutputStream();
        try {
            //生成文字验证码
            String code = producer.createText();
            // 设置三分钟过期
            redisUtils.setCacheObject(LOGIN_IMAGE_CODE + uuid, code, expirtime, TimeUnit.MINUTES);
            //生成图片验证码
            BufferedImage image = producer.createImage(code);
            // 设置图片的格式
            ImageIO.write(image, "jpg", stream);
            byte[] bytes = Base64.encodeBase64(stream.toByteArray());
            String base64 = new String(bytes);
            captchaResponse.setVerifyCodeBase64("data:image/jpeg;base64," + base64);
            captchaResponse.setVerifyCodeKey(code);
        } catch (Exception e) {
            log.info("error：" + e.getMessage());
            throw new BcyServiceException("获取验证码失败");
        }
        return captchaResponse;
    }

}
