package com.bcy.business.login;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.crypto.SecureUtil;
import com.bcy.business.sys.BcySysConfigService;
import com.bcy.constants.BcyAdminBusinessConstants;
import com.bcy.domain.form.login.LoginForm;
import com.bcy.domain.form.user.ForgetPwdForm;
import com.bcy.domain.form.user.RegisterForm;
import com.bcy.domain.request.mini.WechatMiniProgramLoginRequest;
import com.bcy.domain.response.LoginUserResponse;
import com.bcy.domain.response.WechatUserInfoResponse;
import com.bcy.domain.response.login.LoginResponse;
import com.bcy.domain.response.login.SysSettingResponse;
import com.bcy.domain.response.mini.WechatMiniProgramLoginResponse;
import com.bcy.entity.sys.SysUserEntity;
import com.bcy.entity.sys.SysUserTenantEntity;
import com.bcy.enums.StatusEnum;
import com.bcy.enums.UserTypeEnum;
import com.bcy.exception.BcyServiceException;
import com.bcy.service.sys.IUserService;
import com.bcy.service.sys.IUserTenantService;
import com.bcy.service.wechat.WechatAuthService;
import com.bcy.service.wechat.WechatMiniProgramService;
import com.bcy.spi.BcyAdminFeignSpiImpl;
import com.bcy.utils.BcyAssertUtils;
import com.bcy.utils.IpUtils;
import com.bcy.utils.RedisUtils;
import com.bcy.utils.ServletUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.bcy.admin.constants.BcyAdminCacheConstants.FORGET_PWD_CODE;
import static com.bcy.admin.constants.BcyAdminCacheConstants.REGISTER_CODE;
import static com.bcy.admin.constants.BcyAdminConstants.LOGIN_IMAGE_CODE;
import static com.bcy.admin.constants.BcyAdminConstants.LOGIN_USER_INFO;
import static com.bcy.constants.BcyAdminBusinessConstants.CACHE_PERMISSION;
import static com.bcy.constants.BcyAdminBusinessConstants.CACHE_ROLE;
import static com.bcy.constants.BcyAuthConstants.USER_NAME;


/**
 * 登录相关服务
 *
 * <AUTHOR>
 * @since 2024/10/27-21:38
 */
@Service
@Slf4j
public class LoginService {

    @Resource
    public RedisUtils redisUtils;

    @Resource
    private IUserService userService;

    @Resource
    private IUserTenantService iUserTenantService;

    @Resource
    private WechatMiniProgramService wechatMiniProgramService;
    @Resource
    private WechatAuthService wechatAuthService;

    @Resource
    private BcySysConfigService bcySysConfigService;
    @Resource
    private BcyAdminFeignSpiImpl bcyAdminFeignSpi;

    /**
     * 账号密码登录
     *
     * @param loginForm 登录表单
     * @return 登陆信息
     */
    public LoginResponse login(LoginForm loginForm) {
        LoginResponse loginResponse = new LoginResponse();
        SysSettingResponse settingResponse = bcySysConfigService.configSetting();
        if (settingResponse.getIsCode() != 0) {
            // 获取验证码
            String code = redisUtils.getCacheObject(LOGIN_IMAGE_CODE + loginForm.getUuid());
            if (StringUtils.isBlank(code) || !code.equalsIgnoreCase(loginForm.getCaptcha())) {
                throw new BcyServiceException("验证码校验失败，请重试！");
            }
        }
        SysUserEntity user = userService.getUserInfoByPhoneOrUserName(loginForm.getUserName());
        BcyAssertUtils.isNull(user, String.format("【%s】用户不存在", loginForm.getUserName()));
        if (!SecureUtil.md5(loginForm.getPassword()).equals(user.getPassword())) {
            throw new BcyServiceException("用户密码错误");
        }
        //账号锁定
        if (Objects.equals(StatusEnum.DISABLE.getCode(), user.getStatus())) {
            throw new BcyServiceException("账号已被锁定,请联系管理员");
        }
        // 设置相关的用户数据
        LoginUserResponse loginUser = new LoginUserResponse();
        loginUser.setId(user.getId());
        loginUser.setType(UserTypeEnum.getDescByCode(user.getType()));
        loginUser.setUserName(user.getUserName());
        StpUtil.login(user.getId());
        StpUtil.getSession().set(LOGIN_USER_INFO, loginUser);
        StpUtil.getSession().set(USER_NAME, loginUser.getUserName());
        user.setLoginTime(new Date());
        user.setLoginIp(IpUtils.getIpAddress(ServletUtils.getRequest()));
        userService.updateById(user);
        loginResponse.setAccessToken(StpUtil.getTokenValue());
        loginResponse.setExpireTime(StpUtil.getTokenTimeout());
        List<String> permissionList = bcyAdminFeignSpi.getPermissionList();
        if (CollectionUtil.isNotEmpty(permissionList)) {
            redisUtils.setCacheList(CACHE_PERMISSION + user.getId(), permissionList);
        }
        List<String> roleList = bcyAdminFeignSpi.getRoleList();
        if (CollectionUtil.isNotEmpty(permissionList)) {
            redisUtils.setCacheList(CACHE_ROLE + user.getId(), roleList);
        }
        return loginResponse;
    }

    /**
     * 微信登陆
     *
     * @param request 登录表单
     * @return 登陆信息
     */
    public LoginResponse wxLogin(WechatMiniProgramLoginRequest request) {
        LoginResponse loginResponse = new LoginResponse();
        WechatMiniProgramLoginResponse wechatMiniProgramLoginResponse = wechatMiniProgramService.getOpenId(request);
        if (Objects.nonNull(wechatMiniProgramLoginResponse) && "0".equals(wechatMiniProgramLoginResponse.getErrCode())) {
            // 判断用户是否存在
            SysUserEntity user = userService.listByOpenId(wechatMiniProgramLoginResponse.getOpenId());
            if (Objects.isNull(user)) {
                user = new SysUserEntity();
                WechatUserInfoResponse getUserInfo = wechatAuthService.getUserInfo(wechatMiniProgramLoginResponse.getAccessToken(), wechatMiniProgramLoginResponse.getOpenId());
                if (Objects.nonNull(getUserInfo)) {
                    user.setUserName(getUserInfo.getNickName());
                    user.setNickName(getUserInfo.getNickName());
                }
                // 用户不存在 -- 进行新增
                user.setType(UserTypeEnum.WECHAT.getCode());
                user.setOpenId(wechatMiniProgramLoginResponse.getOpenId());
                user.setPassword(SecureUtil.md5(BcyAdminBusinessConstants.DEFAULT_PWD));
                userService.save(user);
            }
            // 设置相关的用户数据
            LoginUserResponse loginUser = new LoginUserResponse();
            loginUser.setId(user.getId());
            loginUser.setType(user.getType().toString());
            loginUser.setUserName(user.getUserName());
            StpUtil.login(user.getId());
            StpUtil.getSession().set(LOGIN_USER_INFO, loginUser);
            StpUtil.getSession().set(USER_NAME, loginUser.getUserName());
            user.setLoginTime(new Date());
            user.setLoginIp(IpUtils.getIpAddress(ServletUtils.getRequest()));
            userService.updateById(user);
            loginResponse.setAccessToken(StpUtil.getTokenValue());
            loginResponse.setExpireTime(StpUtil.getTokenTimeout());
        }
        return loginResponse;
    }

    /**
     * 用户忘记密码
     *
     * @param forgetPwdForm 忘记密码表单
     * @return true/false
     */
    public boolean forgetPwd(ForgetPwdForm forgetPwdForm) {
        String code;
        SysUserEntity user;
        if (BigDecimal.ZERO.intValue() == forgetPwdForm.getCodeType()) {
            // 手机号
            BcyAssertUtils.isBlank(forgetPwdForm.getPhone(), "手机号不能为空！");
            user = userService.listByPhone(forgetPwdForm.getPhone());
            BcyAssertUtils.isNull(user, "该手机号不存在！");
            code = redisUtils.getCacheObject(String.format(FORGET_PWD_CODE, forgetPwdForm.getPhone()));
        } else if (BigDecimal.ONE.intValue() == forgetPwdForm.getCodeType()) {
            BcyAssertUtils.isBlank(forgetPwdForm.getEmail(), "邮箱不能为空！");
            user = userService.listByEmail(forgetPwdForm.getEmail());
            BcyAssertUtils.isNull(user, "该邮箱不存在！");
            code = redisUtils.getCacheObject(String.format(FORGET_PWD_CODE, forgetPwdForm.getEmail()));
        } else {
            throw new BcyServiceException("类型错误，只能为手机号或者邮箱类型！");
        }
        if (StringUtils.isBlank(code) || !forgetPwdForm.getCode().equals(code)) {
            throw new BcyServiceException("验证码错误！");
        }
        if (!forgetPwdForm.getNewPassword().equals(forgetPwdForm.getNewRepeatPassword())) {
            throw new BcyServiceException("两次输入的密码不一致！");
        }
        user.setPassword(SecureUtil.md5(forgetPwdForm.getNewPassword()));
        return userService.updateById(user);
    }

    /**
     * 用户注册
     *
     * @param registerForm 注册表单
     * @return true/false
     */
    public boolean register(RegisterForm registerForm) {
        String code;
        SysUserEntity user;
        if (BigDecimal.ZERO.intValue() == registerForm.getCodeType()) {
            // 手机号
            BcyAssertUtils.isBlank(registerForm.getPhone(), "手机号不能为空！");
            user = userService.listByPhone(registerForm.getPhone());
            BcyAssertUtils.isNotNull(user, "该手机号已存在，请直接登录！");
            code = redisUtils.getCacheObject(String.format(REGISTER_CODE, registerForm.getPhone()));
        } else if (BigDecimal.ONE.intValue() == registerForm.getCodeType()) {
            BcyAssertUtils.isBlank(registerForm.getEmail(), "邮箱不能为空！");
            user = userService.listByEmail(registerForm.getEmail());
            BcyAssertUtils.isNotNull(user, "该邮箱已存在，请直接登录！");
            code = redisUtils.getCacheObject(String.format(REGISTER_CODE, registerForm.getEmail()));
        } else {
            throw new BcyServiceException("类型错误，只能为手机号或者邮箱类型！");
        }
        if (StringUtils.isBlank(code) || !registerForm.getCode().equals(code)) {
            throw new BcyServiceException("验证码错误！");
        }
        user = userService.getUserInfoByPhoneOrUserName(registerForm.getUserName());
        BcyAssertUtils.isNotNull(user, "该用户名已存在，请重新输入");
        user = new SysUserEntity();
        BeanUtils.copyProperties(registerForm, user);
        user.setPassword(SecureUtil.md5(registerForm.getNewPassword()));
        if (StringUtils.isBlank(registerForm.getNickName())) {
            user.setNickName(registerForm.getUserName());
        }
        userService.save(user);
        // 添加租户-默认1
        SysUserTenantEntity sysUserTenantEntity = new SysUserTenantEntity();
        sysUserTenantEntity.setUserId(user.getId());
        sysUserTenantEntity.setLinkTenantId(BigDecimal.ONE.longValue());
        return iUserTenantService.save(sysUserTenantEntity);
    }
}
