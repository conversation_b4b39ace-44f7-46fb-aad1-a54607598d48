package com.bcy.business.sys;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.crypto.SecureUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bcy.config.OssClientProperties;
import com.bcy.constants.BcyAdminBusinessConstants;
import com.bcy.admin.domain.form.FileLogForm;
import com.bcy.domain.form.IdsForm;
import com.bcy.domain.form.OssForm;
import com.bcy.domain.form.user.*;
import com.bcy.domain.response.ExportResponse;
import com.bcy.domain.response.login.SysLoginUserResponse;
import com.bcy.domain.response.page.sys.SysUserPageResponse;
import com.bcy.domain.response.sys.SysMenuResponse;
import com.bcy.domain.response.sys.SysUserResponse;
import com.bcy.entity.sys.*;
import com.bcy.admin.enums.SmsModeEnum;
import com.bcy.enums.StatusEnum;
import com.bcy.enums.ExportTypeEnum;
import com.bcy.enums.UserTypeEnum;
import com.bcy.excel.write.EasyWriteExcelUtils;
import com.bcy.exception.BcyServiceException;
import com.bcy.service.excel.ExportService;
import com.bcy.service.oss.impl.OssSerService;
import com.bcy.service.sys.*;
import com.bcy.business.log.BcyFileLogService;
import com.bcy.domain.form.sys.SysUserForm;
import com.bcy.domain.request.UserPageRequest;
import com.bcy.utils.*;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.bcy.constant.BcyConstants.SPOT_SUFFIX_EXCEL_XLSX;
import static com.bcy.constant.BcyConstants.SUFFIX_EXCEL_XLSX;
import static com.bcy.admin.constants.BcyAdminConstants.*;

/**
 * 系統用户相关服务
 *
 * <AUTHOR>
 * @since 2023/3/27-21:38
 */
@Service
@Slf4j
public class BcySysUserService {
    @Resource
    private BcySysDictionItemService bcySysDictionItemService;
    @Resource
    private IUserService userService;
    @Resource
    private BcySysMenuService bcySysMenuService;
    @Resource
    private ITenantService tenantService;
    @Resource
    private BcySysRoleService bcySysRoleService;
    @Resource
    private IUserRoleService userRoleService;
    @Resource
    private IUserTenantService userTenantService;
    @Resource
    private IDeptUserService deptUserService;
    @Resource
    private IDeptService deptService;
    @Resource
    private BcyFileLogService bcyFileLogService;
    @Resource
    private OssClientProperties ossClientProperties;
    @Resource
    private OssSerService ossSerService;
    @Resource
    private ExportService exportService;
    @Resource
    private RedisUtils redisUtils;

    /**
     * 默认添加超级管理员
     */
    @PostConstruct
    public void init() {
        // 判断超级管理员是否存在
        SysUserEntity user = userService.getById(BigDecimal.ONE.longValue());
        if (Objects.isNull(user)) {
            log.info("超级管理员数据不存在，开始添加");
            user = new SysUserEntity();
            user.setId(BigDecimal.ONE.longValue());
            user.setTenantId(BigDecimal.ZERO.longValue());
            user.setUserName("admin");
            user.setNickName("超级管理员");
            user.setType(UserTypeEnum.SYS.getCode());
            user.setPassword(SecureUtil.md5(BcyAdminBusinessConstants.DEFAULT_PWD));
            user.setEmail("<EMAIL>");
            userService.save(user);
        }
    }

    /**
     * 获取当前登录用户信息
     *
     * @return 登录用户信息
     */
    public SysLoginUserResponse loginUserInfo() {
        SysLoginUserResponse user = new SysLoginUserResponse();
        SysUserEntity sysUserEntity = userService.getById(StpUtil.getLoginIdAsLong());
        BcyAssertUtils.isNull(sysUserEntity, "用户未登录！");
        BeanUtils.copyProperties(sysUserEntity, user);
        SecurityContextUtils.setUserId(user.getId().toString());
        SecurityContextUtils.setUserName(user.getUserName());
        List<SysMenuResponse> menuResponses = bcySysMenuService.getUserMenuList(true, StringUtils.EMPTY);
        Set<String> permissionList = menuResponses.stream().map(SysMenuResponse::getPerm).collect(Collectors.toSet());
        user.setPerms(permissionList);
        SecurityContextUtils.setUserId(sysUserEntity.getId().toString());
        SecurityContextUtils.setUserName(sysUserEntity.getUserName());
        List<SysRoleEntity> roleEntityList = bcySysRoleService.listByLoginUserId();
        Set<String> roleCodeList = roleEntityList.stream().map(SysRoleEntity::getCode).collect(Collectors.toSet());
        if (BcyUtils.isSuperAdmin(user.getId())) {
            roleCodeList.add(ROOT_ROLE);
        }
        user.setRoles(roleCodeList);
        if (BcyUtils.isSuperAdmin(user.getId())) {
            List<SysTenantEntity> tenantEntityList = tenantService.list();
            user.setTenantIds(tenantEntityList.stream().map(SysTenantEntity::getId).toList());
        } else {
            List<SysUserTenantEntity> sysUserTenantEntities = userTenantService.tenantListByUserId(user.getId());
            user.setTenantIds(sysUserTenantEntities.stream().map(SysUserTenantEntity::getTenantId).toList());
        }
        if (CollectionUtil.isNotEmpty(user.getTenantIds())) {
            SecurityContextUtils.setUserTenant(user.getTenantIds().get(0));
        }

        return user;
    }

    /**
     * 用户分页列表
     *
     * @param pageRequest 分页表单
     * @return 用户分页列表
     */
    public PageUtils queryPage(UserPageRequest pageRequest) {
        List<Long> allUser = new ArrayList<>();
        List<SysUserPageResponse> sysLogPageResponses = new ArrayList<>();
        if (Objects.nonNull(pageRequest.getLinkId())) {
            // 有部门过滤部门用户
            List<Long> userIdList = deptUserService.listByDeptId(pageRequest.getLinkId()).stream().map(SysDeptUserEntity::getUserId).toList();
            if (CollectionUtil.isNotEmpty(userIdList)) {
                allUser.addAll(userIdList);
            } else {
                allUser.add(BigDecimal.ZERO.longValue());
            }
        } else {
            // 无部门过滤租户用户
            List<Long> userIds = userTenantService.userListByTenantId(BcyInfoUtils.getUserTenant()).stream().map(SysUserTenantEntity::getUserId).toList();
            if (CollectionUtil.isNotEmpty(userIds)) {
                allUser.addAll(userIds);
            } else {
                allUser.add(BigDecimal.ZERO.longValue());
            }
        }

        IPage<SysUserEntity> page = userService.userPage(pageRequest, null, allUser);
        List<SysUserEntity> sysUserEntities = page.getRecords();

        if (CollectionUtil.isNotEmpty(sysUserEntities)) {
            sysUserEntities.forEach(item -> {
                SysUserPageResponse sysUserPageResponse = new SysUserPageResponse();
                BeanUtils.copyProperties(item, sysUserPageResponse);
                sysUserPageResponse.setStatusName(StatusEnum.getNameByCode(item.getStatus()));
                List<SysDeptUserEntity> sysDeptUserEntityList = deptUserService.listByUserId(item.getId());
                if (CollectionUtil.isNotEmpty(sysDeptUserEntityList)) {
                    SysDeptEntity sysDeptEntity = deptService.getById(sysDeptUserEntityList.get(0).getDeptId());
                    sysUserPageResponse.setDeptName(Objects.nonNull(sysDeptEntity) ? sysDeptEntity.getName() : StringUtils.EMPTY);
                }
                SysDictionItemEntity sysDictionItemEntity = bcySysDictionItemService.getInfByParam(SYS_SEX_TYPE, item.getSex());
                if (Objects.nonNull(sysDictionItemEntity)) {
                    sysUserPageResponse.setSexName(sysDictionItemEntity.getName());
                }
                sysLogPageResponses.add(sysUserPageResponse);
            });
        }
        return new PageUtils(sysLogPageResponses, page.getTotal(), pageRequest.getLimit(), pageRequest.getPage());
    }

    /**
     * 根据用户Id获取用户信息
     *
     * @param userId 用户id
     * @return 用户信息
     */
    public SysUserResponse getInfo(Long userId) {
        SysUserEntity user = userService.getById(userId);
        BcyAssertUtils.isNull(user, String.format("%s:获取用户信息失败", userId));
        SysUserResponse sysUserResponse = new SysUserResponse();
        BeanUtils.copyProperties(user, sysUserResponse);
        List<SysDeptUserEntity> sysDeptUserEntityList = deptUserService.listByUserId(userId);
        if (CollectionUtil.isNotEmpty(sysDeptUserEntityList)) {
            sysUserResponse.setDeptId(sysDeptUserEntityList.get(0).getDeptId());
        }
        List<SysUserRoleEntity> sysUserRoleEntities = userRoleService.userRoleListByUserId(userId);
        if (CollectionUtil.isNotEmpty(sysUserRoleEntities)) {
            sysUserResponse.setRoleIds(sysUserRoleEntities.stream().map(SysUserRoleEntity::getRoleId).toList());
        }
        List<SysUserTenantEntity> sysUserTenantEntities = userTenantService.tenantListByUserId(userId);
        if (CollectionUtil.isNotEmpty(sysUserTenantEntities)) {
            sysUserResponse.setTenantIds(sysUserTenantEntities.stream().map(SysUserTenantEntity::getLinkTenantId).toList());
        }
        return sysUserResponse;
    }

    /**
     * 批量删除用户信息
     *
     * @param idsForm id数组
     * @return boolean
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteUsers(IdsForm idsForm) {
        Long userId = StpUtil.getLoginIdAsLong();
        if (idsForm.getIds().stream().anyMatch(item -> BcyUtils.isSuperAdmin(item) || item.equals(userId))) {
            throw new BcyServiceException("自己和超级管理员账号不可删除！");
        }

        List<SysUserEntity> sysUserEntities = userService.listByIds(idsForm.getIds());
        if (!BcyUtils.isSuperAdmin(StpUtil.getLoginIdAsLong()) && CollectionUtil.isNotEmpty(sysUserEntities) && sysUserEntities.stream().anyMatch(item -> !item.getCreateUser().equalsIgnoreCase(BcyInfoUtils.getUserName()))) {
            throw new BcyServiceException("非自己创建的账号不可删除！");
        }
        // 删除关联数据
        userRoleService.removeByUserIds(idsForm.getIds());
        deptUserService.removeByUserIds(idsForm.getIds());
        userTenantService.removeByUserIds(idsForm.getIds());
        return userService.removeBatchByIds(idsForm.getIds());
    }


    /**
     * 新增用户信息
     *
     * @param userForm 用户表单
     * @return boolean
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveUser(SysUserForm userForm) {
        long nameCount = userService.listByUserName(userForm.getUserName());
        if (nameCount > 0) {
            throw new BcyServiceException(String.format("%s该用户名已存在！", userForm.getPhone()));
        }
        SysUserEntity phoneCount = userService.listByPhone(userForm.getPhone());
        if (Objects.nonNull(phoneCount)) {
            throw new BcyServiceException(String.format("%s该手机号已存在！", userForm.getPhone()));
        }
        SysUserEntity emailCount = userService.listByEmail(userForm.getEmail());
        if (Objects.nonNull(emailCount)) {
            throw new BcyServiceException(String.format("%s该邮箱已存在！", userForm.getEmail()));
        }
        SysUserEntity user = new SysUserEntity();
        BeanUtils.copyProperties(userForm, user);
        user.setPassword(SecureUtil.md5(BcyAdminBusinessConstants.DEFAULT_PWD));
        boolean flag = userService.save(user);
        // 生成工号
        String jobNumber = StringUtils.leftPad(user.getId().toString(), 10, "0");
        user.setJobNumber(jobNumber);
        userService.updateById(user);
        userForm.setId(user.getId());
        saveLinkByUser(userForm, false);
        return flag;
    }

    /**
     * 修改用户信息
     *
     * @param userForm 用户表单
     * @return boolean
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUser(SysUserForm userForm) {
        SysUserEntity user = userService.getById(userForm.getId());
        BcyAssertUtils.isNull(user, String.format("%s用户不存在！", userForm.getId()));
        if (!user.getPhone().equals(userForm.getPhone())) {
            SysUserEntity phoneCount = userService.listByPhone(userForm.getPhone());
            if (Objects.nonNull(phoneCount)) {
                throw new BcyServiceException(String.format("%s该手机号已存在！", userForm.getPhone()));
            }
        }
        if (!user.getEmail().equals(userForm.getEmail())) {
            SysUserEntity emailCount = userService.listByEmail(userForm.getEmail());
            if (Objects.nonNull(emailCount)) {
                throw new BcyServiceException(String.format("%s该邮箱已存在！", userForm.getEmail()));
            }
        }
        BeanUtils.copyProperties(userForm, user);
        saveLinkByUser(userForm, true);
        return userService.updateById(user);
    }

    /**
     * 修改个人用户信息
     *
     * @param personForm 用户表单
     * @return boolean
     */
    public boolean updatePerson(PersonForm personForm) {
        SysUserEntity user = userService.getById(StpUtil.getLoginIdAsLong());
        BcyAssertUtils.isNull(user, String.format("%s用户不存在！", StpUtil.getLoginIdAsLong()));
        user.setNickName(personForm.getNickName());
        user.setSex(personForm.getSex());
        return userService.updateById(user);
    }


    /**
     * 重置密码
     *
     * @param resetPwdForm 重置密码表单
     * @return boolean
     */
    public boolean resetPwd(ResetPwdForm resetPwdForm) {
        SysUserEntity user = userService.getById(resetPwdForm.getUserId());
        BcyAssertUtils.isNull(user, String.format("%s用户不存在！", resetPwdForm.getUserId()));
        if (BcyUtils.isSuperAdmin(resetPwdForm.getUserId())) {
            throw new BcyServiceException("登录用户不允许重置超级管理员密码！");
        }
        user.setPassword(SecureUtil.md5(resetPwdForm.getPassword()));
        return userService.updateById(user);
    }

    /**
     * 用户修改密码
     *
     * @param updatePwdForm 修改密码表单
     * @return boolean
     */
    public boolean updatePwd(UpdatePwdForm updatePwdForm) {
        // 判断旧密码是否正确
        SysUserEntity user = userService.getById(StpUtil.getLoginIdAsLong());
        BcyAssertUtils.isNull(user, String.format("%s用户不存在！", StpUtil.getLoginIdAsLong()));
        if (!SecureUtil.md5(updatePwdForm.getOldPassword()).equals(user.getPassword())) {
            throw new BcyServiceException("旧密码不正确！");
        }
        if (!updatePwdForm.getNewPassword().equals(updatePwdForm.getNewRepeatPassword())) {
            throw new BcyServiceException("两次输入的密码不一致！");
        }
        user.setPassword(SecureUtil.md5(updatePwdForm.getNewPassword()));
        return userService.updateById(user);
    }

    /**
     * 更新用户状态
     *
     * @param id 用户ID
     * @return boolean
     */
    public boolean updateStatus(Long id) {
        if (StpUtil.getLoginIdAsLong() == id || BcyUtils.isSuperAdmin(id)) {
            throw new BcyServiceException("登录用户无法更改超级管理员和自身状态！");
        }
        SysUserEntity user = userService.getById(id);
        BcyAssertUtils.isNull(user, String.format("%s用户不存在！", id));
        user.setStatus(Objects.equals(user.getStatus(), StatusEnum.ENABLE.getCode()) ? StatusEnum.DISABLE.getCode() : StatusEnum.ENABLE.getCode());
        return userService.updateById(user);
    }


    /**
     * 用户关联相关信息
     *
     * @param userForm 用户表单
     * @param isUpdate 是否更新
     */
    private void saveLinkByUser(SysUserForm userForm, boolean isUpdate) {
        if (isUpdate) {
            // 删除角色关联数据
            userRoleService.removeByUserId(userForm.getId());
            // 删除租户关联数据
            userTenantService.removeByUserId(userForm.getId());
            // 删除部门关联数据
            deptUserService.removeByUserId(userForm.getId());
        }
        // 添加角色关联信息
        List<SysUserRoleEntity> roleEntityList = new ArrayList<>();
        userForm.getRoleIds().forEach(item -> {
            SysUserRoleEntity sysUserRoleEntity = new SysUserRoleEntity();
            sysUserRoleEntity.setUserId(userForm.getId());
            sysUserRoleEntity.setRoleId(item);
            roleEntityList.add(sysUserRoleEntity);
        });
        userRoleService.saveBatch(roleEntityList);
        // 添加部门关系列表
        SysDeptUserEntity sysDeptUserEntity = new SysDeptUserEntity();
        sysDeptUserEntity.setDeptId(userForm.getDeptId());
        sysDeptUserEntity.setUserId(userForm.getId());
        deptUserService.save(sysDeptUserEntity);
        // 添加租户关联信息
        List<SysUserTenantEntity> tenantEntities = new ArrayList<>();
        userForm.getTenantIds().forEach(item -> {
            SysUserTenantEntity sysUserTenantEntity = new SysUserTenantEntity();
            sysUserTenantEntity.setUserId(userForm.getId());
            sysUserTenantEntity.setLinkTenantId(item);
            tenantEntities.add(sysUserTenantEntity);
        });
        userTenantService.saveBatch(tenantEntities);
    }

    /**
     * 数据导出
     *
     * @param userPageRequest 分页表单
     */
    public void export(UserPageRequest userPageRequest) {
        // 一直设置第一页
        long startTime = System.currentTimeMillis();
        userPageRequest.setPage(BigDecimal.ONE.intValue());
        userPageRequest.setLimit(ossClientProperties.getLimit());
        PageUtils pageUtils = queryPage(userPageRequest);
        if (CollectionUtil.isEmpty(pageUtils.getRecords())) {
            throw new BcyServiceException("查无数据，导出失败！");
        }
        List<SysUserPageResponse> responses = new ArrayList<>();
        userPageRequest.setUserId(StpUtil.getLoginIdAsLong());
        userPageRequest.setTenantId(BcyInfoUtils.getUserTenant());
        exportUserList(userPageRequest, startTime, responses);
    }

    protected void exportUserList(UserPageRequest userPageRequest, long startTime, List<SysUserPageResponse> responses) {
        getUserList(userPageRequest, responses, null);
        String fileName = ExportTypeEnum.USER_LIST.getDesc();
        ExportResponse exportResponse = exportService.exportFile(fileName, SysUserPageResponse.class, responses);
        long time = (new Date()).getTime() - startTime;
        FileLogForm fileLogForm = new FileLogForm();
        fileLogForm.setTime(time);
        fileLogForm.setUserId(userPageRequest.getUserId());
        fileLogForm.setFileName(fileName + SPOT_SUFFIX_EXCEL_XLSX);
        fileLogForm.setFilePath(exportResponse.getFilePath());
        fileLogForm.setSourceFileName(fileName + SPOT_SUFFIX_EXCEL_XLSX);
        fileLogForm.setFileUrl(exportResponse.getUrl());
        bcyFileLogService.saveLog(fileLogForm);
    }

    /**
     * 递归获取数据
     *
     * @param pageRequest 分页表单
     * @param minId       最小ID
     * @param responses   列表数据
     */
    private void getUserList(UserPageRequest pageRequest, List<SysUserPageResponse> responses, Long minId) {
        List<Long> allUser = new ArrayList<>();
        if (Objects.nonNull(pageRequest.getDeptId())) {
            SecurityContextUtils.setUserTenant(pageRequest.getTenantId());
            List<Long> userIdList = deptUserService.listByDeptId(pageRequest.getDeptId()).stream().map(SysDeptUserEntity::getUserId).toList();
            if (CollectionUtil.isNotEmpty(userIdList)) {
                allUser.addAll(userIdList);
            } else {
                allUser.add(BigDecimal.ZERO.longValue());
            }
        } else {
            List<Long> userIds = userTenantService.userListByTenantId(pageRequest.getTenantId()).stream().map(SysUserTenantEntity::getUserId).toList();
            if (CollectionUtil.isNotEmpty(userIds)) {
                allUser.addAll(userIds);
            } else {
                allUser.add(BigDecimal.ZERO.longValue());
            }
        }
        IPage<SysUserEntity> page = userService.userPage(pageRequest, minId, allUser);
        if (CollectionUtil.isNotEmpty(page.getRecords())) {
            // 最大id
            minId = page.getRecords().get(page.getRecords().size() - 1).getId();
            page.getRecords().forEach(item -> {
                SysUserPageResponse sysUserPageResponse = new SysUserPageResponse();
                BeanUtils.copyProperties(item, sysUserPageResponse);
                sysUserPageResponse.setStatusName(StatusEnum.getNameByCode(item.getStatus()));
                SysDictionItemEntity sysDictionItemEntity = bcySysDictionItemService.getInfByParam(SYS_SEX_TYPE, item.getSex());
                if (Objects.nonNull(sysDictionItemEntity)) {
                    sysUserPageResponse.setSexName(sysDictionItemEntity.getName());
                }
                responses.add(sysUserPageResponse);
            });
            getUserList(pageRequest, responses, minId);
        }
    }

    /**
     * 修改个人用户头像
     *
     * @param avatarForm 头像表单
     * @return boolean
     */
    public boolean updateAvatar(AvatarForm avatarForm) {
        SysUserEntity user = userService.getById(StpUtil.getLoginIdAsLong());
        BcyAssertUtils.isNull(user, String.format("%s用户不存在！", StpUtil.getLoginIdAsLong()));
        user.setAvatar(avatarForm.getAvatar());
        return userService.updateById(user);
    }

    /**
     * 绑定用户手机号或邮箱
     *
     * @param bindLinkForm 绑定信息
     * @return boolean
     */
    public boolean bindLink(BindLinkForm bindLinkForm) {
        SysUserEntity user = userService.getById(StpUtil.getLoginIdAsLong());
        BcyAssertUtils.isNull(user, String.format("%s用户不存在！", StpUtil.getLoginIdAsLong()));
        if (BigDecimal.ZERO.intValue() == bindLinkForm.getCodeType()) {
            String code = redisUtils.getCacheObject(String.format(PHONE_CODE_VALIDATE, SmsModeEnum.BIND.name(), bindLinkForm.getPhone()));
            if (StringUtils.isBlank(code) || !code.equals(bindLinkForm.getCode())) {
                throw new BcyServiceException("验证码校验失败！");
            }
            user.setPhone(bindLinkForm.getPhone());
        } else if (BigDecimal.ONE.intValue() == bindLinkForm.getCodeType()) {
            String code = redisUtils.getCacheObject(String.format(MAIL_CODE_VALIDATE, SmsModeEnum.BIND.name(), bindLinkForm.getEmail()));
            if (StringUtils.isBlank(code) || !code.equals(bindLinkForm.getCode())) {
                throw new BcyServiceException("验证码校验失败！");
            }
            user.setEmail(bindLinkForm.getEmail());
        }
        return userService.updateById(user);
    }
}
