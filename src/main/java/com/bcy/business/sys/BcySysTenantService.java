package com.bcy.business.sys;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.crypto.SecureUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bcy.constants.BcyAdminBusinessConstants;
import com.bcy.domain.form.IdsForm;
import com.bcy.domain.response.page.sys.SysTenantPageResponse;
import com.bcy.domain.response.sys.SysTenantResponse;
import com.bcy.service.sys.ITenantService;
import com.bcy.service.sys.IUserTenantService;
import com.bcy.domain.form.sys.SysTenantForm;
import com.bcy.domain.request.PageRequest;
import com.bcy.entity.sys.SysTenantEntity;
import com.bcy.entity.sys.SysUserTenantEntity;
import com.bcy.exception.BcyServiceException;
import com.bcy.utils.BcyAssertUtils;
import com.bcy.utils.BcyUtils;
import com.bcy.utils.PageUtils;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 系统租户相关服务
 *
 * <AUTHOR>
 * @since 2023/12/26-21:38
 */
@Service
@Slf4j
public class BcySysTenantService {
    @Resource
    private ITenantService tenantService;

    @Resource
    private IUserTenantService userTenantService;

    /**
     * 添加默认租户
     */
    @PostConstruct
    public void saveDefaultTenant() {
        SysTenantEntity tenantEntity = tenantService.getById(BigDecimal.ONE.longValue());
        if (Objects.isNull(tenantEntity)) {
            tenantEntity = new SysTenantEntity();
            tenantEntity.setId(BigDecimal.ONE.longValue());
            tenantEntity.setName(BcyAdminBusinessConstants.TENANT_DEFAULT_NAME);
            tenantEntity.setAppKey(BcyAdminBusinessConstants.TENANT_DEFAULT_APP_KEY);
            tenantEntity.setSecret(SecureUtil.md5(BcyAdminBusinessConstants.TENANT_DEFAULT_APP_KEY));
            tenantEntity.setRemark("博昌云科技默认租户！");
            tenantService.save(tenantEntity);
        }
    }

    /**
     * 租户分页列表
     *
     * @param pageRequest 分页表单
     * @return 分页列表
     */
    public PageUtils queryPage(PageRequest pageRequest) {
        List<SysTenantPageResponse> responses = new ArrayList<>();
        Long userId = StpUtil.getLoginIdAsLong();
        List<Long> tenantIdList;
        if (!BcyUtils.isSuperAdmin(userId)) {
            tenantIdList = userTenantService.tenantListByUserId(userId).stream().map(SysUserTenantEntity::getLinkTenantId).toList();
        } else {
            tenantIdList = tenantService.list().stream().map(SysTenantEntity::getId).toList();
        }
        if (CollectionUtil.isEmpty(tenantIdList)) {
            tenantIdList.add(BigDecimal.ZERO.longValue());
        }
        IPage<SysTenantEntity> iPage = tenantService.tenantPage(pageRequest, tenantIdList);
        List<SysTenantEntity> list = iPage.getRecords();
        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(item -> {
                SysTenantPageResponse tenantPageResponse = new SysTenantPageResponse();
                BeanUtils.copyProperties(item, tenantPageResponse);
                responses.add(tenantPageResponse);
            });
        }
        return new PageUtils(responses, iPage.getTotal(), pageRequest.getLimit(), pageRequest.getPage());
    }

    /**
     * 批量删除租户列表
     *
     * @param idsForm id数组请求
     * @return true/false
     */
    public boolean deleteTenant(IdsForm idsForm) {
        if (idsForm.getIds().contains(BigDecimal.ONE.longValue())) {
            throw new BcyServiceException("默认租户不可删除！");
        }
        // 判断是否有关联用户
        long count = userTenantService.userListByTenantIdList(idsForm.getIds());
        if (count > 0) {
            throw new BcyServiceException("存在绑定用户的租户，请先进行删除！");
        }
        return tenantService.removeBatchByIds(idsForm.getIds());
    }

    /**
     * 根据Id获取租户信息
     *
     * @param id 租户Id
     * @return 租户详情
     */
    public SysTenantResponse getInfoById(Long id) {
        SysTenantEntity sysTenantEntity = tenantService.getById(id);
        BcyAssertUtils.isNull(sysTenantEntity, String.format("获取租户详情信息失败，%s不存在！", id));
        SysTenantResponse sysTenantResponse = new SysTenantResponse();
        BeanUtils.copyProperties(sysTenantEntity, sysTenantResponse);
        return sysTenantResponse;
    }

    /**
     * 添加租户
     *
     * @param tenantForm 租户表单
     * @return true/false
     */
    public boolean saveTenant(SysTenantForm tenantForm) {
        long count = tenantService.countByKey(tenantForm.getAppKey());
        if (count > 0) {
            throw new BcyServiceException("该租户appKey已存在!");
        }
        SysTenantEntity sysTenantEntity = new SysTenantEntity();
        BeanUtils.copyProperties(tenantForm, sysTenantEntity);
        // 自定义生成secret
        sysTenantEntity.setSecret(SecureUtil.md5(String.format("%s:%s", sysTenantEntity.getName(), sysTenantEntity.getAppKey())));
        return tenantService.save(sysTenantEntity);
    }

    /**
     * 更新新租户信息
     *
     * @param tenantForm 租户表单
     * @return true/false
     */
    public boolean updateTenant(SysTenantForm tenantForm) {
        SysTenantEntity sysTenantEntity = tenantService.getById(tenantForm.getId());
        BcyAssertUtils.isNull(sysTenantEntity, String.format("%s租户详情不存在", tenantForm.getId()));
        if (!sysTenantEntity.getAppKey().equals(tenantForm.getAppKey())) {
            long count = tenantService.countByKey(tenantForm.getAppKey());
            if (count > 0) {
                throw new BcyServiceException("该租户appKey已存在!");
            }
        }
        BeanUtils.copyProperties(tenantForm, sysTenantEntity);
        return tenantService.updateById(sysTenantEntity);
    }

    /**
     * 根据租户ID获取已绑定的用户列表
     *
     * @param id 租户ID
     * @return 用户ID列表
     */
    public List<Long> bindListByTenantId(Long id) {
        return userTenantService.userListByTenantId(id).stream().map(SysUserTenantEntity::getUserId).toList();
    }
}
