package com.bcy.business.sys;

import cn.hutool.core.collection.CollectionUtil;
import com.bcy.constants.BcyAdminBusinessConstants;
import com.bcy.domain.response.page.sys.SysDeptPageResponse;
import com.bcy.domain.response.sys.SysDeptResponse;
import com.bcy.service.sys.IDeptService;
import com.bcy.service.sys.IDeptUserService;
import com.bcy.domain.form.sys.SysDeptForm;
import com.bcy.entity.sys.SysDeptEntity;
import com.bcy.exception.BcyServiceException;
import com.bcy.utils.BcyAssertUtils;
import com.bcy.utils.TreeUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 部门相关服务
 *
 * <AUTHOR>
 * @since 2023/12/26-21:38
 */
@Service
@Slf4j
public class BcySysDeptService {
    @Resource
    private IDeptService deptService;
    @Resource
    private IDeptUserService deptUserService;

    /**
     * 获取部门层级列表
     *
     * @param name 部门名称
     * @return 部门层级列表
     */
    public List<SysDeptPageResponse> deptListPage(String name) {
        if (StringUtils.isBlank(name)) {
            // 构建树形结构
            return TreeUtils.buildTreeByTwoLayersFor(deptList(name));
        }
        return deptList(name);
    }

    /**
     * 获取部门层级列表
     *
     * @param name 部门名称
     * @return 部门层级列表
     */
    public List<SysDeptPageResponse> deptList(String name) {
        List<SysDeptPageResponse> list = new ArrayList<>();
        List<SysDeptEntity> deptEntityList = deptService.listByDeptName(name);
        if (StringUtils.isBlank(name) && CollectionUtil.isEmpty(deptEntityList)) {
            // 添加默认顶级部门
            SysDeptEntity sysDeptEntity = new SysDeptEntity();
            sysDeptEntity.setName(BcyAdminBusinessConstants.DEFAULT_DEPT_NAME);
            sysDeptEntity.setParentId(BigDecimal.ZERO.longValue());
            sysDeptEntity.setRemark("默认顶级部门");
            deptService.save(sysDeptEntity);
            deptEntityList.add(sysDeptEntity);
        }
        deptEntityList.forEach(item -> {
            SysDeptPageResponse sysDeptResponse = new SysDeptPageResponse();
            BeanUtils.copyProperties(item, sysDeptResponse);
            list.add(sysDeptResponse);
        });
        return list;
    }

    /**
     * 删除部门
     *
     * @param id 部门ID
     * @return boolean
     */
    public boolean deleteDept(Long id) {
        SysDeptEntity sysDeptEntity = deptService.getById(id);
        BcyAssertUtils.isNull(sysDeptEntity, String.format("%s部门信息不存在", id));
        if (CollectionUtil.isNotEmpty(deptService.childDeptListByDeptId(id)) || CollectionUtil.isNotEmpty(deptUserService.listByDeptId(id))) {
            throw new BcyServiceException("删除失败，部门存在子部门或存在绑定用户");
        }
        return deptService.removeById(id);
    }

    /**
     * 根据Id获取部门信息
     *
     * @param id 部门Id
     * @return 部门信息
     */
    public SysDeptResponse getInfoById(Long id) {
        SysDeptEntity sysDeptEntity = deptService.getById(id);
        BcyAssertUtils.isNull(sysDeptEntity, String.format("%s部门信息不存在", id));
        SysDeptResponse response = new SysDeptResponse();
        BeanUtils.copyProperties(sysDeptEntity, response);
        return response;
    }

    /**
     * 保存部门信息
     *
     * @param deptForm 部门表单
     * @return boolean
     */
    public boolean saveDept(SysDeptForm deptForm) {
        // 判断名称是否存在
        long count = deptService.listByDeptName(deptForm.getName()).size();
        if (count > 0) {
            throw new BcyServiceException(String.format("%s部门名称已存在！", deptForm.getName()));
        }

        SysDeptEntity sysDeptEntity = new SysDeptEntity();
        BeanUtils.copyProperties(deptForm, sysDeptEntity);
        return deptService.save(sysDeptEntity);
    }

    /**
     * 修改部门信息
     *
     * @param deptForm 部门表单
     * @return boolean
     */
    public boolean updateDept(SysDeptForm deptForm) {
        SysDeptEntity sysDeptEntity = deptService.getById(deptForm.getId());
        BcyAssertUtils.isNull(sysDeptEntity, String.format("%s部门不存在", deptForm.getId()));
        if (Objects.equals(deptForm.getId(), deptForm.getParentId())) {
            throw new BcyServiceException("父级部门不能是自身");
        }

        if (!deptForm.getName().equals(sysDeptEntity.getName())) {
            // 判断名称是否存在
            long count = deptService.listByDeptName(deptForm.getName()).size();
            if (count > 0) {
                throw new BcyServiceException(String.format("%s部门名称已存在！", deptForm.getName()));
            }
        }
        // 获取原来的下级ID
        List<SysDeptEntity> chirldList = deptService.childDeptListByDeptId(deptForm.getId());
        if (CollectionUtil.isNotEmpty(chirldList) && chirldList.stream().map(SysDeptEntity::getId).toList().contains(deptForm.getParentId())) {
            throw new BcyServiceException("该部门父级Id不能选择下级部门");
        }
        BeanUtils.copyProperties(deptForm, sysDeptEntity);
        return deptService.updateById(sysDeptEntity);
    }

}
