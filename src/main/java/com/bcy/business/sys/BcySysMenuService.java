package com.bcy.business.sys;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.bcy.constants.BcyAdminBusinessConstants;
import com.bcy.domain.response.route.SysRoutsResponse;
import com.bcy.domain.response.sys.SysMenuResponse;
import com.bcy.enums.MenuTypeEnum;
import com.bcy.service.sys.IMenuService;
import com.bcy.service.sys.IRoleMenuService;
import com.bcy.service.sys.IUserRoleService;
import com.bcy.domain.form.sys.SysMenuForm;
import com.bcy.entity.sys.SysMenuEntity;
import com.bcy.entity.sys.SysRoleMenuEntity;
import com.bcy.entity.sys.SysUserRoleEntity;
import com.bcy.enums.StatusEnum;
import com.bcy.exception.BcyServiceException;
import com.bcy.utils.BcyAssertUtils;
import com.bcy.utils.BcyUtils;
import com.bcy.utils.ServletUtils;
import com.bcy.utils.TreeUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.bcy.constant.BcyConstants.SLASH;
import static com.bcy.admin.constants.BcyAdminConstants.SERVICE_MODULE;


/**
 * 系統菜单相关服务
 *
 * <AUTHOR>
 * @since 2023/12/26-21:38
 */
@Service
@Slf4j
public class BcySysMenuService {
    @Value("${bcy.menu.min}")
    private long minId;

    @Resource
    private IMenuService iMenuService;
    @Resource
    private IRoleMenuService iRoleMenuService;
    @Resource
    private IUserRoleService iUserRoleService;

    /**
     * 获取用户菜单列表
     *
     * @param hasButton 是否带上按钮
     * @param name      菜单名称
     * @return 用户菜单列表
     */
    public List<SysMenuResponse> userMenuList(boolean hasButton, String name) {
        List<SysMenuResponse> list = new ArrayList<>();
        Long userId = StpUtil.getLoginIdAsLong();
        List<Long> menuIdList = new ArrayList<>();
        if (!BcyUtils.isSuperAdmin(userId)) {
            List<Long> userRoleIds = iUserRoleService.userRoleListByUserId(userId).stream().map(SysUserRoleEntity::getRoleId).toList();
            if (CollectionUtil.isNotEmpty(userRoleIds)) {
                menuIdList = iRoleMenuService.roleMenuListByRoleIds(userRoleIds).stream().map(SysRoleMenuEntity::getMenuId).collect(Collectors.toList());
            }
        }
        if (CollectionUtil.isEmpty(menuIdList)) {
            menuIdList.add(BigDecimal.ZERO.longValue());
        }
        List<SysMenuEntity> menuEntities = iMenuService.listByIdList(menuIdList, BcyUtils.isSuperAdmin(userId), name);
        if (!hasButton) {
            menuEntities = menuEntities.stream().filter(sysMenuEntity -> !sysMenuEntity.getType().equals(MenuTypeEnum.MENU_TYPE_BUTTON.getCode())).toList();
        }
        menuEntities.forEach(item -> {
            SysMenuResponse menuResponse = new SysMenuResponse();
            BeanUtils.copyProperties(item, menuResponse);
            if (!menuResponse.getPath().startsWith(SLASH) && !MenuTypeEnum.MENU_TYPE_EXT_LINK.getCode().equals(menuResponse.getType())) {
                // 路由 name 需要驼峰，首字母大写
                String routeName = StringUtils.capitalize(StrUtil.toCamelCase(menuResponse.getPath(), '-'));
                menuResponse.setRouteName(routeName);
            }
            list.add(menuResponse);
        });
        return list;
    }

    /**
     * 获取用户菜单列表(树形)
     *
     * @param hasButton 是否带上按钮
     * @param name      菜单名称
     * @return 用户菜单列表(树形)
     */
    public List<SysMenuResponse> getUserMenuList(boolean hasButton, String name) {
        List<SysMenuResponse> list = userMenuList(hasButton, name);
        return StringUtils.isBlank(name) ? TreeUtils.buildTreeByTwoLayersFor(list) : list;
    }

    /**
     * 获取用户菜单列表(树形)
     *
     * @param hasButton 是否带上按钮
     * @return 用户菜单列表(树形)
     */
    public List<SysRoutsResponse> userNav(boolean hasButton) {
        List<SysRoutsResponse> responses = new ArrayList<>();
        List<SysMenuResponse> list = userMenuList(hasButton, StringUtils.EMPTY);
        if (CollectionUtil.isEmpty(list)) {
            return responses;
        }
        list.stream().filter(sysMenuResponse -> sysMenuResponse.getModule().equalsIgnoreCase(ServletUtils.getRequest().getHeader(SERVICE_MODULE)) || sysMenuResponse.getModule().equalsIgnoreCase(BcyAdminBusinessConstants.API_SERVICE)).forEach(item -> {
            SysRoutsResponse sysRoutsResponse = new SysRoutsResponse();
            BeanUtils.copyProperties(item, sysRoutsResponse);
            // 路由 name 需要驼峰，首字母大写
            String routeName = StringUtils.capitalize(StrUtil.toCamelCase(item.getPath(), '-'));
            sysRoutsResponse.setName(routeName);

            SysRoutsResponse.Meta meta = new SysRoutsResponse.Meta();
            meta.setTitle(item.getName());
            meta.setIcon(item.getIcon());
            meta.setHidden(StatusEnum.ENABLE.getCode().equals(item.getVisible()));
            // 【菜单】是否开启页面缓存
            if (MenuTypeEnum.MENU_TYPE_MENU.getCode().equals(item.getType()) && ObjectUtil.equals(item.getKeepAlive(), BigDecimal.ONE.intValue())) {
                meta.setKeepAlive(true);
            }
            meta.setAlwaysShow(ObjectUtil.equals(item.getAlwaysShow(), BigDecimal.ONE.intValue()));
            sysRoutsResponse.setMeta(meta);
            responses.add(sysRoutsResponse);
        });

        return convertListToTree(responses, BigDecimal.ZERO.longValue());
    }

    /**
     * 转为树形结构
     *
     * @param responses 返回值
     * @return list 树形结构列表
     */
    private List<SysRoutsResponse> convertListToTree(List<SysRoutsResponse> responses, Long parentId) {
        List<SysRoutsResponse> list = new ArrayList<>();
        for (SysRoutsResponse sysRoutsResponse : responses) {
            if (Objects.equals(sysRoutsResponse.getParentId(), parentId)) {
                List<SysRoutsResponse> children = convertListToTree(responses, sysRoutsResponse.getId());
                sysRoutsResponse.setChildren(children);
                list.add(sysRoutsResponse);
            }
        }
        return list;
    }


    /**
     * 新增菜单
     *
     * @param menuForm 菜单表单
     * @return boolean
     */
    public boolean saveMenu(SysMenuForm menuForm) {
        // 判断是否存在菜单名称
        long count = iMenuService.listByName(menuForm.getName());
        if (count > 0) {
            throw new BcyServiceException(String.format("%s该菜单名称已存在!", menuForm.getId()));
        }
        SysMenuEntity menu = new SysMenuEntity();
        BeanUtils.copyProperties(menuForm, menu);
        if (MenuTypeEnum.MENU_TYPE_CATALOGUE.getCode().equals(menu.getType())) {
            String path = menuForm.getPath();
            if (menuForm.getParentId() == BigDecimal.ZERO.longValue() && !path.startsWith(SLASH)) {
                // 一级目录需以 / 开头
                menu.setPath(SLASH + path);
            }
            menu.setComponent("Layout");
        } else if (MenuTypeEnum.MENU_TYPE_EXT_LINK.getCode().equals(menu.getType())) {
            menu.setComponent(StringUtils.EMPTY);
        }
        return iMenuService.save(menu);
    }

    /**
     * 更新菜单
     *
     * @param menuForm 菜单表单
     * @return boolean
     */
    public boolean updateMenu(SysMenuForm menuForm) {
        SysMenuEntity menu = iMenuService.getById(menuForm.getId());
        BcyAssertUtils.isNull(menu, "该菜单不存在！");
        if (menu.getId().equals(menuForm.getParentId())) {
            throw new BcyServiceException("父级ID不能为自身");
        }
        // 获取原来的下级菜单ID
        List<SysMenuEntity> chirldList = iMenuService.listByParentId(menu.getId());
        if (CollectionUtil.isNotEmpty(chirldList) && chirldList.stream().map(SysMenuEntity::getId).toList().contains(menuForm.getParentId())) {
            throw new BcyServiceException("该菜单父级Id选择错误");
        }

        if (!menuForm.getName().equals(menu.getName())) {
            // 判断是否存在菜单名称
            long count = iMenuService.listByName(menuForm.getName());
            if (count > 0) {
                throw new BcyServiceException("该菜单名称已存在!");
            }
        }
        if (MenuTypeEnum.MENU_TYPE_EXT_LINK.getCode().equals(menu.getType())) {
            String path = menuForm.getPath();
            if (menuForm.getParentId() == 0 && !path.startsWith(SLASH)) {
                // 一级目录需以 / 开头
                menuForm.setPath(SLASH + path);
            }
            menuForm.setComponent("Layout");
        } else if (MenuTypeEnum.MENU_TYPE_CATALOGUE.getCode().equals(menu.getType())) {
            menu.setRedirect(StringUtils.EMPTY);
        }
        BeanUtils.copyProperties(menuForm, menu);
        return iMenuService.updateById(menu);
    }

    /**
     * 删除菜单
     *
     * @param id 菜单ID
     * @return boolean
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteMenus(Long id) {
        // 获取菜单列表
        SysMenuEntity sysMenuEntity = iMenuService.getById(id);
        BcyAssertUtils.isNull(sysMenuEntity, "该菜单不存在！");
        List<SysMenuEntity> childList = iMenuService.listByParentId(id);
        if (childList.stream().anyMatch(item -> item.getId() < minId || CollectionUtil.isNotEmpty(iMenuService.listByParentId(item.getId())))) {
            throw new BcyServiceException("批量删除失败，存在下级或者不可删除菜单");
        }
        iRoleMenuService.removeByMenuIdList(Collections.singletonList(id));
        return iMenuService.removeById(id);
    }

    /**
     * 根据菜单Id获取菜单详情
     *
     * @param id 菜单Id
     * @return 菜单详情
     */
    public SysMenuResponse getInfo(Long id) {
        SysMenuEntity sysMenuEntity = iMenuService.getById(id);
        BcyAssertUtils.isNull(sysMenuEntity, "该菜单不存在！");
        SysMenuResponse sysMenuResponse = new SysMenuResponse();
        BeanUtils.copyProperties(sysMenuEntity, sysMenuResponse);
        return sysMenuResponse;
    }
}
