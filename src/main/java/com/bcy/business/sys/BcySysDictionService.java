package com.bcy.business.sys;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bcy.domain.dto.diction.DictionDTO;
import com.bcy.domain.dto.diction.DictionItemDTO;
import com.bcy.domain.response.page.sys.SysDictionPageResponse;
import com.bcy.domain.response.sys.SysDictionResponse;
import com.bcy.service.sys.IDictionItemService;
import com.bcy.service.sys.IDictionService;
import com.bcy.domain.form.IdsForm;
import com.bcy.domain.form.sys.SysDictionForm;
import com.bcy.domain.request.PageRequest;
import com.bcy.entity.sys.SysDictionEntity;
import com.bcy.entity.sys.SysDictionItemEntity;
import com.bcy.exception.BcyServiceException;
import com.bcy.utils.BcyAssertUtils;
import com.bcy.utils.PageUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 系統数据字典相关服务
 *
 * <AUTHOR>
 * @since 2023/12/26-21:38
 */
@Service
@Slf4j
public class BcySysDictionService {
    @Resource
    private IDictionService dictionService;
    @Resource
    private IDictionItemService dictionItemService;

    /**
     * 分页获取字典列表
     *
     * @param pageRequest 分页表单
     * @return 分页列表
     */
    public PageUtils queryPage(PageRequest pageRequest) {
        List<SysDictionPageResponse> list = new ArrayList<>();
        IPage<SysDictionEntity> iPage = dictionService.dictionPage(pageRequest);
        List<SysDictionEntity> sysDictionEntities = iPage.getRecords();
        if (CollectionUtil.isNotEmpty(sysDictionEntities)) {
            sysDictionEntities.forEach(item -> {
                SysDictionPageResponse sysDictionResponse = new SysDictionPageResponse();
                BeanUtils.copyProperties(item, sysDictionResponse);
                list.add(sysDictionResponse);
            });
        }
        return new PageUtils(list, iPage.getTotal(), pageRequest.getLimit(), pageRequest.getPage());
    }

    /**
     * 根据id获取字典详情
     *
     * @param id 字典Id
     * @return 字典详情
     */
    public SysDictionResponse getInfoById(Long id) {
        SysDictionEntity sysDictionEntity = dictionService.getById(id);
        BcyAssertUtils.isNull(sysDictionEntity, String.format("获取字典信息失败，%s不存在！", id));
        SysDictionResponse sysDictionResponse = new SysDictionResponse();
        BeanUtils.copyProperties(sysDictionEntity, sysDictionResponse);
        return sysDictionResponse;
    }

    /**
     * 新增字典信息
     *
     * @param dictionForm 字典表单
     * @return boolean
     */
    public boolean saveDiction(SysDictionForm dictionForm) {
        // 判断是否存在相同key
        long count = dictionService.listByKey(dictionForm.getDataCode());
        if (count > 0) {
            throw new BcyServiceException("该字典标识符已存在!");
        }
        SysDictionEntity sysDictionEntity = new SysDictionEntity();
        BeanUtils.copyProperties(dictionForm, sysDictionEntity);
        return dictionService.save(sysDictionEntity);
    }

    /**
     * 更新字典信息
     *
     * @param dictionForm 字典表单
     * @return boolean
     */
    public boolean updateDiction(SysDictionForm dictionForm) {
        SysDictionEntity sysDictionEntity = dictionService.getById(dictionForm.getId());
        BcyAssertUtils.isNull(sysDictionEntity, String.format("%s对象不存在", dictionForm.getId()));
        if (!dictionForm.getDataCode().equals(sysDictionEntity.getDataCode())) {
            // 判断是否存在相同key
            long count = dictionService.listByKey(dictionForm.getDataCode());
            if (count > 0) {
                throw new BcyServiceException("该字典标识符已存在!");
            }
        }
        BeanUtils.copyProperties(dictionForm, sysDictionEntity);
        return dictionService.updateById(sysDictionEntity);
    }

    /**
     * 批量删除字典信息
     *
     * @param idsForm id数组
     * @return true/false
     */
    public boolean deleteDiction(IdsForm idsForm) {
        long count = dictionItemService.countByDictionIds(idsForm.getIds());
        if (count > 0) {
            throw new BcyServiceException("该字典存在字典项，请先删除!");
        }
        return dictionService.removeBatchByIds(idsForm.getIds());
    }

    /**
     * 所有字典值
     *
     * @return 带字典项列表
     */
    public List<DictionDTO> allList() {
        List<DictionDTO> list = new ArrayList<>();
        List<SysDictionEntity> dictionEntities = dictionService.list();
        if (CollectionUtil.isNotEmpty(dictionEntities)) {
            dictionEntities.forEach(item -> {
                DictionDTO dictionDTO = new DictionDTO();
                dictionDTO.setName(item.getName());
                dictionDTO.setDataCode(item.getDataCode());
                List<SysDictionItemEntity> dictionItemEntities = dictionItemService.listByDictionId(item.getId());
                if (CollectionUtil.isNotEmpty(dictionItemEntities)) {
                    List<DictionItemDTO> itemDTOS = new ArrayList<>();
                    dictionItemEntities.forEach(sysDictionItemEntity -> {
                        DictionItemDTO dictionItemDTO = new DictionItemDTO();
                        dictionItemDTO.setLabel(sysDictionItemEntity.getName());
                        dictionItemDTO.setValue(sysDictionItemEntity.getValue());
                        itemDTOS.add(dictionItemDTO);
                    });
                    dictionDTO.setItemList(itemDTOS);
                }
                list.add(dictionDTO);
            });
        }
        return list;
    }
}
