package com.bcy.business.sys;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bcy.domain.form.IdsForm;
import com.bcy.domain.request.PageRequest;
import com.bcy.domain.response.DropDownResponse;
import com.bcy.domain.response.page.sys.SysDictionItemPageResponse;
import com.bcy.domain.response.sys.SysDictionItemResponse;
import com.bcy.exception.BcyServiceException;
import com.bcy.service.sys.IDictionItemService;
import com.bcy.service.sys.IDictionService;
import com.bcy.domain.form.sys.SysDictionItemForm;
import com.bcy.entity.sys.SysDictionEntity;
import com.bcy.entity.sys.SysDictionItemEntity;
import com.bcy.utils.BcyAssertUtils;
import com.bcy.utils.PageUtils;
import com.bcy.utils.RedisUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static com.bcy.admin.constants.BcyAdminConstants.REDIS_DICT_KEY;

/**
 * 系統数据字典项相关服务
 *
 * <AUTHOR>
 * @since 2023/12/26-21:38
 */
@Service
@Slf4j
public class BcySysDictionItemService {
    @Resource
    private IDictionService iDictionService;
    @Resource
    private IDictionItemService dictionItemService;
    @Resource
    private RedisUtils redisUtils;

    /**
     * 分页获取字典项列表
     *
     * @param pageRequest 分页表单
     * @return 分页列表
     */
    public PageUtils queryPage(PageRequest pageRequest) {
        List<SysDictionItemPageResponse> list = new ArrayList<>();
        IPage<SysDictionItemEntity> iPage = dictionItemService.dictionItemPage(pageRequest);
        List<SysDictionItemEntity> sysDictionEntities = iPage.getRecords();
        if (CollectionUtil.isNotEmpty(sysDictionEntities)) {
            sysDictionEntities.forEach(item -> {
                SysDictionItemPageResponse dictionItemPageResponse = new SysDictionItemPageResponse();
                BeanUtils.copyProperties(item, dictionItemPageResponse);
                list.add(dictionItemPageResponse);
            });
        }
        return new PageUtils(list, iPage.getTotal(), pageRequest.getLimit(), pageRequest.getPage());
    }

    /**
     * 根据id获取字典项详情
     *
     * @param id 字典项Id
     * @return 字典项详情
     */
    public SysDictionItemResponse getInfoById(Long id) {
        SysDictionItemEntity sysDictionItemEntity = dictionItemService.getById(id);
        BcyAssertUtils.isNull(sysDictionItemEntity, String.format("获取字典项信息失败，%s不存在！", id));
        SysDictionItemResponse itemResponse = new SysDictionItemResponse();
        BeanUtils.copyProperties(sysDictionItemEntity, itemResponse);
        return itemResponse;
    }

    /**
     * 新增字典项信息
     *
     * @param dictionItemForm 字典项表单
     * @return boolean
     */
    public boolean saveDiction(SysDictionItemForm dictionItemForm) {
        SysDictionEntity sysDictionEntity = iDictionService.getById(dictionItemForm.getDictionId());
        BcyAssertUtils.isNull(sysDictionEntity, "该字典不存在");
        // 判断是否存在相同key
        long count = dictionItemService.countByValue(dictionItemForm.getDictionId(), dictionItemForm.getValue());
        if (count > 0) {
            throw new BcyServiceException("该字典项Value已存在!");
        }
        SysDictionItemEntity sysDictionItem = new SysDictionItemEntity();
        BeanUtils.copyProperties(dictionItemForm, sysDictionItem);
        sysDictionItem.setDataCode(sysDictionEntity.getDataCode());
        boolean flag = dictionItemService.save(sysDictionItem);
        updateDictRedis();
        return flag;
    }

    /**
     * 更新字典项信息
     *
     * @param dictionItemForm 字典项表单
     * @return boolean
     */
    public boolean updateDiction(SysDictionItemForm dictionItemForm) {
        SysDictionItemEntity sysDictionItem = dictionItemService.getById(dictionItemForm.getId());
        BcyAssertUtils.isNull(sysDictionItem, String.format("%s对象不存在", dictionItemForm.getId()));
        if (!dictionItemForm.getValue().equals(sysDictionItem.getValue())) {
            // 判断是否存在相同key
            long count = dictionItemService.countByValue(dictionItemForm.getDictionId(), dictionItemForm.getValue());
            if (count > 0) {
                throw new BcyServiceException("该字典项标识符已存在!");
            }
        }
        BeanUtils.copyProperties(dictionItemForm, sysDictionItem);
        boolean flag = dictionItemService.updateById(sysDictionItem);
        updateDictRedis();
        return flag;
    }

    /**
     * 批量删除字典项信息
     *
     * @param idsForm id数组
     * @return true/false
     */
    public boolean deleteDiction(IdsForm idsForm) {
        boolean flag = dictionItemService.removeBatchByIds(idsForm.getIds());
        updateDictRedis();
        return flag;
    }

    /**
     * 更新redis字典
     */
    public void updateDictRedis() {
        List<SysDictionEntity> list = iDictionService.list();
        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(item -> {
                        List<SysDictionItemEntity> itemList = dictionItemService.listByDictionId(item.getId());
                        List<DropDownResponse> dropDownResponseList = new ArrayList<>();
                        if (CollectionUtil.isNotEmpty(itemList)) {
                            itemList.forEach(sysDictionItemEntity -> {
                                DropDownResponse dropDownResponse = new DropDownResponse();
                                dropDownResponse.setValueStr(sysDictionItemEntity.getValue());
                                dropDownResponse.setValue(sysDictionItemEntity.getId());
                                dropDownResponse.setKey(sysDictionItemEntity.getId());
                                dropDownResponse.setLabel(sysDictionItemEntity.getName());
                                dropDownResponseList.add(dropDownResponse);
                            });
                        }
                        try {
                            redisUtils.setCacheObject(String.format("%s%s", REDIS_DICT_KEY, item.getDataCode()), dropDownResponseList);
                        } catch (Exception e) {
                            log.error(e.getMessage(), e);
                        }
                    }
            );
        }

    }

    /**
     * 根据code和value获取详情
     *
     * @param dataCode 标识符
     * @param value    字典值
     * @return 字典项详情
     */
    public SysDictionItemEntity getInfByParam(String dataCode, String value) {
        return dictionItemService.getInfByParam(dataCode, value);
    }
}
