package com.bcy.business.sys;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bcy.domain.form.BindIdsForm;
import com.bcy.domain.form.IdsForm;
import com.bcy.domain.response.page.sys.SysRolePageResponse;
import com.bcy.domain.response.sys.SysRoleResponse;
import com.bcy.service.sys.IRoleMenuService;
import com.bcy.service.sys.IRoleService;
import com.bcy.service.sys.IUserRoleService;
import com.bcy.domain.form.sys.SysRoleForm;
import com.bcy.domain.request.PageRequest;
import com.bcy.entity.sys.SysRoleEntity;
import com.bcy.entity.sys.SysRoleMenuEntity;
import com.bcy.entity.sys.SysUserRoleEntity;
import com.bcy.admin.enums.RoleCodeEnum;
import com.bcy.exception.BcyServiceException;
import com.bcy.utils.BcyAssertUtils;
import com.bcy.utils.BcyUtils;
import com.bcy.utils.PageUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 系統角色相关服务
 *
 * <AUTHOR>
 * @since 2023/12/26-21:38
 */
@Service
@Slf4j
public class BcySysRoleService {
    @Resource
    private IRoleService iRoleService;
    @Resource
    private IUserRoleService iUserRoleService;
    @Resource
    private IRoleMenuService iRoleMenuService;

    /**
     * 角色分页列表
     *
     * @param pageRequest 分页表单
     * @return 分页列表
     */
    public PageUtils queryPage(PageRequest pageRequest) {
        Long userId = StpUtil.getLoginIdAsLong();
        List<SysRolePageResponse> responses = new ArrayList<>();
        List<SysRoleEntity> roleEntityList;
        // 获取关联的角色ID列表
        List<Long> roleIdList;
        if (!BcyUtils.isSuperAdmin(userId)) {
            roleIdList = iUserRoleService.userRoleListByUserId(userId).stream().map(SysUserRoleEntity::getRoleId).toList();
        } else {
            roleIdList = iRoleService.list().stream().map(SysRoleEntity::getId).toList();
        }
        if (CollectionUtil.isEmpty(roleIdList)) {
            roleIdList = new ArrayList<>();
            roleIdList.add(BigDecimal.ZERO.longValue());
        }
        IPage<SysRoleEntity> iPage = iRoleService.rolePage(pageRequest, roleIdList);
        roleEntityList = iPage.getRecords();
        if (CollectionUtils.isNotEmpty(roleEntityList)) {
            roleEntityList.forEach(item -> {
                SysRolePageResponse roleResponse = new SysRolePageResponse();
                BeanUtils.copyProperties(item, roleResponse);
                roleResponse.setCodeName(RoleCodeEnum.getDescByCode(item.getCode()));
                responses.add(roleResponse);
            });
        }
        return new PageUtils(responses, iPage.getTotal(), pageRequest.getLimit(), pageRequest.getPage());
    }

    /**
     * 根据id数组删除角色列表
     *
     * @param idsForm id数组
     * @return boolean
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRoles(IdsForm idsForm) {
        // 判断是否存在绑定用户
        long count = iUserRoleService.userRoleListByRoleIdList(idsForm.getIds());
        if (count > 0) {
            throw new BcyServiceException("角色存在绑定的用户，请先解绑！");
        }
        // 删除关联的菜单数据
        iRoleMenuService.removeByRoleIds(idsForm.getIds());
        return iRoleService.removeBatchByIds(idsForm.getIds());
    }

    /**
     * 根据id获取角色信息
     *
     * @param id 角色Id
     * @return 角色信息
     */
    public SysRoleResponse getRoleInfo(Long id) {
        SysRoleEntity role = iRoleService.getById(id);
        BcyAssertUtils.isNull(role, "该角色不存在！");
        SysRoleResponse response = new SysRoleResponse();
        BeanUtils.copyProperties(role, response);
        return response;
    }

    /**
     * 添加角色
     *
     * @param roleForm 角色表单
     * @return boolean
     */
    public boolean saveRole(SysRoleForm roleForm) {
        // 判断是否存在角色名称
        long count = iRoleService.roleListByName(roleForm.getName());
        if (count > 0) {
            throw new BcyServiceException("该角色名称已存在!");
        }
        SysRoleEntity role = new SysRoleEntity();
        BeanUtils.copyProperties(roleForm, role);
        return iRoleService.save(role);
    }

    /**
     * 更新角色
     *
     * @param roleForm 角色表单
     * @return boolean
     */
    public boolean updateRole(SysRoleForm roleForm) {
        SysRoleEntity role = iRoleService.getById(roleForm.getId());
        BcyAssertUtils.isNull(role, "该角色不存在！");
        if (!roleForm.getName().equals(role.getName())) {
            // 判断是否存在角色名称
            long count = iRoleService.roleListByName(roleForm.getName());
            if (count > 0) {
                throw new BcyServiceException("该角色名称已存在!");
            }
        }
        BeanUtils.copyProperties(roleForm, role);
        return iRoleService.updateById(role);
    }

    /**
     * 获取当前登录用户的角色列表
     */
    public List<SysRoleEntity> listByLoginUserId() {
        Long userId = StpUtil.getLoginIdAsLong();
        if (BcyUtils.isSuperAdmin(userId)) {
            return iRoleService.list();
        } else {
            List<Long> roleIdList = iUserRoleService.userRoleListByUserId(userId).stream().map(SysUserRoleEntity::getRoleId).toList();
            if (CollectionUtil.isNotEmpty(roleIdList)) {
                return iRoleService.listByIds(roleIdList);
            }
        }
        return new ArrayList<>();
    }

    /**
     * 角色绑定菜单ID
     *
     * @param bindIdsForm 绑定ID表单
     */
    @Transactional(rollbackFor = Exception.class)
    public void bindMenuIds(BindIdsForm bindIdsForm) {
        SysRoleEntity sysRoleEntity = iRoleService.getById(bindIdsForm.getOriginId());
        BcyAssertUtils.isNull(sysRoleEntity, String.format("%s:该角色不存在！", bindIdsForm.getOriginId()));
        List<SysRoleMenuEntity> sysRoleMenuEntityList = iRoleMenuService.roleMenuListByRoleId(bindIdsForm.getOriginId());
        if (CollectionUtil.isNotEmpty(sysRoleMenuEntityList)) {
            // 删除原来角色关联表数据
            iRoleMenuService.removeBatchByIds(sysRoleMenuEntityList.stream().map(SysRoleMenuEntity::getId).toList());
        }
        // 保存角色菜单关联表
        List<SysRoleMenuEntity> list = new ArrayList<>();
        bindIdsForm.getBindIds().forEach(item -> {
            SysRoleMenuEntity sysRoleMenuEntity = new SysRoleMenuEntity();
            sysRoleMenuEntity.setRoleId(bindIdsForm.getOriginId());
            sysRoleMenuEntity.setMenuId(item);
            list.add(sysRoleMenuEntity);
        });
        if (CollectionUtils.isNotEmpty(list)) {
            iRoleMenuService.saveBatch(list);
        }
    }

    /**
     * 当前角色ID获取已绑定的菜单ID列表
     *
     * @param roleId 角色ID
     * @return 菜单Id列表
     */
    public List<Long> roleMenuIdList(Long roleId) {
        SysRoleEntity sysRoleEntity = iRoleService.getById(roleId);
        BcyAssertUtils.isNull(sysRoleEntity, String.format("%s:该角色不存在！", roleId));
        List<SysRoleMenuEntity> sysRoleMenuEntityList = iRoleMenuService.roleMenuListByRoleId(roleId);
        return sysRoleMenuEntityList.stream().map(SysRoleMenuEntity::getMenuId).toList();
    }
}
