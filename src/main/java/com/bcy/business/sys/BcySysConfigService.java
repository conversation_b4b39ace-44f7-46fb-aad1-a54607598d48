package com.bcy.business.sys;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bcy.annotation.DataPerm;
import com.bcy.constants.BcyAdminBusinessConstants;
import com.bcy.domain.request.PageRequest;
import com.bcy.admin.domain.response.SysConfigResponse;
import com.bcy.domain.response.common.SmsConfigResponse;
import com.bcy.domain.response.login.SysSettingResponse;
import com.bcy.domain.response.page.sys.SysConfigPageResponse;
import com.bcy.enums.StatusEnum;
import com.bcy.exception.BcyServiceException;
import com.bcy.service.sys.IConfigService;
import com.bcy.domain.form.sys.SysConfigForm;
import com.bcy.entity.sys.SysConfigEntity;
import com.bcy.utils.BcyAssertUtils;
import com.bcy.utils.BcyUtils;
import com.bcy.utils.PageUtils;
import com.bcy.utils.RedisUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 系統配置相关服务
 *
 * <AUTHOR>
 * @since 2023/12/26-21:38
 */
@Service
@Slf4j
public class BcySysConfigService {

    @Resource
    private RedisUtils redisUtils;
    @Resource
    private IConfigService configService;

    /**
     * 刷新redis缓存
     */
    public boolean refreshCache() {
        List<SysConfigEntity> list = configService.listByKey(StringUtils.EMPTY, StatusEnum.ENABLE.getCode());
        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(this::setRedisConfig);
        }
        return true;
    }

    /**
     * 分页获取配置列表
     *
     * @param PageRequest 分页表单
     * @return 分页列表
     */
    public PageUtils queryPage(PageRequest PageRequest) {
        List<SysConfigPageResponse> responses = new ArrayList<>();
        IPage<SysConfigEntity> selectPage = configService.configPage(PageRequest);
        List<SysConfigEntity> pageList = selectPage.getRecords();
        if (CollectionUtil.isNotEmpty(pageList)) {
            pageList.forEach(item -> {
                SysConfigPageResponse sysConfigResponse = new SysConfigPageResponse();
                BeanUtils.copyProperties(item, sysConfigResponse);
                sysConfigResponse.setStatusName(StatusEnum.getNameByCode(item.getStatus()));
                responses.add(sysConfigResponse);
            });
        }
        return new PageUtils(responses, selectPage.getTotal(), PageRequest.getLimit(), PageRequest.getPage());
    }

    /**
     * 根据Id获取配置详情
     *
     * @param id 配置Id
     * @return 配置详情
     */
    @DataPerm
    public SysConfigResponse getInfoById(Long id) {
        SysConfigEntity sysConfigEntity = configService.getById(id);
        BcyAssertUtils.isNull(sysConfigEntity, String.format("获取配置信息失败，%s不存在！", id));
        SysConfigResponse response = new SysConfigResponse();
        BeanUtils.copyProperties(sysConfigEntity, response);
        return response;
    }

    /**
     * 批量删除配置信息
     *
     * @param id 配置ID
     * @return true/false
     */
    public boolean deleteConfigs(Long id) {
        SysConfigEntity sysConfigEntity = configService.getById(id);
        BcyAssertUtils.isNull(sysConfigEntity, String.format("获取配置信息失败，%s不存在！", id));
        if (sysConfigEntity.getParamsKey().equals(BcyAdminBusinessConstants.SYS_SETTING)) {
            throw new BcyServiceException("删除失败，配置不允许删除");
        }
        // 删除redis配置
        redisUtils.deleteObject(String.format("%s%s", BcyAdminBusinessConstants.REDIS_CONFIG_KEY, sysConfigEntity.getParamsKey()));
        return configService.removeById(id);
    }

    /**
     * 新增配置
     *
     * @param sysConfigForm 配置表单
     * @return true/false
     */
    public boolean saveConfig(SysConfigForm sysConfigForm) {
        SysConfigEntity sysConfigEntity = new SysConfigEntity();
        // 判断名称是否存在
        long count = configService.listByKey(sysConfigForm.getParamsKey(), null).size();
        if (count > 0) {
            throw new BcyServiceException(String.format("%s配置参数key已存在！", sysConfigForm.getParamsKey()));
        }
        if (sysConfigForm.getParamsValue().contains("{") && !BcyUtils.isJson(sysConfigForm.getParamsValue())) {
            throw new BcyServiceException("请输入正确的json串！");
        }
        BeanUtils.copyProperties(sysConfigForm, sysConfigEntity);
        if (StatusEnum.ENABLE.getCode().equals(sysConfigForm.getStatus())) {
            setRedisConfig(sysConfigEntity);
        }
        return configService.save(sysConfigEntity);
    }

    /**
     * 修改配置
     *
     * @param sysConfigForm 配置表单
     * @return true/false
     */
    public boolean updateConfig(SysConfigForm sysConfigForm) {
        SysConfigEntity sysConfigEntity = configService.getById(sysConfigForm.getId());
        BcyAssertUtils.isNull(sysConfigEntity, String.format("%s配置参数不存在", sysConfigForm.getId()));
        if (!sysConfigForm.getParamsKey().equals(sysConfigEntity.getParamsKey())) {
            throw new BcyServiceException(String.format("%s不能更改！", sysConfigForm.getParamsKey()));
        }
        if (sysConfigForm.getParamsValue().contains("{") && !BcyUtils.isJson(sysConfigForm.getParamsValue())) {
            throw new BcyServiceException("请输入正确的json串！");
        }
        BeanUtils.copyProperties(sysConfigForm, sysConfigEntity);
        setRedisConfig(sysConfigEntity);
        if (StatusEnum.ENABLE.getCode().equals(sysConfigForm.getStatus())) {
            setRedisConfig(sysConfigEntity);
        } else {
            // 删除redis配置
            redisUtils.deleteObject(String.format("%s%s", BcyAdminBusinessConstants.REDIS_CONFIG_KEY, sysConfigEntity.getParamsKey()));
        }
        return configService.updateById(sysConfigEntity);
    }

    /**
     * 将配置保存到redis
     *
     * @param sysConfigEntity 配置信息
     */
    private void setRedisConfig(SysConfigEntity sysConfigEntity) {
        redisUtils.setCacheObject(String.format("%s%s", BcyAdminBusinessConstants.REDIS_CONFIG_KEY, sysConfigEntity.getParamsKey()), sysConfigEntity.getParamsValue());
    }

    /**
     * 启用/禁用配置
     *
     * @param id 配置ID
     */
    public boolean updateStatus(Long id) {
        SysConfigEntity sysConfigEntity = configService.getById(id);
        BcyAssertUtils.isNull(sysConfigEntity, String.format("%s配置参数不存在", id));
        sysConfigEntity.setStatus(StatusEnum.DISABLE.getCode().equals(sysConfigEntity.getStatus()) ? StatusEnum.ENABLE.getCode() : StatusEnum.DISABLE.getCode());
        if (StatusEnum.ENABLE.getCode().equals(sysConfigEntity.getStatus())) {
            setRedisConfig(sysConfigEntity);
        } else {
            // 删除redis配置
            redisUtils.deleteObject(String.format("%s%s", BcyAdminBusinessConstants.REDIS_CONFIG_KEY, sysConfigEntity.getParamsKey()));
        }
        return configService.updateById(sysConfigEntity);
    }

    /**
     * 根据标识符获取配置 -先从redis获取再从数据库获取
     *
     * @param key 标识符号
     * @return 配置详情
     */
    public String getConfigValueByKey(String key) {
        // 先从缓存获取
        String jsonConfig = redisUtils.getCacheObject(String.format("%s%s", BcyAdminBusinessConstants.REDIS_CONFIG_KEY, key));
        if (StringUtils.isBlank(jsonConfig)) {
            List<SysConfigEntity> configEntities = configService.listByKey(key, StatusEnum.ENABLE.getCode());
            if (CollectionUtil.isNotEmpty(configEntities)) {
                jsonConfig = configEntities.get(0).getParamsValue();
                redisUtils.setCacheObject(String.format("%s%s", BcyAdminBusinessConstants.REDIS_CONFIG_KEY, key), jsonConfig);
            }
        }
        return jsonConfig;
    }

    /**
     * 获取页面标题配置
     *
     * @return 页面数据获取
     */
    public SysSettingResponse configSetting() {
        SysSettingResponse settingResponse = new SysSettingResponse();
        String jsonConfig = getConfigValueByKey(BcyAdminBusinessConstants.SYS_SETTING);
        if (StringUtils.isNotBlank(jsonConfig)) {
            settingResponse = JSONObject.parseObject(jsonConfig, SysSettingResponse.class);
        }
        return settingResponse;
    }

    /**
     * 获取短信配置
     *
     * @return 短信配置
     */
    public SmsConfigResponse getSmsConfig(String key) {
        SmsConfigResponse smsConfigResponse = new SmsConfigResponse();
        String jsonConfig = getConfigValueByKey(key);
        if (StringUtils.isNotBlank(jsonConfig)) {
            smsConfigResponse = JSONObject.parseObject(jsonConfig, SmsConfigResponse.class);
        }
        return smsConfigResponse;
    }
}
