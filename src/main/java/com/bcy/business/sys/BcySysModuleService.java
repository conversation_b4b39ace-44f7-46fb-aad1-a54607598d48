package com.bcy.business.sys;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bcy.domain.form.IdsForm;
import com.bcy.domain.response.page.sys.SysModulePageResponse;
import com.bcy.domain.response.sys.SysModuleResponse;
import com.bcy.service.sys.IModuleService;
import com.bcy.domain.form.sys.SysModuleForm;
import com.bcy.domain.request.PageRequest;
import com.bcy.entity.sys.SysModuleEntity;
import com.bcy.enums.StatusEnum;
import com.bcy.exception.BcyServiceException;
import com.bcy.utils.BcyAssertUtils;
import com.bcy.utils.PageUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 系統模块相关服务
 *
 * <AUTHOR>
 * @since 2024/10/26-21:38
 */
@Service
@Slf4j
public class BcySysModuleService {

    @Resource
    private IModuleService moduleService;


    /**
     * 模块列表展示
     *
     * @return 列表
     */
    public List<SysModuleResponse> list() {
        List<SysModuleResponse> sysModuleResponses = new ArrayList<>();
        List<SysModuleEntity> list = moduleService.showList();
        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(item -> {
                SysModuleResponse response = new SysModuleResponse();
                BeanUtils.copyProperties(item, response);
                sysModuleResponses.add(response);
            });
        }
        return sysModuleResponses;
    }

    /**
     * 分页获取模块服务列表
     *
     * @param pageRequest 分页表单
     * @return 分页列表
     */
    public PageUtils queryPage(PageRequest pageRequest) {
        List<SysModulePageResponse> responses = new ArrayList<>();
        IPage<SysModuleEntity> selectPage = moduleService.modulePage(pageRequest);
        List<SysModuleEntity> pageList = selectPage.getRecords();
        if (CollectionUtil.isNotEmpty(pageList)) {
            pageList.forEach(item -> {
                SysModulePageResponse modulePageResponse = new SysModulePageResponse();
                BeanUtils.copyProperties(item, modulePageResponse);
                responses.add(modulePageResponse);
            });
        }
        return new PageUtils(responses, selectPage.getTotal(), pageRequest.getLimit(), pageRequest.getPage());
    }

    /**
     * 根据Id获取模块服务详情
     *
     * @param id 模块服务Id
     * @return 模块服务详情
     */
    public SysModuleResponse getInfoById(Long id) {
        SysModuleEntity sysModuleEntity = moduleService.getById(id);
        BcyAssertUtils.isNull(sysModuleEntity, String.format("获取模块服务信息失败，%s不存在！", id));
        SysModuleResponse response = new SysModuleResponse();
        BeanUtils.copyProperties(sysModuleEntity, response);
        return response;
    }

    /**
     * 批量删除模块服务信息
     *
     * @param idsForm id数组
     * @return true/false
     */
    public boolean deleteModules(IdsForm idsForm) {
        return moduleService.removeBatchByIds(idsForm.getIds());
    }

    /**
     * 新增模块服务
     *
     * @param moduleForm 模块服务表单
     * @return true/false
     */
    public boolean saveModule(SysModuleForm moduleForm) {
        SysModuleEntity sysModuleEntity = new SysModuleEntity();
        // 判断名称是否存在
        long count = moduleService.countByCode(moduleForm.getCode());
        if (count > 0) {
            throw new BcyServiceException(String.format("%s模块服务参数key已存在！", moduleForm.getCode()));
        }
        BeanUtils.copyProperties(moduleForm, sysModuleEntity);
        return moduleService.save(sysModuleEntity);
    }

    /**
     * 修改模块服务
     *
     * @param moduleForm 模块服务表单
     * @return true/false
     */
    public boolean updateModule(SysModuleForm moduleForm) {
        SysModuleEntity sysModuleEntity = moduleService.getById(moduleForm.getId());
        BcyAssertUtils.isNull(sysModuleEntity, String.format("%s模块服务参数不存在", moduleForm.getId()));
        if (!moduleForm.getCode().equals(sysModuleEntity.getCode())) {
            throw new BcyServiceException("标识符不能更改！");
        }
        BeanUtils.copyProperties(moduleForm, sysModuleEntity);
        return moduleService.updateById(sysModuleEntity);
    }

    /**
     * 启用/禁用模块服务
     *
     * @param id 模块服务ID
     * @return true/false
     */
    public boolean updateStatus(Long id) {
        SysModuleEntity sysModuleEntity = moduleService.getById(id);
        BcyAssertUtils.isNull(sysModuleEntity, String.format("%s模块服务参数不存在", id));
        sysModuleEntity.setStatus(StatusEnum.DISABLE.getCode().equals(sysModuleEntity.getStatus()) ? StatusEnum.ENABLE.getCode() : StatusEnum.DISABLE.getCode());
        return moduleService.updateById(sysModuleEntity);
    }


}
