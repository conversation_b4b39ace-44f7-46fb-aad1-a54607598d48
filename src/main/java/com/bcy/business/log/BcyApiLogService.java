package com.bcy.business.log;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bcy.admin.domain.form.SysApiLogForm;
import com.bcy.domain.request.PageRequest;
import com.bcy.domain.response.log.SysApiLogResponse;
import com.bcy.domain.response.page.log.SysApiLogPageResponse;
import com.bcy.service.log.ISysApiLogItemService;
import com.bcy.service.log.ISysApiLogService;
import com.bcy.entity.log.SysApiLogEntity;
import com.bcy.entity.log.SysApiLogItemEntity;
import com.bcy.utils.BcyAssertUtils;
import com.bcy.utils.IpUtils;
import com.bcy.utils.PageUtils;
import com.bcy.utils.ServletUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;


/**
 * API接口日志服务
 *
 * <AUTHOR>
 * @since 2023/3/27-21:38
 */
@Service
@Slf4j
public class BcyApiLogService {
    @Resource
    private ISysApiLogService sysApiLogService;
    @Resource
    private ISysApiLogItemService sysApiLogItemService;

    /**
     * 分页获取系统日志
     *
     * @param pageRequest 日志分页表单
     * @return 系统日志列表
     */
    public PageUtils queryPage(PageRequest pageRequest) {
        List<SysApiLogPageResponse> sysLogPageResponses = new ArrayList<>();
        IPage<SysApiLogEntity> page = sysApiLogService.sysApiLogPage(pageRequest, null);
        List<SysApiLogEntity> sysLogEntities = page.getRecords();
        if (CollectionUtil.isNotEmpty(sysLogEntities)) {
            sysLogEntities.forEach(item -> {
                SysApiLogPageResponse sysApiLogPageResponse = new SysApiLogPageResponse();
                BeanUtils.copyProperties(item, sysApiLogPageResponse);
                sysLogPageResponses.add(sysApiLogPageResponse);
            });
        }
        return new PageUtils(sysLogPageResponses, page.getTotal(), pageRequest.getLimit(), pageRequest.getPage());
    }

    /**
     * 清除n天前的日志信息
     *
     * @param days 天数
     * @return true/false
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteLogs(Integer days) {
        sysApiLogItemService.deleteItemLogs(days);
        return sysApiLogService.deleteLogs(days) > 0;
    }

    /**
     * 获取日志详情
     *
     * @param id 主键
     * @return 详情
     */
    public SysApiLogResponse getInfo(Long id) {
        SysApiLogEntity sysApiLogEntity = sysApiLogService.getById(id);
        BcyAssertUtils.isNull(sysApiLogEntity, "该日志不存在！");
        SysApiLogItemEntity sysApiLogItemEntity = sysApiLogItemService.getInfoByLogId(id);
        BcyAssertUtils.isNull(sysApiLogItemEntity, "该日志详情不存在！");
        SysApiLogResponse sysApiLogResponse = new SysApiLogResponse();
        BeanUtils.copyProperties(sysApiLogEntity, sysApiLogResponse);
        BeanUtils.copyProperties(sysApiLogItemEntity, sysApiLogResponse);
        return sysApiLogResponse;
    }

    /**
     * 批量保存日志
     *
     * @param sysLogFormList 日志表单列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveSysApiLogs(List<SysApiLogForm> sysLogFormList) {
        if (CollectionUtil.isNotEmpty(sysLogFormList)) {
            List<SysApiLogItemEntity> list = new ArrayList<>();
            sysLogFormList.forEach(item -> {
                SysApiLogEntity sysApiLogEntity = new SysApiLogEntity();
                BeanUtils.copyProperties(item, sysApiLogEntity);
                sysApiLogEntity.setTime(System.currentTimeMillis() - item.getStartTime());
                sysApiLogEntity.setIp(IpUtils.getIpAddress(ServletUtils.getRequest()));
                sysApiLogEntity.setIpAddress(IpUtils.getAddressByIP(sysApiLogEntity.getIp()));
                sysApiLogService.save(sysApiLogEntity);
                SysApiLogItemEntity sysApiLogItemEntity = new SysApiLogItemEntity();
                BeanUtils.copyProperties(item, sysApiLogItemEntity);
                sysApiLogItemEntity.setLogId(sysApiLogEntity.getId());
                list.add(sysApiLogItemEntity);
            });
            sysApiLogItemService.saveBatch(list);
        }
    }
}
