package com.bcy.business.log;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bcy.admin.domain.form.SysLogForm;
import com.bcy.domain.request.PageRequest;
import com.bcy.domain.response.page.log.SysLogPageResponse;
import com.bcy.service.log.ISysLogService;
import com.bcy.entity.log.SysLogEntity;
import com.bcy.utils.IpUtils;
import com.bcy.utils.PageUtils;
import com.bcy.utils.ServletUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.bcy.constant.BcyConstants.DEFAULT_SYS;
import static com.bcy.constants.BcyAuthConstants.USER_NAME;


/**
 * 系统日志服务
 *
 * <AUTHOR>
 * @since 2023/3/27-21:38
 */
@Service
@Slf4j
public class BcySysLogService {
    @Resource
    private ISysLogService sysLogService;

    /**
     * 分页获取系统日志
     *
     * @param pageRequest 分页表单
     * @return 系统日志列表
     */
    public PageUtils queryPage(PageRequest pageRequest) {
        List<SysLogPageResponse> sysLogPageResponses = new ArrayList<>();
        IPage<SysLogEntity> page = sysLogService.sysLogPage(pageRequest, null);
        List<SysLogEntity> sysLogEntities = page.getRecords();
        if (CollectionUtil.isNotEmpty(sysLogEntities)) {
            sysLogEntities.forEach(item -> {
                SysLogPageResponse sysLogPageResponse = new SysLogPageResponse();
                BeanUtils.copyProperties(item, sysLogPageResponse);
                sysLogPageResponses.add(sysLogPageResponse);
            });
        }
        return new PageUtils(sysLogPageResponses, page.getTotal(), pageRequest.getLimit(), pageRequest.getPage());
    }

    /**
     * 清除n天前的日志信息
     *
     * @param days 天数
     * @return true/false
     */
    public boolean deleteLogs(Integer days) {
        days = -1 * days;
        return sysLogService.deleteLogs(days) > 0;
    }

    /**
     * 保存日志
     *
     * @param sysLogForm 日志表单
     */
    public void saveSysLogs(SysLogForm sysLogForm) {
        SysLogEntity sysLogEntity = new SysLogEntity();
        BeanUtils.copyProperties(sysLogForm, sysLogEntity);
        sysLogEntity.setCreateDate(new Date());
        //设置IP地址
        if (StpUtil.isLogin() && Objects.nonNull(StpUtil.getSession()) && Objects.nonNull(StpUtil.getSession().get(USER_NAME))) {
            //用户名
            sysLogEntity.setUserName(StpUtil.getSession().get(USER_NAME).toString());
        } else {
            sysLogEntity.setUserName(DEFAULT_SYS);
        }
        sysLogEntity.setIp(IpUtils.getIpAddress(ServletUtils.getRequest()));
        sysLogEntity.setIpAddress(IpUtils.getAddressByIP(sysLogEntity.getIp()));
        sysLogService.save(sysLogEntity);
    }
}
