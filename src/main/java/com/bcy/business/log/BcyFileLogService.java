package com.bcy.business.log;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bcy.domain.response.page.log.SysFileLogPageResponse;
import com.bcy.admin.enums.DownLoadEnum;
import com.bcy.admin.domain.form.FileLogForm;
import com.bcy.domain.form.IdsForm;
import com.bcy.domain.request.PageRequest;
import com.bcy.service.log.ISysFileLogService;
import com.bcy.entity.log.SysFileLogEntity;
import com.bcy.exception.BcyServiceException;
import com.bcy.utils.BcyAssertUtils;
import com.bcy.utils.BcyUtils;
import com.bcy.utils.PageUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 系统文件日志服务
 *
 * <AUTHOR>
 * @since 2023/3/27-21:38
 */
@Service
@Slf4j
public class BcyFileLogService {

    @Resource
    private ISysFileLogService sysFileLogService;

    /**
     * 分页获取文件列表
     *
     * @param pageRequest 分页表单
     * @return 分页列表
     */
    public PageUtils queryPage(PageRequest pageRequest) {
        List<SysFileLogPageResponse> list = new ArrayList<>();
        IPage<SysFileLogEntity> iPage = sysFileLogService.fileLogPage(pageRequest, StpUtil.getLoginIdAsLong());
        List<SysFileLogEntity> sysFileLogEntityList = iPage.getRecords();
        if (CollectionUtil.isNotEmpty(sysFileLogEntityList)) {
            sysFileLogEntityList.forEach(item -> {
                SysFileLogPageResponse sysFileLogPageResponse = new SysFileLogPageResponse();
                BeanUtils.copyProperties(item, sysFileLogPageResponse);
                String fileType = FileUtil.getSuffix(item.getFileUrl());
                sysFileLogPageResponse.setFileType(fileType);
                // 只展示非图片的文件
                if (!ImageIO.getImageReadersBySuffix(fileType).hasNext()) {
                    sysFileLogPageResponse.setStatusName(DownLoadEnum.getDescByCode(item.getStatus()));
                } else {
                    sysFileLogPageResponse.setStatusName(StringUtils.EMPTY);
                }
                list.add(sysFileLogPageResponse);

            });
        }
        return new PageUtils(list, iPage.getTotal(), pageRequest.getLimit(), pageRequest.getPage());
    }

    /**
     * 批量删除文件日志
     *
     * @param idsForm id数组
     * @return 删除数量
     */
    public boolean deleteLogs(IdsForm idsForm) {
        List<SysFileLogEntity> sysFileLogEntityList = sysFileLogService.listByIds(idsForm.getIds());
        if (sysFileLogEntityList.stream().anyMatch(item -> item.getStatus().equals(DownLoadEnum.NO_DOWNED.getCode()))) {
            throw new BcyServiceException("批量删除失败，存在未下载的文件");
        }
        return sysFileLogService.removeBatchByIds(idsForm.getIds());
    }

    /**
     * 更新文件日志状态
     *
     * @param id 文件日志ID
     * @return true/false
     */
    public boolean updateStatus(Long id) {
        SysFileLogEntity sysFileLogEntity = sysFileLogService.getById(id);
        BcyAssertUtils.isNull(sysFileLogEntity, "该文件日志不存在！");
        if (!BcyUtils.isSuperAdmin(StpUtil.getLoginIdAsLong())) {
            sysFileLogEntity.setStatus(DownLoadEnum.DOWNED.getCode());
            return sysFileLogService.updateById(sysFileLogEntity);
        } else {
            return true;
        }
    }

    /**
     * 保存文件
     *
     * @param fileLogForm 文件日志表单
     */
    public void saveLog(FileLogForm fileLogForm) {
        SysFileLogEntity sysFileLogEntity = new SysFileLogEntity();
        BeanUtils.copyProperties(fileLogForm, sysFileLogEntity);
        sysFileLogEntity.setTime(fileLogForm.getTime());
        sysFileLogEntity.setExportTime(new Date());
        sysFileLogService.save(sysFileLogEntity);
    }
}
