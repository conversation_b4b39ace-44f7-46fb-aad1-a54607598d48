package com.bcy.business.points;

import cn.dev33.satoken.stp.StpUtil;
import com.bcy.entity.points.BcyUserInviteEntity;
import com.bcy.entity.points.BcyUserPointsEntity;
import com.bcy.service.points.UserInviteService;
import com.bcy.service.points.UserPointsService;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.time.Instant;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 业务层：用户邀请
 */
@Service
public class BcyUserInviteService {
    @Resource
    private UserInviteService userInviteService;
    @Resource
    private UserPointsService userPointsService;

    /**
     * 生成用户邀请链接，带回调地址
     *
     * @param baseUrl     基础邀请链接
     * @param callbackUrl 回调地址
     * @return 带userId、时间戳、callback的完整链接
     */
    public String generateInviteLink(String baseUrl, String callbackUrl) {
        Long userId = StpUtil.getLoginIdAsLong();
        long timestamp = Instant.now().toEpochMilli();
        return String.format("%s?userId=%d&ts=%d&callback=%s", baseUrl, userId, timestamp, java.net.URLEncoder.encode(callbackUrl, java.nio.charset.StandardCharsets.UTF_8));
    }

    /**
     * 生成base64二维码
     *
     * @param content 二维码内容
     * @return base64字符串
     */
    public String generateInviteQrBase64(String content) {
        try {
            int width = 300;
            int height = 300;
            Map<EncodeHintType, Object> hints = new HashMap<>();
            hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
            BitMatrix bitMatrix = new MultiFormatWriter().encode(content, BarcodeFormat.QR_CODE, width, height, hints);
            BufferedImage image = MatrixToImageWriter.toBufferedImage(bitMatrix);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(image, "png", baos);
            return "data:image/png;base64," + Base64.getEncoder().encodeToString(baos.toByteArray());
        } catch (Exception e) {
            throw new RuntimeException("二维码生成失败", e);
        }
    }

    /**
     * 用户点击邀请链接时的回调，生成邀请记录并给被邀请用户加积分
     *
     * @param inviterId 邀请人ID
     * @param inviteeId 被邀请人ID
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean handleInviteCallback(Long inviterId, Long inviteeId) {
        // 保存邀请记录
        BcyUserInviteEntity invite = new BcyUserInviteEntity();
        invite.setInviterId(inviterId);
        invite.setInviteeId(inviteeId);
        invite.setInviteTime(new Date());
        userInviteService.save(invite);
        // 判断被邀请人是否为新用户，是则加分
        BcyUserPointsEntity inviteePoints = userPointsService.lambdaQuery().eq(BcyUserPointsEntity::getUserId, inviteeId).one();
        if (inviteePoints == null) {
            // 新用户，邀请人加分
            BcyUserPointsEntity inviterPoints = userPointsService.lambdaQuery().eq(BcyUserPointsEntity::getUserId, inviterId).one();
            if (inviterPoints == null) {
                inviterPoints = new BcyUserPointsEntity();
                inviterPoints.setUserId(inviterId);
                inviterPoints.setTotalPoints(300);
                userPointsService.save(inviterPoints);
            } else {
                inviterPoints.setTotalPoints(inviterPoints.getTotalPoints() + 300);
                userPointsService.updateById(inviterPoints);
            }
            // 新用户加分
            inviteePoints = new BcyUserPointsEntity();
            inviteePoints.setUserId(inviteeId);
            inviteePoints.setTotalPoints(300);
            userPointsService.save(inviteePoints);
        }
        return true;
    }
} 