package com.bcy.business.points;

import com.bcy.domain.form.IdsForm;
import com.bcy.domain.response.points.PointsGoodsOrderResponse;
import com.bcy.entity.points.BcyPointsGoodsOrderEntity;
import com.bcy.exception.BcyServiceException;
import com.bcy.service.points.PointsGoodsOrderService;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 业务层：积分商品兑换记录
 */
@Service
public class BcyPointsGoodsOrderService {
    @Resource
    private PointsGoodsOrderService pointsGoodsOrderService;

    /**
     * 批量删除兑换记录
     */
    public boolean deleteByIds(IdsForm idsForm) {
        return pointsGoodsOrderService.removeByIds(idsForm.getIds());
    }

    /**
     * 根据ID查询兑换记录
     */
    public PointsGoodsOrderResponse getInfo(Long id) {
        BcyPointsGoodsOrderEntity entity = pointsGoodsOrderService.getById(id);
        if (Objects.isNull(entity)) {
            throw new BcyServiceException("该记录不存在");
        }
        PointsGoodsOrderResponse response = new PointsGoodsOrderResponse();
        BeanUtils.copyProperties(entity, response);
        return response;
    }
} 