package com.bcy.business.points;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bcy.domain.form.IdsForm;
import com.bcy.domain.form.points.PointsTaskForm;
import com.bcy.domain.request.PageRequest;
import com.bcy.domain.response.points.PointsTaskResponse;
import com.bcy.entity.points.BcyPointsTaskEntity;
import com.bcy.exception.BcyServiceException;
import com.bcy.service.points.PointsTaskService;
import com.bcy.utils.PageUtils;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 业务层：积分任务
 */
@Service
public class BcyPointsTaskService {
    @Resource
    private PointsTaskService pointsTaskService;

    /**
     * 分页查询
     *
     * @param pageRequest 分页表单
     * @return 分页列表
     */
    public PageUtils queryPage(PageRequest pageRequest) {
        IPage<BcyPointsTaskEntity> iPage = pointsTaskService.queryPage(pageRequest);
        List<PointsTaskResponse> responses = new ArrayList<>();
        List<BcyPointsTaskEntity> taskList = iPage.getRecords();
        if (CollectionUtils.isNotEmpty(taskList)) {
            taskList.forEach(item -> {
                PointsTaskResponse response = new PointsTaskResponse();
                BeanUtils.copyProperties(item, response);
                responses.add(response);
            });
        }
        return new PageUtils(responses, iPage.getTotal(), pageRequest.getLimit(), pageRequest.getPage());
    }

    /**
     * 新增任务
     *
     * @param form 任务表单
     * @return 是否新增成功
     */
    public boolean saveTask(PointsTaskForm form) {
        // 判断任务名称不能重复
        long count = pointsTaskService.countByName(form.getName());
        if (count > 0) {
            throw new BcyServiceException("任务名称已存在，不能重复添加");
        }
        BcyPointsTaskEntity entity = new BcyPointsTaskEntity();
        BeanUtils.copyProperties(form, entity);
        return pointsTaskService.save(entity);
    }

    /**
     * 批量删除任务
     *
     * @param idsForm id数组
     * @return true/false
     */
    public boolean deleteByIds(IdsForm idsForm) {
        return pointsTaskService.removeByIds(idsForm.getIds());
    }

    /**
     * 更新任务
     *
     * @param form 任务表单
     * @return 是否更新成功
     */
    public boolean updateTask(PointsTaskForm form) {
        BcyPointsTaskEntity entity = pointsTaskService.getById(form.getId());
        if (Objects.isNull(entity)) {
            throw new BcyServiceException("该任务不存在");
        }
        if (!entity.getName().equals(form.getName())) {
            // 判断任务名称不能重复
            long count = pointsTaskService.countByName(form.getName());
            if (count > 0) {
                throw new BcyServiceException("任务名称已存在，不能重复添加");
            }
        }
        BeanUtils.copyProperties(form, entity);
        return pointsTaskService.updateById(entity);
    }

    /**
     * 根据ID查询任务
     *
     * @param id 任务ID
     * @return 任务信息
     */
    public PointsTaskResponse getInfo(Long id) {
        BcyPointsTaskEntity entity = pointsTaskService.getById(id);
        if (Objects.isNull(entity)) {
            throw new BcyServiceException("该任务不存在");
        }
        PointsTaskResponse response = new PointsTaskResponse();
        BeanUtils.copyProperties(entity, response);
        return response;
    }
} 