package com.bcy.business.points;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bcy.domain.request.PageRequest;
import com.bcy.domain.response.points.UserPointsResponse;
import com.bcy.entity.points.BcyUserPointsEntity;
import com.bcy.entity.sys.SysUserEntity;
import com.bcy.service.points.UserPointsService;
import com.bcy.service.sys.IUserService;
import com.bcy.utils.PageUtils;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 业务层：用户积分
 */
@Service
public class BcyUserPointsService {
    @Resource
    private UserPointsService userPointsService;
    @Resource
    private IUserService userService;

    /**
     * 用户积分分页列表
     *
     * @param pageRequest 分页表单
     * @return 分页列表
     */
    public PageUtils queryPage(PageRequest pageRequest) {
        IPage<BcyUserPointsEntity> iPage = userPointsService.queryPage(pageRequest);
        List<UserPointsResponse> responses = new ArrayList<>();
        List<BcyUserPointsEntity> pointsList = iPage.getRecords();
        if (CollectionUtils.isNotEmpty(pointsList)) {
            pointsList.forEach(item -> {
                UserPointsResponse response = new UserPointsResponse();
                BeanUtils.copyProperties(item, response);
                SysUserEntity user = userService.getById(item.getUserId());
                if (Objects.nonNull(user)) {
                    response.setUserName(user.getNickName());
                }
                responses.add(response);
            });
        }
        return new PageUtils(responses, iPage.getTotal(), pageRequest.getLimit(), pageRequest.getPage());
    }

}