package com.bcy.business.pet;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bcy.domain.form.IdsForm;
import com.bcy.domain.form.pet.BcyPetContentForm;
import com.bcy.domain.request.PageRequest;
import com.bcy.domain.response.page.pet.BcyPetContentPageResponse;
import com.bcy.domain.response.pet.BcyPetContentResponse;
import com.bcy.entity.pet.BcyPetCategoryEntity;
import com.bcy.entity.pet.BcyPetContentDetailEntity;
import com.bcy.entity.pet.BcyPetContentEntity;
import com.bcy.enums.StatusEnum;
import com.bcy.exception.BcyServiceException;
import com.bcy.service.pet.IPetCategoryService;
import com.bcy.service.pet.IPetContentDetailService;
import com.bcy.service.pet.IPetContentService;
import com.bcy.utils.BcyAssertUtils;
import com.bcy.utils.PageUtils;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 宠物科普服务
 *
 * <AUTHOR>
 * @since 2024/10/17-21:38
 */
@Service
public class BcyPetContentService {
    @Resource
    private IPetCategoryService iPetCategoryService;
    @Resource
    private IPetContentService iPetContentService;
    @Resource
    private IPetContentDetailService iPetContentDetailService;

    /**
     * 分页获取宠物科普列表
     *
     * @param pageRequest 分页列表
     * @return 分页列表
     */
    public PageUtils queryPage(PageRequest pageRequest) {
        List<BcyPetContentPageResponse> responses = new ArrayList<>();
        IPage<BcyPetContentEntity> selectPage = iPetContentService.pageList(pageRequest);
        List<BcyPetContentEntity> pageList = selectPage.getRecords();
        if (CollectionUtil.isNotEmpty(pageList)) {
            pageList.forEach(item -> {
                BcyPetContentPageResponse bcyPetContentPageResponse = new BcyPetContentPageResponse();
                BeanUtils.copyProperties(item, bcyPetContentPageResponse);
                BcyPetCategoryEntity bcyPetCategoryEntity = iPetCategoryService.getById(item.getCategoryId());
                if (Objects.nonNull(bcyPetCategoryEntity)) {
                    bcyPetContentPageResponse.setCategoryName(bcyPetCategoryEntity.getName());
                }
                responses.add(bcyPetContentPageResponse);
            });
        }
        return new PageUtils(responses, selectPage.getTotal(), pageRequest.getLimit(), pageRequest.getPage());
    }

    /**
     * 根据Id获取文章详情
     *
     * @param id 主键
     * @return 文章详情
     */
    public BcyPetContentResponse getInfoById(Long id) {
        BcyPetContentEntity bcyPetContentEntity = iPetContentService.getById(id);
        BcyAssertUtils.isNull(bcyPetContentEntity, "文章内容为空！");
        BcyPetContentResponse bcyPetContentResponse = new BcyPetContentResponse();
        BeanUtils.copyProperties(bcyPetContentEntity, bcyPetContentResponse);
        BcyPetContentDetailEntity bcyPetContentDetailEntity = iPetContentDetailService.getByContentId(id);
        BcyAssertUtils.isNull(bcyPetContentDetailEntity, "文章内容详情为空！");
        bcyPetContentResponse.setDetails(bcyPetContentDetailEntity.getDetails());
        return bcyPetContentResponse;
    }

    /**
     * 保存科普文章
     *
     * @param bcyPetContentForm 文章表单
     * @return true/false
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean save(BcyPetContentForm bcyPetContentForm) {
        // 判断标题是否一致
        long count = iPetContentService.countByTitle(bcyPetContentForm.getTitle());
        if (count > 0) {
            throw new BcyServiceException("文章标题已存在！");
        }
        BcyPetContentEntity bcyPetContentEntity = new BcyPetContentEntity();
        BeanUtils.copyProperties(bcyPetContentForm, bcyPetContentEntity);
        if (StatusEnum.ENABLE.getCode().equals(bcyPetContentEntity.getStatus())) {
            bcyPetContentEntity.setPushTime(new Date());
        }
        iPetContentService.save(bcyPetContentEntity);
        BcyPetContentDetailEntity bcyPetContentDetailEntity = new BcyPetContentDetailEntity();
        bcyPetContentDetailEntity.setContentId(bcyPetContentEntity.getId());
        bcyPetContentDetailEntity.setDetails(bcyPetContentForm.getDetails());
        return iPetContentDetailService.save(bcyPetContentDetailEntity);
    }

    /**
     * 更新科普文章
     *
     * @param bcyPetContentForm 文章表单
     * @return true/false
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean update(BcyPetContentForm bcyPetContentForm) {
        BcyPetContentEntity bcyPetContentEntity = iPetContentService.getById(bcyPetContentForm.getId());
        BcyAssertUtils.isNull(bcyPetContentEntity, "文章内容为空！");
        if (!bcyPetContentForm.getTitle().equals(bcyPetContentEntity.getTitle())) {
            // 判断标题是否一致
            long count = iPetContentService.countByTitle(bcyPetContentForm.getTitle());
            if (count > 0) {
                throw new BcyServiceException("文章标题已存在！");
            }
        }
        BeanUtils.copyProperties(bcyPetContentForm, bcyPetContentEntity);
        if (StatusEnum.ENABLE.getCode().equals(bcyPetContentEntity.getStatus())) {
            bcyPetContentEntity.setPushTime(new Date());
        }
        iPetContentService.updateById(bcyPetContentEntity);
        BcyPetContentDetailEntity bcyPetContentDetailEntity = iPetContentDetailService.getByContentId(bcyPetContentEntity.getId());
        if (Objects.nonNull(bcyPetContentDetailEntity)) {
            bcyPetContentDetailEntity.setDetails(bcyPetContentForm.getDetails());
            return iPetContentDetailService.updateById(bcyPetContentDetailEntity);
        } else {
            bcyPetContentDetailEntity = new BcyPetContentDetailEntity();
            bcyPetContentDetailEntity.setContentId(bcyPetContentEntity.getId());
            bcyPetContentDetailEntity.setDetails(bcyPetContentForm.getDetails());
            return iPetContentDetailService.save(bcyPetContentDetailEntity);
        }
    }

    /**
     * 删除科普文章
     *
     * @param idsForm 文章id列表
     * @return true/false
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(IdsForm idsForm) {
        iPetContentDetailService.removeByContentIds(idsForm.getIds());
        return iPetContentService.removeBatchByIds(idsForm.getIds());
    }

    /**
     * 更新文章状态
     *
     * @param id 文章id
     * @return true/false
     */
    public boolean updateStatus(Long id) {
        BcyPetContentEntity bcyPetContentEntity = iPetContentService.getById(id);
        BcyAssertUtils.isNull(bcyPetContentEntity, "文章内容为空！");
        bcyPetContentEntity.setStatus(bcyPetContentEntity.getStatus().equals(StatusEnum.ENABLE.getCode()) ? StatusEnum.DISABLE.getCode() : StatusEnum.ENABLE.getCode());
        if (StatusEnum.ENABLE.getCode().equals(bcyPetContentEntity.getStatus())) {
            bcyPetContentEntity.setPushTime(new Date());
        }
        return iPetContentService.updateById(bcyPetContentEntity);
    }
}
