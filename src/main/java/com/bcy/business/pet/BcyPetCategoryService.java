package com.bcy.business.pet;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bcy.domain.form.pet.BcyPetCategoryForm;
import com.bcy.domain.request.PageRequest;
import com.bcy.domain.response.page.pet.BcyPetCategoryPageResponse;
import com.bcy.domain.response.pet.BcyPetCategoryDetailResponse;
import com.bcy.domain.response.pet.BcyPetCategoryResponse;
import com.bcy.entity.pet.BcyPetCategoryDetailEntity;
import com.bcy.entity.pet.BcyPetCategoryEntity;
import com.bcy.exception.BcyServiceException;
import com.bcy.service.pet.IPetCategoryDetailService;
import com.bcy.service.pet.IPetCategoryService;
import com.bcy.utils.BcyAssertUtils;
import com.bcy.utils.PageUtils;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 宠物类别服务
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Service
public class BcyPetCategoryService {
    @Resource
    private IPetCategoryService iPetCategoryService;
    @Resource
    private IPetCategoryDetailService iPetCategoryDetailService;

    /**
     * 分页获取宠物类别列表
     *
     * @param pageRequest 分页表单
     * @return 分页列表
     */
    public PageUtils queryPage(PageRequest pageRequest) {
        List<BcyPetCategoryPageResponse> responses = new ArrayList<>();
        IPage<BcyPetCategoryEntity> selectPage = iPetCategoryService.pageList(pageRequest);
        List<BcyPetCategoryEntity> pageList = selectPage.getRecords();
        if (CollectionUtil.isNotEmpty(pageList)) {
            pageList.forEach(item -> {
                BcyPetCategoryPageResponse response = new BcyPetCategoryPageResponse();
                BeanUtils.copyProperties(item, response);
                responses.add(response);
            });
        }
        return new PageUtils(responses, selectPage.getTotal(), pageRequest.getLimit(), pageRequest.getPage());
    }

    /**
     * 删除宠物类别详情
     *
     * @param id 类别ID
     * @return true/false
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deletePetCategory(Long id) {
        BcyPetCategoryEntity bcyPetCategoryEntity = iPetCategoryService.getById(id);
        BcyAssertUtils.isNull(bcyPetCategoryEntity, "该类别不存在！");
        List<BcyPetCategoryEntity> chirdList = iPetCategoryService.getListByParentId(id);
        if (CollectionUtil.isNotEmpty(chirdList)) {
            throw new BcyServiceException("存在子类别，无法删除！");
        }
        iPetCategoryDetailService.deleteByCategoryId(id);
        return iPetCategoryService.removeById(id);
    }

    /**
     * 宠物类别详情
     *
     * @param id 类别ID
     * @return 详情
     */
    public BcyPetCategoryResponse getInfoById(Long id) {
        BcyPetCategoryEntity bcyPetCategoryEntity = iPetCategoryService.getById(id);
        BcyAssertUtils.isNull(bcyPetCategoryEntity, "该类别不存在！");
        BcyPetCategoryResponse bcyPetCategoryResponse = new BcyPetCategoryResponse();
        BeanUtils.copyProperties(bcyPetCategoryEntity, bcyPetCategoryResponse);
        return bcyPetCategoryResponse;
    }

    /**
     * 新增宠物类别
     *
     * @param petCategoryForm 类别表单
     * @return true/false
     */
    public boolean savePetCategory(BcyPetCategoryForm petCategoryForm) {
        long countByName = iPetCategoryService.countByName(petCategoryForm.getName());
        if (countByName > 0) {
            throw new BcyServiceException("该类别名称已存在！");
        }
        BcyPetCategoryEntity bcyPetCategoryEntity = new BcyPetCategoryEntity();
        BeanUtils.copyProperties(petCategoryForm, bcyPetCategoryEntity);
        return iPetCategoryService.save(bcyPetCategoryEntity);
    }

    /**
     * 更新宠物类别
     *
     * @param petCategoryForm 类别表单
     * @return true/false
     */
    public boolean updatePetCategory(BcyPetCategoryForm petCategoryForm) {
        BcyPetCategoryEntity bcyPetCategoryEntity = iPetCategoryService.getById(petCategoryForm.getId());
        BcyAssertUtils.isNull(bcyPetCategoryEntity, "该类别不存在！");
        if (!bcyPetCategoryEntity.getName().equals(petCategoryForm.getName())) {
            long countByName = iPetCategoryService.countByName(petCategoryForm.getName());
            if (countByName > 0) {
                throw new BcyServiceException("该类别名称已存在！");
            }
        }
        BeanUtils.copyProperties(petCategoryForm, bcyPetCategoryEntity);
        return iPetCategoryService.updateById(bcyPetCategoryEntity);
    }

    /**
     * 获取分类ID获取列表（0表示父级列表）
     *
     * @param id 主键，默认0
     * @return 列表
     */
    public List<BcyPetCategoryResponse> allList(Long id) {
        List<BcyPetCategoryResponse> responses = new ArrayList<>();
        List<BcyPetCategoryEntity> list = iPetCategoryService.getListByParentId(id);
        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(item -> {
                BcyPetCategoryResponse bcyPetCategoryResponse = new BcyPetCategoryResponse();
                BeanUtils.copyProperties(item, bcyPetCategoryResponse);
                responses.add(bcyPetCategoryResponse);
            });
        }
        return responses;
    }

    /**
     * 宠物类别详情
     *
     * @param id 类别ID
     * @return 详情
     */
    public BcyPetCategoryResponse getCategoryInfo(Long id) {
        BcyPetCategoryResponse bcyPetCategoryResponse = getInfoById(id);
        List<BcyPetCategoryDetailEntity> detailEntities = iPetCategoryDetailService.getByCategoryId(id);
        if (CollectionUtil.isNotEmpty(detailEntities)) {
            List<BcyPetCategoryDetailResponse> list = new ArrayList<>();
            detailEntities.forEach(item -> {
                BcyPetCategoryDetailResponse bcyPetCategoryDetailResponse = new BcyPetCategoryDetailResponse();
                BeanUtils.copyProperties(item, bcyPetCategoryDetailResponse);
                list.add(bcyPetCategoryDetailResponse);
            });
            bcyPetCategoryResponse.setDetailResponseList(list);
        }
        return bcyPetCategoryResponse;
    }
}
