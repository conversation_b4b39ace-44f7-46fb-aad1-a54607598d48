package com.bcy.business.pet;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bcy.domain.form.pet.BcyPetInfoForm;
import com.bcy.domain.request.PageRequest;
import com.bcy.domain.response.page.pet.BcyPetInfoPageResponse;
import com.bcy.domain.response.pet.BcyPetInfoResponse;
import com.bcy.entity.pet.BcyPetInfoEntity;
import com.bcy.enums.YesOrNoEnum;
import com.bcy.service.pet.IPetInfoService;
import com.bcy.utils.BcyAssertUtils;
import com.bcy.utils.PageUtils;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 用户宠物服务
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
@Service
public class BcyPetInfoService {
    @Resource
    private IPetInfoService iPetInfoService;

    /**
     * 用户宠物信息分页列表
     *
     * @param pageRequest 分页列表
     * @return 分页列表
     */
    public PageUtils queryPage(PageRequest pageRequest) {
        List<BcyPetInfoPageResponse> responses = new ArrayList<>();
        IPage<BcyPetInfoEntity> selectPage = iPetInfoService.pageList(pageRequest);
        List<BcyPetInfoEntity> pageList = selectPage.getRecords();
        if (CollectionUtil.isNotEmpty(pageList)) {
            pageList.forEach(item -> {
                BcyPetInfoPageResponse bcyPetInfoPageResponse = new BcyPetInfoPageResponse();
                BeanUtils.copyProperties(item, bcyPetInfoPageResponse);
                bcyPetInfoPageResponse.setUserName(item.getCreateUser());
                responses.add(bcyPetInfoPageResponse);
            });
        }
        return new PageUtils(responses, selectPage.getTotal(), pageRequest.getLimit(), pageRequest.getPage());
    }

    /**
     * 根据ID获取详情
     *
     * @param id id
     * @return 详情
     */
    public BcyPetInfoResponse getPetInfoById(Long id) {
        BcyPetInfoEntity bcyPetInfoEntity = iPetInfoService.getById(id);
        BcyAssertUtils.isNull(bcyPetInfoEntity, "该宠物信息不存在！");
        BcyPetInfoResponse bcyPetInfoResponse = new BcyPetInfoResponse();
        BeanUtils.copyProperties(bcyPetInfoEntity, bcyPetInfoResponse);
        bcyPetInfoResponse.setUserName(bcyPetInfoEntity.getCreateUser());
        return bcyPetInfoResponse;
    }


    /**
     * 获取当前用户的宠物列表
     *
     * @return 详情
     */
    public List<BcyPetInfoResponse> userPetList() {
        List<BcyPetInfoResponse> list = new ArrayList<>();
        List<BcyPetInfoEntity> bcyPetInfoEntities = iPetInfoService.getByUserId(StpUtil.getLoginIdAsLong());
        if (CollectionUtil.isNotEmpty(bcyPetInfoEntities)) {
            bcyPetInfoEntities.forEach(item -> {
                BcyPetInfoResponse response = new BcyPetInfoResponse();
                BeanUtils.copyProperties(item, response);
                list.add(response);
            });
        }
        return list;
    }

    /**
     * 新增用户宠物信息
     *
     * @param bcyPetInfoForm 宠物信息表单
     * @return true/false
     */
    public boolean savePetInfo(BcyPetInfoForm bcyPetInfoForm) {
        BcyPetInfoEntity bcyPetInfoEntity = new BcyPetInfoEntity();
        BeanUtils.copyProperties(bcyPetInfoForm, bcyPetInfoEntity);
        bcyPetInfoEntity.setUserId(StpUtil.getLoginIdAsLong());
        return iPetInfoService.save(bcyPetInfoEntity);
    }

    /**
     * 更新用户宠物信息
     *
     * @param bcyPetInfoForm 宠物信息表单
     * @return true/false
     */
    public boolean updatePetInfo(BcyPetInfoForm bcyPetInfoForm) {
        BcyPetInfoEntity bcyPetInfoEntity = iPetInfoService.getById(bcyPetInfoForm.getId());
        BcyAssertUtils.isNull(bcyPetInfoEntity, "该宠物信息不存在！");
        BeanUtils.copyProperties(bcyPetInfoForm, bcyPetInfoEntity);
        return iPetInfoService.updateById(bcyPetInfoEntity);
    }

    /**
     * 删除宠物信息
     *
     * @param id 主键
     * @return true/false
     */
    public boolean deletePetInfo(Long id) {
        return iPetInfoService.removeById(id);
    }

    /**
     * 设置默认宠物
     *
     * @param id 主键
     * @return true/false
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean defaultPet(Long id) {
        BcyPetInfoEntity bcyPetInfoEntity = iPetInfoService.getById(id);
        BcyAssertUtils.isNull(bcyPetInfoEntity, "该宠物信息不存在！");
        bcyPetInfoEntity.setIsDefault(YesOrNoEnum.YES.getCode());
        // 其他的设置非默认
        List<BcyPetInfoEntity> bcyPetInfoEntities = iPetInfoService.getByUserId(StpUtil.getLoginIdAsLong());
        if (CollectionUtil.isNotEmpty(bcyPetInfoEntities)) {
            List<BcyPetInfoEntity> list = bcyPetInfoEntities.stream()
                    .filter(item -> Objects.equals(item.getIsDefault(), YesOrNoEnum.YES.getCode()) && !item.getId().equals(id))
                    .peek(item -> item.setIsDefault(YesOrNoEnum.NO.getCode())).toList();
            if (CollectionUtil.isNotEmpty(list)) {
                iPetInfoService.updateBatchById(list);
            }
        }
        return iPetInfoService.updateById(bcyPetInfoEntity);
    }

}
