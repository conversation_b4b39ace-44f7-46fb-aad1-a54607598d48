package com.bcy.business.pet;


import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bcy.domain.form.IdsForm;
import com.bcy.domain.form.pet.BcyPetCategoryDetailForm;
import com.bcy.domain.request.PageRequest;
import com.bcy.domain.response.page.pet.BcyPetCategoryPageDetailResponse;
import com.bcy.domain.response.pet.BcyPetCategoryDetailResponse;
import com.bcy.entity.pet.BcyPetCategoryDetailEntity;
import com.bcy.exception.BcyServiceException;
import com.bcy.service.pet.IPetCategoryDetailService;
import com.bcy.utils.BcyAssertUtils;
import com.bcy.utils.PageUtils;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 类型介绍服务
 *
 * <AUTHOR>
 * @since 2024-10-28 14:54:59
 */
@Service
public class BcyPetCategoryDetailService {
    @Resource
    private IPetCategoryDetailService iPetCategoryDetailService;

    /**
     * 根据分类ID获取详情列表
     *
     * @param pageRequest 分页请求
     * @return 分页列表
     */
    public PageUtils queryPage(PageRequest pageRequest) {
        BcyAssertUtils.isNull(pageRequest.getLinkId(), "分类Id不能为空");
        List<BcyPetCategoryPageDetailResponse> responses = new ArrayList<>();
        IPage<BcyPetCategoryDetailEntity> selectPage = iPetCategoryDetailService.pageList(pageRequest);
        List<BcyPetCategoryDetailEntity> pageList = selectPage.getRecords();
        if (CollectionUtil.isNotEmpty(pageList)) {
            pageList.forEach(item -> {
                BcyPetCategoryPageDetailResponse bcyPetCategoryDetailResponse = new BcyPetCategoryPageDetailResponse();
                BeanUtils.copyProperties(item, bcyPetCategoryDetailResponse);
                responses.add(bcyPetCategoryDetailResponse);
            });
        }
        return new PageUtils(responses, selectPage.getTotal(), pageRequest.getLimit(), pageRequest.getPage());
    }


    /**
     * 根据ID获取详情信息
     *
     * @param id id
     * @return 详情信息
     */
    public BcyPetCategoryDetailResponse getInfoById(Long id) {
        BcyPetCategoryDetailEntity bcyPetCategoryDetailEntity = iPetCategoryDetailService.getById(id);
        BcyAssertUtils.isNull(bcyPetCategoryDetailEntity, "该详情不存在！");
        BcyPetCategoryDetailResponse bcyPetCategoryDetailResponse = new BcyPetCategoryDetailResponse();
        BeanUtils.copyProperties(bcyPetCategoryDetailEntity, bcyPetCategoryDetailResponse);
        return bcyPetCategoryDetailResponse;
    }

    /**
     * 保存分类详情
     *
     * @param bcyPetCategoryDetailForm 详情表单
     * @return true/false
     */
    public boolean save(BcyPetCategoryDetailForm bcyPetCategoryDetailForm) {
        long count = iPetCategoryDetailService.countByCategoryIdAndTags(bcyPetCategoryDetailForm.getCategoryId(), bcyPetCategoryDetailForm.getTags());
        if (count > 0) {
            throw new BcyServiceException("同一个分类标签已存在详情！");
        }
        BcyPetCategoryDetailEntity bcyPetCategoryDetailEntity = new BcyPetCategoryDetailEntity();
        BeanUtils.copyProperties(bcyPetCategoryDetailForm, bcyPetCategoryDetailEntity);
        return iPetCategoryDetailService.save(bcyPetCategoryDetailEntity);
    }

    /**
     * 更新分类详情
     *
     * @param bcyPetCategoryDetailForm 详情表单
     * @return true/false
     */
    public boolean update(BcyPetCategoryDetailForm bcyPetCategoryDetailForm) {
        BcyPetCategoryDetailEntity bcyPetCategoryDetailEntity = iPetCategoryDetailService.getById(bcyPetCategoryDetailForm.getId());
        BcyAssertUtils.isNull(bcyPetCategoryDetailEntity, "该详情不存在！");
        if (!bcyPetCategoryDetailForm.getTags().equals(bcyPetCategoryDetailEntity.getTags())) {
            long count = iPetCategoryDetailService.countByCategoryIdAndTags(bcyPetCategoryDetailForm.getCategoryId(), bcyPetCategoryDetailForm.getTags());
            if (count > 0) {
                throw new BcyServiceException("同一个分类标签已存在详情！");
            }
        }
        BeanUtils.copyProperties(bcyPetCategoryDetailForm, bcyPetCategoryDetailEntity);
        return iPetCategoryDetailService.updateById(bcyPetCategoryDetailEntity);
    }


    /**
     * 删除详情
     *
     * @param idsForm 详情ID列表
     * @return true/false
     */
    public boolean delete(IdsForm idsForm) {
        return iPetCategoryDetailService.removeBatchByIds(idsForm.getIds());
    }


}




