package com.bcy.business.msg;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bcy.business.sys.BcySysDictionItemService;
import com.bcy.domain.form.IdsForm;
import com.bcy.domain.form.msg.SysMessageForm;
import com.bcy.domain.response.msg.SysMessageResponse;
import com.bcy.domain.response.page.msg.SysMessagePageResponse;
import com.bcy.admin.enums.NoticeTypeEnum;
import com.bcy.enums.PushStatusEnum;
import com.bcy.admin.enums.ReadStatusEnum;
import com.bcy.exception.BcyServiceException;
import com.bcy.service.msg.IMessageService;
import com.bcy.service.msg.IUserMessageService;
import com.bcy.service.sys.IUserTenantService;
import com.bcy.domain.request.MessagePageRequest;
import com.bcy.entity.msg.SysMessageEntity;
import com.bcy.entity.msg.SysUserMessageEntity;
import com.bcy.entity.sys.SysDictionItemEntity;
import com.bcy.entity.sys.SysUserTenantEntity;
import com.bcy.utils.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

import static com.bcy.admin.constants.BcyAdminConstants.SYS_MSG_TYPE;


/**
 * 消息相关服务
 *
 * <AUTHOR>
 * @since 2024/01/02-21:38
 */
@Service
@Slf4j
public class BcySysMessageService {
    @Resource
    private IMessageService iMessageService;
    @Resource
    private IUserMessageService iUserMessageService;
    @Resource
    private IUserTenantService iUserTenantService;
    @Resource
    private BcySysDictionItemService bcySysDictionItemService;


    /**
     * 系统消息分页列表
     *
     * @param pageRequest 分页表单
     * @return 分页列表
     */
    public PageUtils queryPage(MessagePageRequest pageRequest) {
        Long userId = StpUtil.getLoginIdAsLong();
        if (BcyUtils.isSuperAdmin(userId) && pageRequest.getIsManage() == 1) {
            // 如果是超级管理员，待办页面为空
            return new PageUtils(new ArrayList<>(), 0, pageRequest.getLimit(), pageRequest.getPage());
        }
        List<Long> messageIdList = new ArrayList<>();
        List<SysUserMessageEntity> sysUserMessageEntityList = iUserMessageService.userMessageListByUserId(userId, pageRequest.getStatus());
        messageIdList.add(BigDecimal.ZERO.longValue());
        // 获取消息列表(待办)
        List<Long> msgIdList = sysUserMessageEntityList.stream().map(SysUserMessageEntity::getMessageId).toList();
        if (CollectionUtil.isNotEmpty(msgIdList)) {
            messageIdList.addAll(msgIdList);
        }
        IPage<SysMessageEntity> sysMessageEntities = iMessageService.messagePage(pageRequest, messageIdList);
        List<SysMessagePageResponse> responses = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(sysMessageEntities.getRecords())) {
            sysMessageEntities.getRecords().forEach(item -> {
                SysMessagePageResponse sysMessagePageResponse = new SysMessagePageResponse();
                BeanUtils.copyProperties(item, sysMessagePageResponse);

                if (CollectionUtil.isNotEmpty(sysUserMessageEntityList)) {
                    Optional<SysUserMessageEntity> result = sysUserMessageEntityList.stream().filter(entity ->
                            item.getId().equals(entity.getMessageId()) && userId.equals(entity.getUserId())).findFirst();
                    if (result.isPresent()) {
                        SysUserMessageEntity sysUserMessageEntity = result.get();
                        sysMessagePageResponse.setIsRead(sysUserMessageEntity.getIsRead());
                    }
                }
                responses.add(sysMessagePageResponse);
            });
        }
        return new PageUtils(responses, sysMessageEntities.getTotal(), pageRequest.getLimit(), pageRequest.getPage());
    }

    /**
     * 根据Id获取消息详情
     *
     * @param id   消息Id
     * @param type 页面类型
     * @return 消息详情
     */
    public SysMessageResponse getInfoById(Long id, Integer type) {
        Long userId = StpUtil.getLoginIdAsLong();
        SysMessageEntity sysMessageEntity = iMessageService.getById(id);
        BcyAssertUtils.isNull(sysMessageEntity, String.format("获取消息详情失败，%s不存在！", id));
        SysMessageResponse sysMessageResponse = new SysMessageResponse();
        BeanUtils.copyProperties(sysMessageEntity, sysMessageResponse);
        if (BigDecimal.ONE.intValue() == type) {
            // 个人消息页面需要更新阅读状态
            List<SysUserMessageEntity> userMessageEntities = iUserMessageService.userMessageListByUserIdAndMessageId(userId, id);
            if (CollectionUtil.isNotEmpty(userMessageEntities)) {
                // 消息更新为已读
                userMessageEntities.forEach(entity -> {
                    entity.setIsRead(ReadStatusEnum.HAS_READ.getCode());
                    entity.setReadTime(new Date());
                });
                iUserMessageService.updateBatchById(userMessageEntities);
            }
        }
        return sysMessageResponse;
    }

    /**
     * 保存消息信息
     *
     * @param messageForm 消息表单
     * @return boolean
     */
    public boolean saveMessage(SysMessageForm messageForm) {
        SysMessageEntity sysMessageEntity = new SysMessageEntity();
        BeanUtils.copyProperties(messageForm, sysMessageEntity);
        return iMessageService.save(sysMessageEntity);
    }

    /**
     * 更新消息信息
     *
     * @param messageForm 消息表单
     * @return boolean
     */
    public boolean updateMessage(SysMessageForm messageForm) {
        SysMessageEntity sysMessageEntity = iMessageService.getById(messageForm.getId());
        BcyAssertUtils.isNull(sysMessageEntity, String.format("获取消息详情失败，%s不存在！", messageForm.getId()));
        if (sysMessageEntity.getIsPush().equals(PushStatusEnum.HAS_PUSH.getCode())) {
            throw new BcyServiceException("该消息已发送，无法更改！");
        }
        BeanUtils.copyProperties(messageForm, sysMessageEntity);
        return iMessageService.updateById(sysMessageEntity);
    }

    /**
     * 批量删除消息
     *
     * @param idsForm 消息id数组
     * @return 删除数量
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteMessages(IdsForm idsForm) {
        iUserMessageService.removeByMsgIds(idsForm.getIds());
        return iMessageService.removeBatchByIds(idsForm.getIds());
    }

    /**
     * 批量发送消息
     *
     * @param idsForm 消息id数组
     */
    @Transactional(rollbackFor = Exception.class)
    public void pushMessages(IdsForm idsForm) {
        List<SysMessageEntity> sysMessageEntities = iMessageService.listByIds(idsForm.getIds());
        // 消息更新为已读
        sysMessageEntities.forEach(entity -> {
            entity.setIsPush(PushStatusEnum.HAS_PUSH.getCode());
            entity.setPushTime(new Date());
        });
        iMessageService.updateBatchById(sysMessageEntities);
        // 添加关联关系
        List<SysUserMessageEntity> userMessageEntities = new ArrayList<>();
        List<SysUserTenantEntity> userTenantEntities = iUserTenantService.userListByTenantId(BcyInfoUtils.getUserTenant());
        if (CollectionUtil.isNotEmpty(userTenantEntities)) {
            List<Long> userIdList = userTenantEntities.stream().map(SysUserTenantEntity::getUserId).toList();
            sysMessageEntities.forEach(message -> {
                userIdList.forEach(item -> {
                    SysUserMessageEntity sysUserMessageEntity = new SysUserMessageEntity();
                    sysUserMessageEntity.setMessageId(message.getId());
                    if (!BcyUtils.isSuperAdmin(item)) {
                        sysUserMessageEntity.setUserId(item);
                        userMessageEntities.add(sysUserMessageEntity);
                    }
                });
                iUserMessageService.saveBatch(userMessageEntities);
                // 判断消息通知
                try {
                    sendNotice(message);
                } catch (Exception e) {
                    log.error("发送消息失败！{}", e.getMessage());
                }

            });
        }
    }

    /**
     * 发送消息通知
     *
     * @param message 消息信息
     */
    private void sendNotice(SysMessageEntity message) {
        if (NoticeTypeEnum.STATION.getCode().equals(message.getNoticeType())) {
            // websocket通知
            WebSocketUtils.sendInfo(message.getTitle());
        } else if (NoticeTypeEnum.WECHAT.getCode().equals(message.getNoticeType())) {
            // 微信通知
        } else if (NoticeTypeEnum.SMS.getCode().equals(message.getNoticeType())) {
            // 短信通知
        } else if (NoticeTypeEnum.APP.getCode().equals(message.getNoticeType())) {
            // APP通知栏
        }
    }

    /**
     * 一键已读所有未读消息
     *
     * @return boolean
     */
    public boolean readAll() {
        Long userId = StpUtil.getLoginIdAsLong();
        List<SysUserMessageEntity> userMessageEntities = iUserMessageService.userMessageListByUserId(userId, ReadStatusEnum.NO_READ.getCode());
        if (CollectionUtil.isNotEmpty(userMessageEntities)) {
            userMessageEntities.forEach(item -> {
                item.setIsRead(ReadStatusEnum.HAS_READ.getCode());
                item.setReadTime(new Date());
            });
            iUserMessageService.updateBatchById(userMessageEntities);
        }
        return true;
    }

    /**
     * 一键清除所有已读消息
     *
     * @return boolean
     */
    public boolean clearReadMsg() {
        return iUserMessageService.removeByUserId(StpUtil.getLoginIdAsLong(), ReadStatusEnum.HAS_READ.getCode());
    }

    /**
     * 批量删除用户消息
     *
     * @param idsForm 消息ID数组
     * @return boolean
     */
    public boolean deleteUserMessages(IdsForm idsForm) {
        return iUserMessageService.deleteMsgLink(idsForm.getIds(), StpUtil.getLoginIdAsLong());
    }

    /**
     * 获取用户未读消息列表
     *
     * @return 未读消息列表
     */
    public List<SysMessageResponse> unReadList() {
        List<SysMessageResponse> list = new ArrayList<>();
        List<SysUserMessageEntity> userMessageEntities = iUserMessageService.userMessageListByUserId(StpUtil.getLoginIdAsLong(), ReadStatusEnum.NO_READ.getCode());
        if (CollectionUtil.isNotEmpty(userMessageEntities)) {
            List<Long> messageIdList = userMessageEntities.stream().map(SysUserMessageEntity::getMessageId).toList();
            List<SysMessageEntity> messageEntities = iMessageService.listByIds(messageIdList);
            if (CollectionUtil.isNotEmpty(messageEntities)) {
                messageEntities.forEach(item -> {
                    SysMessageResponse sysMessageResponse = new SysMessageResponse();
                    BeanUtils.copyProperties(item, sysMessageResponse);
                    SysDictionItemEntity typeInfo = bcySysDictionItemService.getInfByParam(SYS_MSG_TYPE, item.getType());
                    if (Objects.nonNull(typeInfo)) {
                        sysMessageResponse.setType(typeInfo.getName());
                    }
                    list.add(sysMessageResponse);
                });
            }
        }
        return list;
    }

}
