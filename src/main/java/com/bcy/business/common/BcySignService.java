package com.bcy.business.common;

import cn.dev33.satoken.stp.StpUtil;
import com.bcy.domain.form.common.SignDateForm;
import com.bcy.exception.BcyServiceException;
import com.bcy.service.SignService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * 签到相关服务
 *
 * <AUTHOR>
 * @since 2024/01/02-21:38
 */
@Service
@Slf4j
public class BcySignService {
    @Resource
    private SignService signService;

    /**
     * 用户签到或者补签
     *
     * @param signDateForm 签到日期表单
     */
    public void userSign(SignDateForm signDateForm) {
        if (Objects.isNull(signDateForm.getDate())) {
            signDateForm.setDate(new Date());
        }
        LocalDateTime localDateTime = signDateForm.getDate().toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
        Long user = StpUtil.getLoginIdAsLong();
        boolean isSign = signService.isSign(user, localDateTime);
        if (isSign) {
            throw new BcyServiceException("当天已签到！");
        }
        signService.sign(user, localDateTime);
    }

    /**
     * 当月签到记录
     *
     * @return 记录数组
     */
    public Map<String, Boolean> record() {
        LocalDateTime localDateTime = LocalDateTime.now();
        Long user = StpUtil.getLoginIdAsLong();
        return signService.signInRecord(user, localDateTime);
    }

    /**
     * 当月连续签到次数
     *
     * @return 连续次数
     */
    public Integer count() {
        LocalDateTime localDateTime = LocalDateTime.now();
        Long user = StpUtil.getLoginIdAsLong();
        return signService.signCount(user, localDateTime);
    }
}
