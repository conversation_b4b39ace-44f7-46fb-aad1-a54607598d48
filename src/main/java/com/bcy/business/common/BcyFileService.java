package com.bcy.business.common;

import cn.dev33.satoken.stp.StpUtil;
import com.bcy.domain.response.OssResponse;
import com.bcy.service.log.ISysFileLogService;
import com.bcy.entity.log.SysFileLogEntity;
import com.bcy.service.oss.impl.OssSerService;
import com.bcy.utils.BcyFileUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * 文件操作服务
 *
 * <AUTHOR>
 * @since 2024-02-28
 */
@Slf4j
@Service
public class BcyFileService {
    @Resource
    private OssSerService ossSerService;
    @Resource
    private ISysFileLogService iSysFileLogService;

    /**
     * 文件上传
     *
     * @param file 上传的文件
     * @param type 类型
     * @return 上传结果
     */
    public OssResponse uploadFile(MultipartFile file, String type) {
        OssResponse response = ossSerService.ossUploadFile(file, type);
        if (Objects.nonNull(response)) {
            // 保存文件日志
            SysFileLogEntity sysFileLogEntity = new SysFileLogEntity();
            BeanUtils.copyProperties(response, sysFileLogEntity);
            if (StpUtil.isLogin()) {
                sysFileLogEntity.setUserId(StpUtil.getLoginIdAsLong());
            } else {
                sysFileLogEntity.setUserId(BigDecimal.ZERO.longValue());
            }
            sysFileLogEntity.setExportTime(new Date());
            sysFileLogEntity.setSourceFileName(file.getOriginalFilename());
            sysFileLogEntity.setFileUrl(response.getUrl());
            iSysFileLogService.save(sysFileLogEntity);
        }
        return response;
    }

    /**
     * 文件删除
     *
     * @param filePath 文件路径
     * @return true/false
     */
    public boolean deleteFile(String filePath) {
        SysFileLogEntity sysFileLogEntity =iSysFileLogService.getInfoByUrl(filePath);
        String path = Objects.nonNull(sysFileLogEntity)? sysFileLogEntity.getFilePath(): StringUtils.EMPTY;
        if(StringUtils.isBlank(path)){
            return false;
        }
        BcyFileUtils.deleteFile(path);
        // 删除相应的记录
        return iSysFileLogService.removeById(sysFileLogEntity.getId());
    }

    /**
     * 文件上传
     *
     * @param file     上传的文件
     * @param filePath 存储路径
     * @return 上传结果
     */
    public String uploadToPath(MultipartFile file, String filePath) {
        return ossSerService.uploadFilePath(file, filePath);
    }
}
