package com.bcy.business.common;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.bcy.constants.BcyAdminBusinessConstants;
import com.bcy.domain.response.page.sys.SysDeptPageResponse;
import com.bcy.service.sys.IModuleService;
import com.bcy.service.sys.ITenantService;
import com.bcy.service.sys.IUserTenantService;
import com.bcy.business.sys.BcySysDeptService;
import com.bcy.business.sys.BcySysDictionItemService;
import com.bcy.business.sys.BcySysMenuService;
import com.bcy.business.sys.BcySysRoleService;
import com.bcy.domain.response.DropDownResponse;
import com.bcy.domain.response.sys.SysMenuResponse;
import com.bcy.entity.sys.SysModuleEntity;
import com.bcy.entity.sys.SysRoleEntity;
import com.bcy.entity.sys.SysTenantEntity;
import com.bcy.entity.sys.SysUserTenantEntity;
import com.bcy.admin.enums.SysDropDownTypeEnum;
import com.bcy.utils.BcyUtils;
import com.bcy.utils.DropDownUtils;
import com.bcy.utils.RedisUtils;
import com.bcy.utils.ServletUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static com.bcy.admin.constants.BcyAdminConstants.REDIS_DICT_KEY;
import static com.bcy.admin.constants.BcyAdminConstants.SERVICE_MODULE;


/**
 * 下拉相关服务
 *
 * <AUTHOR>
 * @since 2024/01/02-21:38
 */
@Service
@Slf4j
public class BcySysDropDownService {
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private BcySysMenuService bcySysMenuService;
    @Resource
    private BcySysDeptService bcySysDeptService;
    @Resource
    private BcySysRoleService bcySysRoleService;
    @Resource
    private BcySysDictionItemService bcySysDictionItemService;
    @Resource
    private ITenantService iTenantService;
    @Resource
    private IUserTenantService iUserTenantService;
    @Resource
    private IModuleService iModuleService;

    /**
     * 字典下拉选择框
     *
     * @param code 标识符
     * @return 下拉列表
     */
    public List<DropDownResponse> dictionList(String code) {
        // 先从redis去取数据
        List<DropDownResponse> list = redisUtils.getCacheObject(String.format("%s%s", REDIS_DICT_KEY, code));
        if (CollectionUtil.isEmpty(list)) {
            bcySysDictionItemService.updateDictRedis();
        }
        try {
            return redisUtils.getCacheObject(String.format("%s%s", REDIS_DICT_KEY, code));
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    /**
     * 根据类型获取下拉框
     *
     * @param type 下拉请求
     * @return 下拉列表
     */
    public List<DropDownResponse> dropDownByType(String type) {
        if (SysDropDownTypeEnum.DEPT.name().equals(type)) {
            return listDept();
        } else if (SysDropDownTypeEnum.MENU.name().equals(type)) {
            return listMenu(false);
        } else if (SysDropDownTypeEnum.MENU_BUTTON.name().equals(type)) {
            return listMenu(true);
        } else if (SysDropDownTypeEnum.ROLE.name().equals(type)) {
            return listUserRole();
        } else if (SysDropDownTypeEnum.TENANT.name().equals(type)) {
            return listTenant();
        } else if (SysDropDownTypeEnum.MODULE.name().equals(type)) {
            return listModule();
        }
        return new ArrayList<>();
    }

    /**
     * 租户下拉列表
     *
     * @return 列表
     */
    public List<DropDownResponse> listTenant() {
        Long userId = StpUtil.getLoginIdAsLong();
        List<DropDownResponse> responses = new ArrayList<>();
        List<SysTenantEntity> list;
        // 判断是否超级管理员
        if (BcyUtils.isSuperAdmin(userId)) {
            list = iTenantService.list();
        } else {
            List<Long> tenantIdList = iUserTenantService.tenantListByUserId(userId).stream().map(SysUserTenantEntity::getLinkTenantId).toList();
            list = iTenantService.listByIds(tenantIdList);
        }
        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(item -> {
                DropDownResponse dropDownResponse = new DropDownResponse();
                dropDownResponse.setId(item.getId());
                dropDownResponse.setValue(item.getId());
                dropDownResponse.setLabel(item.getName());
                responses.add(dropDownResponse);
            });
        }
        return responses;
    }

    /**
     * 菜单下拉列表，不包含按钮
     *
     * @return 菜单下拉列表
     */
    public List<DropDownResponse> listMenu(boolean hasButton) {
        List<DropDownResponse> dropDownVoList = new ArrayList<>();
        List<SysMenuResponse> responses = bcySysMenuService.userMenuList(hasButton, StringUtils.EMPTY);
        if (CollectionUtil.isNotEmpty(responses)) {
            responses.stream().filter(sysMenuResponse -> sysMenuResponse.getModule().equalsIgnoreCase(ServletUtils.getRequest().getHeader(SERVICE_MODULE)) || sysMenuResponse.getModule().equalsIgnoreCase(BcyAdminBusinessConstants.API_SERVICE)).forEach(item -> {
                DropDownResponse dropDownVo = new DropDownResponse();
                dropDownVo.setId(item.getId());
                dropDownVo.setKey(item.getId());
                dropDownVo.setParentId(item.getParentId());
                dropDownVo.setValueStr(item.getId().toString());
                dropDownVo.setValue(item.getId());
                dropDownVo.setLabel(item.getName());
                dropDownVoList.add(dropDownVo);
            });
        }
        return DropDownUtils.buildTreeByStream(dropDownVoList);
    }

    /**
     * 部门下拉列列表
     *
     * @return 部门下拉列表
     */
    public List<DropDownResponse> listDept() {
        List<DropDownResponse> dropDownVoList = new ArrayList<>();
        List<SysDeptPageResponse> deptEntities = bcySysDeptService.deptList(StringUtils.EMPTY);
        if (CollectionUtil.isNotEmpty(deptEntities)) {
            deptEntities.forEach(item -> {
                DropDownResponse dropDownVo = new DropDownResponse();
                dropDownVo.setId(item.getId());
                dropDownVo.setParentId(item.getParentId());
                dropDownVo.setValue(item.getId());
                dropDownVo.setLabel(item.getName());
                dropDownVoList.add(dropDownVo);
            });
        }
        return DropDownUtils.buildTreeByStream(dropDownVoList);
    }

    /**
     * 获取当前用户的角色下拉列表
     *
     * @return 角色下拉列表
     */
    public List<DropDownResponse> listUserRole() {
        List<DropDownResponse> dropDownVoList = new ArrayList<>();
        List<SysRoleEntity> list = bcySysRoleService.listByLoginUserId();
        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(item -> {
                DropDownResponse dropDownVo = new DropDownResponse();
                dropDownVo.setId(item.getId());
                dropDownVo.setValue(item.getId());
                dropDownVo.setValueStr(item.getCode());
                dropDownVo.setLabel(item.getName());
                dropDownVoList.add(dropDownVo);
            });
        }
        return dropDownVoList;
    }

    /**
     * 部门下拉列列表
     *
     * @return 部门下拉列表
     */
    public List<DropDownResponse> listModule() {
        List<DropDownResponse> dropDownVoList = new ArrayList<>();
        List<SysModuleEntity> moduleEntities = iModuleService.list();
        if (CollectionUtil.isNotEmpty(moduleEntities)) {
            moduleEntities.forEach(item -> {
                DropDownResponse dropDownVo = new DropDownResponse();
                dropDownVo.setId(item.getId());
                dropDownVo.setParentId(item.getId());
                dropDownVo.setValue(item.getId());
                dropDownVo.setValueStr(item.getCode());
                dropDownVo.setLabel(item.getName());
                dropDownVoList.add(dropDownVo);
            });
        }
        return DropDownUtils.buildTreeByStream(dropDownVoList);
    }
}
