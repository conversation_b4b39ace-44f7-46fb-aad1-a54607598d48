package com.bcy.business.common;

import cn.hutool.core.collection.CollectionUtil;
import com.bcy.domain.response.DropDownResponse;
import com.bcy.entity.pet.BcyPetCategoryEntity;
import com.bcy.entity.web.BcyWebConfigEntity;
import com.bcy.enums.CmsDropDownTypeEnum;
import com.bcy.service.pet.IPetCategoryService;
import com.bcy.service.web.IWebConfigService;
import com.bcy.utils.DropDownUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


/**
 * 下拉相关服务
 *
 * <AUTHOR>
 * @since 2024/01/02-21:38
 */
@Service
@Slf4j
public class BcyCmsDropDownService {

    @Resource
    private IWebConfigService iWebConfigService;

    @Resource
    private IPetCategoryService iPetCategoryService;

    /**
     * 留资站点下拉列表
     *
     * @return 列表
     */
    public List<DropDownResponse> siteList() {
        List<DropDownResponse> responses = new ArrayList<>();
        List<BcyWebConfigEntity> list = iWebConfigService.list();
        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(item -> {
                DropDownResponse dropDownResponse = new DropDownResponse();
                dropDownResponse.setId(item.getId());
                dropDownResponse.setKey(item.getId());
                dropDownResponse.setValue(item.getId());
                dropDownResponse.setValueStr(item.getId().toString());
                dropDownResponse.setLabel(item.getSiteName());
                responses.add(dropDownResponse);
            });
        }
        return responses;
    }


    /**
     * 宠物分类下拉列表
     *
     * @param hasChild 是否包含子列表
     * @return 列表
     */
    public List<DropDownResponse> petCategoryList(boolean hasChild) {
        List<DropDownResponse> responses = new ArrayList<>();
        List<BcyPetCategoryEntity> list = iPetCategoryService.list();
        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(item -> {
                DropDownResponse dropDownResponse = new DropDownResponse();
                dropDownResponse.setId(item.getId());
                dropDownResponse.setParentId(item.getParentId());
                dropDownResponse.setKey(item.getId());
                dropDownResponse.setValue(item.getId());
                dropDownResponse.setValueStr(item.getId().toString());
                dropDownResponse.setLabel(item.getName());
                responses.add(dropDownResponse);
            });
            if (hasChild) {
                return DropDownUtils.buildTreeByStream(responses);
            }
            return responses.stream().filter(item -> item.getParentId() == 0).toList();
        }
        return responses;
    }


    /**
     * 根据类型获取下拉框
     *
     * @param type 下拉请求
     * @return 下拉列表
     */
    public List<DropDownResponse> dropDownByType(String type) {
        if (CmsDropDownTypeEnum.SITE_LIST.name().equals(type)) {
            return siteList();
        } else if (CmsDropDownTypeEnum.PET_CATEGORY_CHILD_LIST.name().equals(type)) {
            return petCategoryList(true);
        } else if (CmsDropDownTypeEnum.PET_CATEGORY_LIST.name().equals(type)) {
            return petCategoryList(false);
        }
        return new ArrayList<>();
    }
}
