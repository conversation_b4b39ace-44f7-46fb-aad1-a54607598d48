package com.bcy.business.common;

import com.alibaba.fastjson2.JSONObject;
import com.bcy.config.SmsProperties;
import com.bcy.admin.domain.form.SendSimpleMailForm;
import com.bcy.admin.domain.form.SendSmsForm;
import com.bcy.domain.form.SmsForm;
import com.bcy.domain.form.WechatTemplateForm;
import com.bcy.domain.response.SmsResponse;
import com.bcy.domain.response.WxMsgResponse;
import com.bcy.domain.response.common.SmsConfigResponse;
import com.bcy.admin.enums.SmsModeEnum;
import com.bcy.enums.SmsTypeEnum;
import com.bcy.exception.BcyServiceException;
import com.bcy.service.impl.SendSmsService;
import com.bcy.service.wechat.WechatMsgService;
import com.bcy.business.sys.BcySysConfigService;
import com.bcy.domain.form.user.RegisterCodeForm;
import com.bcy.domain.form.user.UserSimpleMailForm;
import com.bcy.domain.form.user.UserSmsForm;
import com.bcy.utils.BcyUtils;
import com.bcy.utils.MailUtils;
import com.bcy.utils.RedisUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.bcy.constant.BcyConstants.OK;
import static com.bcy.admin.constants.BcyAdminConstants.MAIL_CODE_VALIDATE;
import static com.bcy.admin.constants.BcyAdminConstants.PHONE_CODE_VALIDATE;
import static com.bcy.admin.constants.BcyAdminConstants.VERIFICATION_CODE_SMS;

/**
 * 短信相关服务
 *
 * <AUTHOR>
 * @since 2024/10/27-21:38
 */
@Service
@Slf4j
public class BcyMsgService {

    @Resource
    private MailUtils mailUtils;
    @Resource
    private BcySysConfigService bcySysConfigService;
    @Resource
    private SendSmsService sendSmsService;
    @Resource
    private WechatMsgService wechatMsgService;
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private SmsProperties smsProperties;
    @Value("${spring.mail.username}")
    private String userName;

    /**
     * 发送短信
     *
     * @param sendSmsForm 短信表单
     * @return 返回信息
     */
    public SmsResponse sendSms(SendSmsForm sendSmsForm) {
        SmsConfigResponse smsConfigResponse = bcySysConfigService.getSmsConfig(VERIFICATION_CODE_SMS);
        if (Objects.isNull(smsConfigResponse)) {
            throw new BcyServiceException("未配置短信配置，发送失败！");
        }

        SmsForm smsForm = new SmsForm();
        smsForm.setSignName(smsConfigResponse.getSignName());
        smsForm.setTemplateCode(smsConfigResponse.getTemplateCode());
        smsForm.setSdkAppId(smsConfigResponse.getSdkAppId());
        smsForm.setSignName(smsConfigResponse.getSignName());
        smsForm.setTemplateCode(smsConfigResponse.getTemplateCode());
        smsForm.setSdkAppId(smsConfigResponse.getSdkAppId());
        smsForm.setPhoneNumber(sendSmsForm.getPhoneNumber());
        smsForm.setTemplateParam(sendSmsForm.getParam());
        smsForm.setOutId(sendSmsForm.getOutId());
        smsForm.setParameters(sendSmsForm.getParameters());
        return sendSmsService.sendMsg(smsForm);
    }

    /**
     * 发送简单邮件
     *
     * @param sendSimpleMailForm 简单邮件表单
     * @return true/false
     */
    public boolean sendMail(SendSimpleMailForm sendSimpleMailForm) {
        sendSimpleMailForm.setFrom("博昌云科技<" + userName + ">");
        return mailUtils.sendGeneralEmail(sendSimpleMailForm.getFrom(), sendSimpleMailForm.getSubject(), sendSimpleMailForm.getContent(), sendSimpleMailForm.getTo());
    }

    /**
     * 发送微信通知
     *
     * @param wechatTemplateForm 简单邮件表单
     * @return 响应
     */
    public String sendWechatNotice(WechatTemplateForm wechatTemplateForm) {
        WxMsgResponse wxMsgResponse = wechatMsgService.sendTemplate(wechatTemplateForm);
        if (BigDecimal.ZERO.intValue() == wxMsgResponse.getErrCode()) {
            return "发送模板消息成功！";
        }
        return String.format("发送模板消息失败！%s", JSONObject.toJSONString(wxMsgResponse));
    }

    /**
     * 绑定手机号-短信验证码
     *
     * @param userSmsForm 用户信息表单
     */
    public void sendUserSms(UserSmsForm userSmsForm) {
        // 随机生成验证码
        String code = BcyUtils.codePhone(6);
        SmsResponse smsResponse = getSmsResponse(code, userSmsForm.getPhoneNumber());
        if (Objects.nonNull(smsResponse) && OK.equals(smsResponse.getCode())) {
            redisUtils.setCacheObject(String.format(PHONE_CODE_VALIDATE, userSmsForm.getMode(), userSmsForm.getPhoneNumber()), code, 1L, TimeUnit.MINUTES);
        }
    }

    /**
     * 发送验证码
     *
     * @param code        验证码
     * @param userSmsForm 用户表单
     * @return 短信返回对象
     */
    private SmsResponse getSmsResponse(String code, String userSmsForm) {
        SendSmsForm sendSmsForm = new SendSmsForm();
        // 随机生成验证码
        if (SmsTypeEnum.ALIYUN.equals(smsProperties.getType())) {
            String param = "{\"code\":\"" + code + "\"}";
            sendSmsForm.setPhoneNumber(userSmsForm);
            sendSmsForm.setParam(param);
        } else if (SmsTypeEnum.TENCENT.equals(smsProperties.getType())) {
            sendSmsForm.setParam(code);
            sendSmsForm.setPhoneNumber("+86" + userSmsForm);
        }
        return sendSms(sendSmsForm);
    }

    /**
     * 绑定邮箱-邮件验证码
     *
     * @param userSimpleMailForm 邮件表单
     */
    public void sendUserMail(UserSimpleMailForm userSimpleMailForm) {
        String code = BcyUtils.codePhone(6);
        redisUtils.setCacheObject(String.format(MAIL_CODE_VALIDATE, userSimpleMailForm.getMode(), userSimpleMailForm.getTo()), code, 5L, TimeUnit.MINUTES);
        SendSimpleMailForm sendSimpleMailForm = new SendSimpleMailForm();
        sendSimpleMailForm.setTo(userSimpleMailForm.getTo());
        sendSimpleMailForm.setContent(String.format("您的验证码为：%s，五分钟内有效，请勿泄露和回复！", code));
        sendSimpleMailForm.setSubject("短信验证码");
        sendSimpleMailForm.setFrom("博昌云科技<" + userName + ">");
        sendMail(sendSimpleMailForm);
    }

    /**
     * 注册-邮件或手机验证码
     *
     * @param registerCodeForm 邮件表单
     */
    public void registerMsg(RegisterCodeForm registerCodeForm) {
        String code = BcyUtils.codePhone(6);
        if (BigDecimal.ZERO.intValue() == registerCodeForm.getCodeType()) {
            SmsResponse smsResponse = getSmsResponse(code, registerCodeForm.getPhone());
            if (Objects.nonNull(smsResponse) && OK.equals(smsResponse.getCode())) {
                redisUtils.setCacheObject(String.format(PHONE_CODE_VALIDATE, SmsModeEnum.REGISTER.name(), registerCodeForm.getPhone()), code, 1L, TimeUnit.MINUTES);
            }
        } else if (BigDecimal.ONE.intValue() == registerCodeForm.getCodeType()) {
            redisUtils.setCacheObject(String.format(MAIL_CODE_VALIDATE, SmsModeEnum.REGISTER.name(), registerCodeForm.getEmail()), code, 5L, TimeUnit.MINUTES);
            SendSimpleMailForm sendSimpleMailForm = new SendSimpleMailForm();
            sendSimpleMailForm.setTo(registerCodeForm.getEmail());
            sendSimpleMailForm.setContent(String.format("您的验证码为：%s，五分钟内有效，请勿泄露和回复！", code));
            sendSimpleMailForm.setSubject("短信验证码");
            sendSimpleMailForm.setFrom("博昌云科技<" + userName + ">");
            sendMail(sendSimpleMailForm);
        }
    }
}
