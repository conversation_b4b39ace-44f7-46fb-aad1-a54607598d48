package com.bcy.job;

import com.bcy.business.log.BcyApiLogService;
import com.bcy.business.log.BcySysLogService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * XxlJob（Bean模式）-系统job处理
 *
 * <AUTHOR>
 * @since 2024-08-12
 */
@Component
@Slf4j
public class BcyJob {
    @Resource
    private BcySysLogService bcySysLogService;
    @Resource
    private BcyApiLogService bcyApiLogService;

    /**
     * 删除系统日志（Bean模式）
     */
    @XxlJob("deleteSysLogs")
    public ReturnT<String> deleteSysLogs() {
        String param = XxlJobHelper.getJobParam();
        log.info("开始执行删除系统日志...,{}天", param);
        bcySysLogService.deleteLogs(Integer.valueOf(param));
        return ReturnT.SUCCESS;
    }

    /**
     * 删除api日志（Bean模式）
     */
    @XxlJob("deleteApiLogs")
    public ReturnT<String> deleteApiLogs() {
        String param = XxlJobHelper.getJobParam();
        log.info("开始执行删除API日志...,{}天", param);
        bcyApiLogService.deleteLogs(Integer.valueOf(param));
        return ReturnT.SUCCESS;
    }
}