package com.bcy.spi;

import cn.hutool.core.collection.CollectionUtil;
import com.bcy.admin.domain.form.FileLogForm;
import com.bcy.admin.domain.form.SysApiLogForm;
import com.bcy.admin.spi.BcyAdminFeignSpi;
import com.bcy.business.common.BcySysDropDownService;
import com.bcy.business.log.BcyApiLogService;
import com.bcy.business.log.BcyFileLogService;
import com.bcy.business.sys.BcySysConfigService;
import com.bcy.business.sys.BcySysMenuService;
import com.bcy.business.sys.BcySysRoleService;
import com.bcy.domain.R;
import com.bcy.domain.response.DropDownResponse;
import com.bcy.domain.response.sys.SysMenuResponse;
import com.bcy.entity.sys.SysRoleEntity;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * 后台系统服务对内feign实现
 *
 * <AUTHOR>
 * @since 2023/12/17
 */
@RestController
public class BcyAdminFeignSpiImpl implements BcyAdminFeignSpi {
    @Resource
    private BcyFileLogService bcyFileLogService;
    @Resource
    private BcySysDropDownService bcySysDropDownService;
    @Resource
    private BcySysConfigService bcySysConfigService;
    @Resource
    private BcyApiLogService bcyApiLogService;
    @Resource
    private BcySysMenuService bcySysMenuService;
    @Resource
    private BcySysRoleService bcySysRoleService;

    @Override
    public void saveFileLog(FileLogForm fileLogForm) {
        bcyFileLogService.saveLog(fileLogForm);
    }

    @Override
    public R<List<DropDownResponse>> diction(String code) {
        return R.ok(bcySysDropDownService.dictionList(code));
    }

    @Override
    public String configByKey(String key) {
        return bcySysConfigService.getConfigValueByKey(key);
    }

    @Override
    public String dictionItemByCode(String code, String value) {
        List<DropDownResponse> list = bcySysDropDownService.dictionList(code);
        if (CollectionUtil.isNotEmpty(list)) {
            DropDownResponse dropDownResponse = list.stream().filter(item -> item.getValueStr().equals(value)).findFirst().orElse(null);
            if (Objects.nonNull(dropDownResponse)) {
                return dropDownResponse.getLabel();
            }
        }
        return StringUtils.EMPTY;
    }

    @Override
    public void saveApiLogs(List<SysApiLogForm> list) {
        bcyApiLogService.saveSysApiLogs(list);
    }

    @Override
    public List<String> getRoleList() {
        return bcySysRoleService.listByLoginUserId().stream().map(SysRoleEntity::getCode).toList();
    }

    @Override
    public List<String> getPermissionList() {
        return bcySysMenuService.userMenuList(true, StringUtils.EMPTY).stream().map(SysMenuResponse::getPerm).toList();
    }
}
