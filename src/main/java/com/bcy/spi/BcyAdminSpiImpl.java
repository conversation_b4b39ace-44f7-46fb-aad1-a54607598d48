package com.bcy.spi;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.bcy.admin.spi.BcyAdminSpi;
import com.bcy.annotation.BcyLog;
import com.bcy.admin.domain.form.SendSimpleMailForm;
import com.bcy.admin.domain.form.SendSmsForm;
import com.bcy.admin.domain.form.SysLogForm;
import com.bcy.domain.form.WechatTemplateForm;
import com.bcy.admin.domain.request.WechatNoticeRequest;
import com.bcy.domain.response.SmsResponse;
import com.bcy.business.common.BcyMsgService;
import com.bcy.business.log.BcySysLogService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RestController;

import static com.bcy.constant.BcyConstants.DEFAULT_SYS;


/**
 * 后台系统服务feign实现
 *
 * <AUTHOR>
 * @since 2023/12/17
 */
@RestController
public class BcyAdminSpiImpl implements BcyAdminSpi {
    @Resource
    private BcyMsgService bcyMsgService;
    @Resource
    private BcySysLogService bcySysLogService;

    @Override
    @BcyLog(businessType = "发送短信验证码", operatorType = DEFAULT_SYS, model = "bcy-admin")
    public String sendSms(SendSmsForm smsForm) {
        SmsResponse smsResponse = bcyMsgService.sendSms(smsForm);
        return JSON.toJSONString(smsResponse);
    }

    @Override
    @BcyLog(businessType = "发送简单邮件信息", operatorType = DEFAULT_SYS, model = "bcy-admin")
    public String sendMail(SendSimpleMailForm sendSimpleMailForm) {
        boolean flag = bcyMsgService.sendMail(sendSimpleMailForm);
        return flag ? "发送成功" : "发送失败";
    }

    @Override
    @BcyLog(businessType = "发送微信通知信息", operatorType = DEFAULT_SYS, model = "bcy-admin")
    public String sendWechatNotice(WechatNoticeRequest wechatNoticeRequest) {
        String request = wechatNoticeRequest.getJsonBody();
        WechatTemplateForm wechatTemplateForm = JSONObject.parseObject(request, WechatTemplateForm.class);
        return bcyMsgService.sendWechatNotice(wechatTemplateForm);
    }

    @Override
    public void saveLogs(SysLogForm sysLogForm) {
        bcySysLogService.saveSysLogs(sysLogForm);
    }
}
