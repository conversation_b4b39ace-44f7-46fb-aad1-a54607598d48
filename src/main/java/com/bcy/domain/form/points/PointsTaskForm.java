package com.bcy.domain.form.points;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.Date;

@Data
public class PointsTaskForm {
    @Schema(description = "主键")
    private Long id;
    @Schema(description = "任务名称")
    private String name;
    @Schema(description = "任务类型（1新手、2日常等）")
    private Integer type;
    @Schema(description = "任务描述")
    private String description;
    @Schema(description = "完成奖励积分")
    private Integer points;
    @Schema(description = "每日完成上限（0为不限）")
    private Integer dailyLimit;
    @Schema(description = "总完成上限（0为不限）")
    private Integer totalLimit;
    @Schema(description = "是否启用（1启用，0禁用）")
    private Integer isActive;
    @Schema(description = "开始时间")
    private Date startTime;
    @Schema(description = "结束时间")
    private Date endTime;
} 