package com.bcy.domain.form.points;

import com.bcy.domain.form.IdForm;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Max;

/**
 * 用户地址表单
 *
 * <AUTHOR>
 * @since 2025-05-06
 */
@Schema(description = "用户地址表单")
@Data
@EqualsAndHashCode(callSuper = true)
public class UserAddressForm extends IdForm {
    @Schema(description = "用户ID")
    private Long userId;
    @NotBlank(message = "收件人姓名不能为空")
    @Schema(description = "收件人姓名")
    private String receiverName;
    @Schema(description = "收件人手机号")
    @Max(value = 200, message = "手机号长度不能超过20位")
    @NotBlank(message = "接收人手机不能为空")
    private String receiverPhone;
    @Schema(description = "省")
    private String province;
    @Schema(description = "市")
    private String city;
    @Schema(description = "区/县")
    private String district;
    @NotBlank(message = "详细地址不能为空")
    @Schema(description = "详细地址")
    private String addressDetail;
    @Schema(description = "是否默认地址（1是0否）")
    private Integer isDefault;
} 