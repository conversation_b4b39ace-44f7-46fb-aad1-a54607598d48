package com.bcy.domain.form.points;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 兑换商品请求
 *
 * <AUTHOR>
 * @since 2024-08-21
 */
@Schema(description = "兑换商品请求")
@Data
public class PointsGoodsOrderForm {
    @NotNull(message = "商品ID不能为空")
    @Schema(description = "商品ID")
    private Long goodsId;
    @NotNull(message = "兑换数量不能为空")
    @Min(value = 1, message = "兑换数量不能小于1")
    @Schema(description = "兑换数量")
    private Integer quantity;
    @Schema(description = "收货地址ID")
    private Long addressId;
} 