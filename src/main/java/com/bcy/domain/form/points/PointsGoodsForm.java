package com.bcy.domain.form.points;

import com.bcy.domain.form.IdForm;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 积分商品表单
 *
 * <AUTHOR>
 * @since 2020-05-06
 */
@Schema(description = "积分商品表单")
@Data
@EqualsAndHashCode(callSuper = true)
public class PointsGoodsForm extends IdForm {
    @NotBlank(message = "商品名称不能为空")
    @Schema(description = "商品名称")
    private String name;
    @Schema(description = "商品描述")
    private String description;
    @Min(value = 10, message = "所需积分不能小于10")
    @Schema(description = "所需积分")
    private Integer pointsCost;
    @Min(value = 1, message = "库存不能小于1")
    @Schema(description = "库存")
    private Integer stock;
    @Schema(description = "每人限兑数量")
    private Integer limitPerUser;
    @Schema(description = "是否上架(0否1是)")
    private Integer isActive;
} 