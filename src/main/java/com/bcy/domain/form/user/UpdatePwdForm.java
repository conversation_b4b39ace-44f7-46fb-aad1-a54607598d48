package com.bcy.domain.form.user;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serial;
import java.io.Serializable;

/**
 * 更改密码表单
 *
 * <AUTHOR>
 * @since 2020-05-06
 */
@Schema(description = "更改密码表单")
@Data
public class UpdatePwdForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 8172428982482518879L;

    @Schema(description = "旧密码")
    @NotBlank(message = "旧密码不能为空")
    private String oldPassword;

    @Schema(description = "新密码")
    @NotBlank(message = "新密码不能为空")
    @Length(min = 6, message = "密码位数不能小于6")
    private String newPassword;

    @Schema(description = "重复新密码")
    @NotBlank(message = "重复新密码不能为空")
    @Length(min = 6, message = "密码位数不能小于6")
    private String newRepeatPassword;

}
