package com.bcy.domain.form.user;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 用户注册验证码表单
 *
 * <AUTHOR>
 * @since 2024-05-06
 */
@Schema(description = "用户注册验证码表单")
@Data
public class RegisterCodeForm implements Serializable {
    @Serial
    private static final long serialVersionUID = 6086418958747946162L;

    @Schema(description = "类型（0手机号1邮箱）")
    @NotNull(message = "类型不能为空")
    private Integer codeType;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "邮箱")
    private String email;
}
