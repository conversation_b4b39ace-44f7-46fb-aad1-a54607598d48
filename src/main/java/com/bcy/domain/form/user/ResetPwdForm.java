package com.bcy.domain.form.user;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 重置密码表单
 *
 * <AUTHOR>
 * @since 2023/5/13-12:43
 */
@Data
@Schema(description = "用户分页表单")
public class ResetPwdForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 3754998338678378074L;

    @Schema(description = "密码")
    @NotBlank(message = "密码不能为空")
    private String password;

    @Schema(description = "用户Id")
    @NotNull(message = "用户Id不能为空")
    private Long userId;

}
