package com.bcy.domain.form.user;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serial;
import java.io.Serializable;

/**
 * 忘记密码表单
 *
 * <AUTHOR>
 * @since 2020-05-06
 */
@Schema(description = "忘记密码表单")
@Data
public class ForgetPwdForm implements Serializable {

    @Serial
    private static final long serialVersionUID = 8172428982482518879L;

    @Schema(description = "类型（0手机号1邮箱）")
    @NotNull(message = "类型不能为空")
    private Integer codeType;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "新密码")
    @NotBlank(message = "新密码不能为空")
    @Length(min = 6, message = "密码位数不能小于6")
    private String newPassword;

    @Schema(description = "重复新密码")
    @NotBlank(message = "重复新密码不能为空")
    @Length(min = 6, message = "密码位数不能小于6")
    private String newRepeatPassword;

    @Schema(description = "验证码")
    @NotBlank(message = "验证码不能为空")
    private String code;
}
