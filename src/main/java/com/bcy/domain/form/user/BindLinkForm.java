package com.bcy.domain.form.user;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 用户绑定联系方式表单
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
@Schema(description = "用户绑定联系方式表单")
@Data
public class BindLinkForm implements Serializable {
    @Serial
    private static final long serialVersionUID = 1054456077419038893L;

    @Schema(description = "类型（0手机号1邮箱）")
    @NotNull(message = "类型不能为空")
    private Integer codeType;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "验证码")
    @NotBlank(message = "验证码不能为空")
    private String code;

}
