package com.bcy.domain.form.user;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serial;
import java.io.Serializable;

/**
 * 发送短信表单
 *
 * <AUTHOR>
 * @since 2024-05-06
 */
@Schema(description = "发送短信表单")
@Data
public class UserSmsForm implements Serializable {
    @Serial
    private static final long serialVersionUID = 2943565203871796117L;

    @Schema(description = "手机号码")
    @NotBlank(message = "手机号码不能为空")
    @Length(min = 5, max = 11, message = "手机号码不能小于5位且大于11位")
    private String phoneNumber;

    @Schema(description = "短信类型")
    @NotBlank(message = "短信类型不能为空")
    private String mode;
}
