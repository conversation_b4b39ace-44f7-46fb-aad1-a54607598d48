package com.bcy.domain.form.user;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 个人中心表单
 *
 * <AUTHOR>
 * @since 2024/09/28
 */
@Schema(description = "头像表单")
@Data
public class AvatarForm implements Serializable {

    @Serial
    private static final long serialVersionUID = -8448221573356986535L;

    @Schema(description = "头像路径")
    @NotBlank(message = "头像路径不能为空")
    private String avatar;
}
