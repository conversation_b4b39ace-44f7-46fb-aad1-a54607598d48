package com.bcy.domain.form.user;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 简单邮件发送表单
 *
 * <AUTHOR>
 * @since 2024/09/14
 */
@Schema(description = "简单邮件发送表单")
@Data
public class UserSimpleMailForm implements Serializable {

    @Serial
    private static final long serialVersionUID = -2367028741560305175L;

    @Schema(description = "主题")
    private String subject;

    @Schema(description = "内容")
    private String content;

    @Schema(description = "收件人")
    @NotBlank(message = "收件人不能为空")
    private String to;

    @Schema(description = "类型")
    @NotBlank(message = "类型不能为空")
    private String mode;
}
