package com.bcy.domain.form.user;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serial;
import java.io.Serializable;

/**
 * 用户注册表单
 *
 * <AUTHOR>
 * @since 2024-05-06
 */
@Schema(description = "用户注册表单")
@Data
public class RegisterForm implements Serializable {
    @Serial
    private static final long serialVersionUID = -4118850086608741796L;

    @Schema(description = "用户名")
    @NotBlank(message = "用户名不能为空")
    private String userName;

    @Schema(description = "姓名")
    private String nickName;

    @Schema(description = "类型（0手机号1邮箱）")
    @NotNull(message = "类型不能为空")
    private Integer codeType;

    @Schema(description = "类型")
    @NotNull(message = "类型不能为空")
    private Integer type;

    @Schema(description = "性别")
    private String sex;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "密码")
    @NotBlank(message = "密码不能为空")
    @Length(min = 6, message = "密码位数不能小于6")
    private String newPassword;

    @Schema(description = "重复密码")
    @NotBlank(message = "重复密码不能为空")
    @Length(min = 6, message = "密码位数不能小于6")
    private String newRepeatPassword;

    @Schema(description = "验证码")
    @NotBlank(message = "验证码不能为空")
    private String code;
}
