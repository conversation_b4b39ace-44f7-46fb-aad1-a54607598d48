package com.bcy.domain.form.msg;


import com.bcy.domain.form.IdForm;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 系统信息
 *
 * <AUTHOR>
 * @since 2020-05-06
 */
@Schema(description = "系统信息表单")
@Data
@EqualsAndHashCode(callSuper = true)
public class SysMessageForm extends IdForm {

    @Schema(description = "主题")
    @NotBlank(message = "消息主题不能为空")
    private String title;

    @Schema(description = "消息内容")
    @NotBlank(message = "消息内容不能为空")
    private String message;

    @Schema(description = "消息类型")
    private String type;

    @Schema(description = "通知类型")
    private String noticeType;

}
