package com.bcy.domain.form.login;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serial;
import java.io.Serializable;

/**
 * 登录表单
 *
 * <AUTHOR>
 * @since 2020-05-06
 */
@Schema(description = "用户登录表单")
@Data
public class LoginForm implements Serializable {
    @Serial
    private static final long serialVersionUID = 1267759486049261488L;

    @Schema(description = "用户名", example = "admin")
    @Length(min = 2, max = 20, message = "用户不小于2位且不大于20位")
    @NotBlank(message = "用户名不能为空")
    private String userName;

    @Schema(description = "密码")
    @Length(min = 6, max = 20, message = "密码不小于6位且不大于20位")
    @NotBlank(message = "密码不能为空")
    private String password;

    @Schema(description = "验证码")
    private String captcha;

    @Schema(description = "uuid")
    private String uuid;
}
