package com.bcy.domain.form.pet;

import com.bcy.domain.form.IdForm;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 宠物分类详情表单
 *
 * <AUTHOR>
 * @since 2024-08-21
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "宠物分类详情表单")
@Data
public class BcyPetCategoryDetailForm extends IdForm {

    @Schema(description = "分类ID")
    @NotNull(message = "分类ID不能为空")
    private Long categoryId;

    @Schema(description = "详情")
    @NotBlank(message = "详情不能为空")
    private String description;

    @Schema(description = "标签")
    @NotBlank(message = "标签不能为空")
    private String tags;

}