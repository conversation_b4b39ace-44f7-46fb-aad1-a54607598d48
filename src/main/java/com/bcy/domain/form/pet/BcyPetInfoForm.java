package com.bcy.domain.form.pet;

import com.bcy.domain.form.IdForm;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 用户宠物信息表单
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "用户宠物信息表单")
@Data
public class BcyPetInfoForm extends IdForm {

    @Schema(description = "分类ID")
    private Long categoryId;

    @Schema(description = "宠物名称")
    @NotBlank(message = "爱称不能为空")
    private String name;

    @Schema(description = "简介")
    private String shortDescription;

    @Schema(description = "图片")
    @NotBlank(message = "图片不能为空")
    private String image;

    @Schema(description = "性别")
    private String sex;

    @Schema(description = "出生日期")
    private Date birthday;
}