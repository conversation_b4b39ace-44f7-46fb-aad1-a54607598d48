package com.bcy.domain.form.pet;

import com.bcy.domain.form.IdForm;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 科普文章表单
 *
 * <AUTHOR>
 * @since 2024-08-21
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "宠物科普文章表单")
@Data
public class BcyPetContentForm extends IdForm {

    @Schema(description = "分类ID")
    private Long categoryId;

    @Schema(description = "文章标题")
    @NotBlank(message = "文章标题不能为空")
    private String title;

    @Schema(description = "文章副标题")
    private String subTitle;

    @Schema(description = "简介")
    @NotBlank(message = "简介不能为空")
    private String shortDescription;

    @Schema(description = "文章内容")
    @NotBlank(message = "文章内容不能为空")
    private String details;

    @Schema(description = "文章缩略图")
    private String image;

    @Schema(description = "文章来源")
    private String source;

    @Schema(description = "文章作者")
    private String author;

    @Schema(description = "是否显示0显示 1不显示")
    private Integer status;

    @Schema(description = "文章类型")
    private String type;

    @Schema(description = "发布时间")
    private Date pushTime;

    @Schema(description = "自定义顺序")
    private Integer sort;

    @Schema(description = "文章标签")
    private String tags;
}