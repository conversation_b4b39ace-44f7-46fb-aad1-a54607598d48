package com.bcy.domain.form.pet;


import com.bcy.domain.form.IdForm;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 宠物分类表单
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "宠物分类表单")
@Data
public class BcyPetCategoryForm extends IdForm {

    @Schema(description = "父级ID")
    @NotNull(message = "父级ID不能为空")
    private Long parentId;

    @Schema(description = "分类名称")
    @NotBlank(message = "分类名称不能为空")
    private String name;

    @Schema(description = "banner图")
    private String images;

    @Schema(description = "分类图标")
    private String ico;

    @Schema(description = "是否显示（0是1否）")
    private Integer status;

    @Schema(description = "描述")
    private String descrip;

    @Schema(description = "关键字")
    private String keyword;

    @Schema(description = "自定义顺序")
    private Integer sort;
}
