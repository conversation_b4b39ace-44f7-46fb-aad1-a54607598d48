package com.bcy.domain.form.common;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 微信模板消息表单
 *
 * <AUTHOR>
 * @since 2024-09-18
 */
@Schema(description = "微信模板消息表单")
@Data
public class WechatCmsTemplateForm {

    @Schema(description = "接收人openID")
    @JsonProperty("touser")
    @NotBlank(message = "接收人不能为空")
    private String toUser;

    @Schema(description = "模板ID")
    @JsonProperty("template_id")
    @NotBlank(message = "模板ID不能为空")
    private String templateId;

    @Schema(description = "模板跳转链接（海外账号没有跳转能力）")
    private String url;

    @JsonProperty("miniprogram")
    @Schema(description = "跳小程序所需数据，不需跳小程序可不用传该数据")
    private String miniProgram;

    @Schema(description = "模板数据")
    @NotBlank(message = "模板数据不能为空")
    private String data;

    @Schema(description = "防重入id。对于同一个openid + client_msg_id, 只发送一条消息,10分钟有效,超过10分钟不保证效果。若无防重入需求，可不填")
    @JsonProperty("client_msg_id")
    private String client_msg_id;

}
