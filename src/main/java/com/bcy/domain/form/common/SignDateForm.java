package com.bcy.domain.form.common;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 签到时间表单
 *
 * <AUTHOR>
 * @since 2024/3/9 23:22
 */
@Schema(description = "签到时间表单")
@Data
public class SignDateForm implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "时间")
    private Date date;
}
