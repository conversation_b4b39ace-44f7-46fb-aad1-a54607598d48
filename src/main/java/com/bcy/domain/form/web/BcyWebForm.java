package com.bcy.domain.form.web;

import com.bcy.domain.form.IdForm;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 留资站点表单
 *
 * <AUTHOR>
 * @since 2024-08-21
 */
@Schema(description = "留资站点表单")
@Data
@EqualsAndHashCode(callSuper = true)
public class BcyWebForm extends IdForm {

    @Schema(description = "站点名称")
    @NotBlank(message = "名称不能为空")
    private String siteName;

    @Schema(description = "ico图标")
    private String icon;

    @Schema(description = "头像")
    @NotBlank(message = "头像不能为空")
    private String avatar;

    @Schema(description = "上部分图片")
    @NotBlank(message = "上部分图片不能为空")
    private String headImage;

    @Schema(description = "下部分图片")
    private String bottomImage;

    @Schema(description = "站点版权信息")
    private String siteCopyright;

    @Schema(description = "联系电话")
    private String siteLinkPhone;

    @Schema(description = "平台类型")
    private String platformType;

    @Schema(description = "跳转地址")
    private String linkUrl;

    @Schema(description = "通知联系信息")
    private String noticeMsg;

    @Schema(description = "微信通知模板Id")
    private String wxTemplateId;

    @Schema(description = "通知类型")
    private String noticeType;

    @Schema(description = "状态（0启用1禁用）")
    private Integer status;

    @Schema(description = "悬浮窗标题")
    private String title;

    @Schema(description = "悬浮窗副标题")
    private String subTitle;

    @Schema(description = "下部分图片")
    @NotEmpty(message = "下部分图片不能为空")
    private List<String> bottomImageList;
}