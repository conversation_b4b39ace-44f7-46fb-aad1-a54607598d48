package com.bcy.domain.form.web;

import com.bcy.domain.form.IdForm;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 更新客户相关状态
 *
 * <AUTHOR>
 * @since 2024-08-21
 */
@Schema(description = "更新客户相关状态")
@Data
@EqualsAndHashCode(callSuper = true)
public class BcyCustomerForm extends IdForm {

    @Schema(description = "备注")
    private String remark;
}