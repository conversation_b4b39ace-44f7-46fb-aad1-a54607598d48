package com.bcy.domain.form.web;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 客户咨询表单
 *
 * <AUTHOR>
 * @since 2024-08-21
 */
@Schema(description = "客户咨询表单")
@Data
public class BcySeekAdviceForm {

    @Schema(description = "siteId")
    @NotNull(message = "站点id不能为空")
    private Long siteId;

    @Schema(description = "客户名称")
    private String name;

    @Schema(description = "联系电话")
    @NotBlank(message = "手机号码不能为空")
    private String phone;

    @Schema(description = "验证码")
    @NotBlank(message = "手机验证码不能为空")
    private String code;

    @Schema(description = "全路径")
    private String url;

    @Schema(description = "客户需求")
    @NotBlank(message = "需求不能为空")
    private String demand;
}
