package com.bcy.domain.form.sys;


import com.bcy.domain.form.IdForm;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 系统服务配置
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Schema(description = "系统配置表单")
@Data
@EqualsAndHashCode(callSuper = true)
public class SysModuleForm extends IdForm {

    @Schema(description = "模块名称")
    @NotBlank(message = "模块名称不能为空")
    private String name;

    @Schema(description = "模块代码")
    @NotBlank(message = "模块代码不能为空")
    private String code;

    @Schema(description = "路径")
    @NotBlank(message = "路径不能为空")
    private String url;

    @Schema(description = "状态（0展示1隐藏）")
    private Integer status;

    @Schema(description = "备注")
    private String remark;

}
