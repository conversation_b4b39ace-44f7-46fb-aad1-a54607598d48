package com.bcy.domain.form.sys;


import com.bcy.domain.form.IdForm;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 系统配置
 *
 * <AUTHOR>
 * @since 2020-05-06
 */
@Schema(description = "系统配置表单")
@Data
@EqualsAndHashCode(callSuper = true)
public class SysConfigForm extends IdForm {

    @Schema(description = "配置名称")
    @NotBlank(message = "配置名称不能为空")
    private String name;


    @Schema(description = "配置标识符")
    @NotBlank(message = "配置标识符不能为空")
    private String paramsKey;

    @Schema(description = "配置json值")
    @NotBlank(message = "配置json值不能为空")
    private String paramsValue;

    @Schema(description = "是否启用（0是1否）")
    private Integer status;

    @Schema(description = "备注")
    private String remark;

}
