package com.bcy.domain.form.sys;

import com.bcy.domain.form.IdForm;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 菜单表单
 *
 * <AUTHOR>
 * @since 2022/09/28
 */
@Schema(description = "菜单表单")
@Data
@EqualsAndHashCode(callSuper = true)
public class SysMenuForm extends IdForm {

    @NotNull(message = "父菜单ID不能为空")
    @Schema(description = "父菜单ID，一级菜单为0")
    private Long parentId;

    @Schema(description = "模块服务", example = "bcy-admin")
    private String module;

    @NotBlank(message = "菜单名称不能为空")
    @Schema(description = "菜单名称")
    private String name;

    @NotBlank(message = "菜单类型不能为空")
    @Schema(description = "菜单类型(MENU:菜单 CATALOG:目录 BUTTON:按钮 EXT_LINK:外链)")
    private String type;

    @Schema(description = "路由路径(浏览器地址栏路径)")
    private String path;

    @Schema(description = "组件路径(vue页面完整路径，省略.vue后缀)")
    private String component;

    @Schema(description = "权限标识")
    private String perm;

    @Schema(description = "显示状态(1:显示;0:隐藏)")
    private Integer visible;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "菜单图标")
    private String icon;

    @Schema(description = "跳转路径")
    private String redirect;

    @Schema(description = "【目录】只有一个子路由是否始终显示(1:是 0:否)")
    private Integer alwaysShow;

    @Schema(description = "【菜单】是否开启页面缓存(1:是 0:否)")
    private Integer keepAlive;

}
