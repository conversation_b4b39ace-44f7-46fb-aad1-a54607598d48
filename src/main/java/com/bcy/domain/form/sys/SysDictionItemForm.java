package com.bcy.domain.form.sys;

import com.bcy.domain.form.IdForm;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 字典项表单
 *
 * <AUTHOR>
 * @since 2020-05-06
 */
@Schema(description = "字典项表单")
@Data
@EqualsAndHashCode(callSuper = true)
public class SysDictionItemForm extends IdForm {

    @Schema(description = "字典项名称")
    @NotBlank(message = "字典项名称不能为空")
    private String name;

    @Schema(description = "字典id名称")
    @NotNull(message = "字典id不能为空")
    private Long dictionId;

    @Schema(description = "字典项")
    @NotBlank(message = "字典项不能为空")
    private String value;

    @Schema(description = "排序")
    private Integer sort;
}
