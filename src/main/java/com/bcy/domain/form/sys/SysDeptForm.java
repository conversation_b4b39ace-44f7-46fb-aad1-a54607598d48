package com.bcy.domain.form.sys;

import com.bcy.domain.form.IdForm;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 部门表单
 *
 * <AUTHOR>
 * @since 2024-10-06
 */
@Schema(description = "部门表单")
@Data
@EqualsAndHashCode(callSuper = true)
public class SysDeptForm extends IdForm {

    @Schema(description = "上级部门")
    private Long parentId;

    @Schema(description = "部门名称")
    @NotBlank(message = "部门名称不能为空")
    private String name;

    @Schema(description = "部门备注")
    private String remark;
}
