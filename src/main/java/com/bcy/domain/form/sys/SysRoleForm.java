package com.bcy.domain.form.sys;

import com.bcy.domain.form.IdForm;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 角色表单
 *
 * <AUTHOR>
 * @since 2024/09/28
 */
@Schema(description = "角色表单")
@Data
@EqualsAndHashCode(callSuper = true)
public class SysRoleForm extends IdForm {

    @Schema(description = "角色名称")
    @NotBlank(message = "角色名称不能为空")
    private String name;

    @Schema(description = "角色编码 ADMIN-超级管理员 USER-普通用户 SYSTEM-系统管理员 OTHER-特殊权限用户 TEST-测试人员 GUEST-演示客户")
    @NotBlank(message = "角色编码不能为空")
    private String code;

    @Schema(description = "角色备注")
    private String remark;

    @Schema(description = "菜单Id列表")
    private List<Long> menuIds;
}
