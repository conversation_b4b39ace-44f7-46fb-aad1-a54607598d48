package com.bcy.domain.form.sys;

import com.bcy.domain.form.IdForm;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 租户表单
 *
 * <AUTHOR>
 * @since 2024/09/28
 */
@Schema(description = "租户表单")
@Data
@EqualsAndHashCode(callSuper = true)
public class SysTenantForm extends IdForm {
    @Schema(description = "租户名称")
    @NotBlank(message = "租户名称不能为空")
    private String name;

    @Schema(description = "租户标识符")
    @NotBlank(message = "租户标识符不能为空")
    private String appKey;

    @Schema(description = "备注")
    private String remark;
}
