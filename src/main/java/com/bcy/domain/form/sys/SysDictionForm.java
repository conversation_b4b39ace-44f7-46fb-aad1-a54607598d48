package com.bcy.domain.form.sys;

import com.bcy.domain.form.IdForm;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 字典表单
 *
 * <AUTHOR>
 * @since 2020-05-06
 */
@Schema(description = "字典表单")
@Data
@EqualsAndHashCode(callSuper = true)
public class SysDictionForm extends IdForm {

    @Schema(description = "字典名称")
    @NotBlank(message = "字典名称不能为空")
    private String name;

    @Schema(description = "字典key值")
    @NotBlank(message = "字典key值不能为空")
    private String dataCode;
}
