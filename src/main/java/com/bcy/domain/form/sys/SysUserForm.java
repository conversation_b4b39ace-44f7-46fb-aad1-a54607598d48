package com.bcy.domain.form.sys;

import com.bcy.domain.form.IdForm;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 用户表单
 *
 * <AUTHOR>
 * @since 2024/09/28
 */
@Schema(description = "用户表单")
@Data
@EqualsAndHashCode(callSuper = true)
public class SysUserForm extends IdForm {

    @Schema(description = "用户名")
    @NotBlank(message = "用户名不能为空")
    private String userName;

    @Schema(description = "姓名")
    private String nickName;


    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "租户Id列表")
    @NotEmpty(message = "租户Id列表不能为空")
    private List<Long> tenantIds;

    @Schema(description = "部门ID")
    @NotNull(message = "部门Id不能为空")
    private Long deptId;

    @Schema(description = "性别")
    private String sex;

    @Schema(description = "头像路径")
    private String avatar;

    @Schema(description = "邮箱")
    @Email(message = "邮箱格式不正确")
    private String email;

    @Schema(description = "手机号")
    @NotBlank(message = "手机号码不能为空")
    private String phone;

    @Schema(description = "角色Id列表")
    @NotEmpty(message = "角色Id列表不能为空")
    private List<Long> roleIds;
}
