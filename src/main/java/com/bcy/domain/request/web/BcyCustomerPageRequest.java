package com.bcy.domain.request.web;

import com.bcy.domain.request.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 留资客户分页查询表单
 *
 * <AUTHOR>
 * @since 2024-08-21
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "留资客户分页查询表单")
@Data
public class BcyCustomerPageRequest extends PageRequest {
    @Schema(description = "站点ID")
    private Long appId;

    @Schema(description = "广告Id")
    private String adsId;
}