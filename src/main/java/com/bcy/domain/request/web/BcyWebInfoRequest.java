package com.bcy.domain.request.web;

import com.bcy.domain.form.IdForm;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 获取留资信息表单
 *
 * <AUTHOR>
 * @since 2024-08-21
 */
@EqualsAndHashCode(callSuper = true)
@Schema(description = "获取留资信息表单")
@Data
public class BcyWebInfoRequest extends IdForm {

    @Schema(description = "平台类型")
    @NotBlank(message = "平台类型不能为空")
    private String platformType;
}
