package com.bcy.domain.request;


import com.bcy.domain.request.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 消息分页表单
 *
 * <AUTHOR>
 * @since 2023/4/16-13:33
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "消息分页表单")
public class MessagePageRequest extends PageRequest {

    @Schema(description = "是否推送")
    private Integer isPush;

    @Schema(description = "类型（0系统1个人）")
    private Integer msgType;

    @Schema(description = "通知类型")
    private String noticeType;


    @Schema(description = "待办或者系统页面（0系统1待办）")
    private Integer isManage = 0;
}
