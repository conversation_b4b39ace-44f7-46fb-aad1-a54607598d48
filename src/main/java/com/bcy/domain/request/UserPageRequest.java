package com.bcy.domain.request;


import com.bcy.domain.request.PageRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户分页表单
 *
 * <AUTHOR>
 * @since 2023/4/16-13:33
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "用户分页表单")
public class UserPageRequest extends PageRequest {

    @Schema(description = "部门Id")
    private Long deptId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "租户ID")
    private Long tenantId;

}
