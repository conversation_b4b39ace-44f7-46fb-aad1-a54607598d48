package com.bcy.domain.response.msg;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 系统消息信息
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
@Schema(description = "系统消息信息")
@Data
public class SysMessageResponse implements Serializable {
    @Serial
    private static final long serialVersionUID = -3540232835721441759L;

    @Schema(description = "消息Id")
    private Long id;

    @Schema(description = "消息主题")
    private String title;

    @Schema(description = "消息内容")
    private String message;

    @Schema(description = "类型（0系统1个人））")
    private Integer msgType;

    @Schema(description = "消息类型")
    private String type;

    @Schema(description = "是否发送")
    private Integer isPush;

    @Schema(description = "推送时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date pushTime;
}
