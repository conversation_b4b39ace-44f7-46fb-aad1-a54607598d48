package com.bcy.domain.response.sys;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 部门表
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Schema(description = "部门表")
@Data
public class SysDeptResponse implements Serializable {
    @Serial
    private static final long serialVersionUID = -6131068337315902798L;

    @Schema(description = "部门Id")
    private Long id;

    @Schema(description = "上级部门")
    private Long parentId;

    @Schema(description = "部门名称")
    private String name;

    @Schema(description = "部门备注")
    private String remark;
}
