package com.bcy.domain.response.sys;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 用户信息
 *
 * <AUTHOR>
 * @since 2024/09/26
 */
@Schema(description = "用户信息")
@Data
public class SysUserResponse implements Serializable {

    @Serial
    private static final long serialVersionUID = 4558080149589989004L;

    @Schema(description = "用户Id")
    private Long id;

    @Schema(description = "用户名")
    private String userName;

    @Schema(description = "用户昵称")
    private String nickName;

    @Schema(description = "工号")
    private String jobNumber;

    @Schema(description = "是否启用（0是1否）")
    private Integer status;

    @Schema(description = "性别")
    private String sex;

    @Schema(description = "头像路径")
    private String avatar;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "部门Id")
    private Long deptId;

    @Schema(description = "租户列表")
    private List<Long> tenantIds;

    @Schema(description = "角色列表")
    private List<Long> roleIds;
}
