package com.bcy.domain.response.sys;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 租户详情
 *
 * <AUTHOR>
 * @since 2024/09/28
 */
@Schema(description = "租户详情")
@Data
public class SysTenantResponse implements Serializable {

    @Serial
    private static final long serialVersionUID = 8550182463092439749L;

    @Schema(description = "租户Id")
    private Long id;

    @Schema(description = "租户名称")
    private String name;

    @Schema(description = "租户标识符")
    private String appKey;

    @Schema(description = "秘钥")
    private String secret;

    @Schema(description = "备注")
    private String remark;
}
