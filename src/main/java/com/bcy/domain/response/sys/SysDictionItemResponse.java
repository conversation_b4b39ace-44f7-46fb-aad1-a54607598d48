package com.bcy.domain.response.sys;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 字典项信息
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
@Schema(description = "字典项信息")
@Data
public class SysDictionItemResponse implements Serializable {

    @Serial
    private static final long serialVersionUID = -5793175492101506191L;

    @Schema(description = "字典项Id")
    private Long id;

    @Schema(description = "字典id")
    private Long dictionId;

    @Schema(description = "项名称")
    private String name;

    @Schema(description = "编码")
    private String dataCode;

    @Schema(description = "项数值")
    private String value;

    @Schema(description = "排序")
    private Integer sort;

}
