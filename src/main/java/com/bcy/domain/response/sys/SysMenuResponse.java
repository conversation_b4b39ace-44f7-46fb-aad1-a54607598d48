package com.bcy.domain.response.sys;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 菜单信息
 *
 * <AUTHOR>
 * @since 2024/09/26
 */
@Schema(description =  "菜单信息")
@Data
public class SysMenuResponse implements Serializable {

    @Serial
    private static final long serialVersionUID = 1325984771235465054L;

    @Schema(description = "菜单ID")
    private Long id;

    @Schema(description = "父菜单ID，一级菜单为0")
    private Long parentId;

    @Schema(description = "父节点ID路径", example = "0,1")
    private String treePath;

    @Schema(description = "菜单名称")
    private String name;

    @Schema(description = "父级菜单名称")
    private String parentName;

    @Schema(description = "菜单类型(MENU:菜单 CATALOG:目录 BUTTON:按钮 EXTLINK:外链)")
    private String type;

    @Schema(description = "路由路径(浏览器地址栏路径)")
    private String path;

    @Schema(description = "路由名称")
    private String routeName;

    @Schema(description = "模块服务", example = "bcy-sys")
    private String module;

    @Schema(description = "组件路径(vue页面完整路径，省略.vue后缀)")
    private String component;

    @Schema(description = "权限标识")
    private String perm;

    @Schema(description = "显示状态(1-显示;0-隐藏)")
    private Integer visible;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "菜单图标")
    private String icon;

    @Schema(description = "跳转路径")
    private String redirect;

    @Schema(description = "【目录】只有一个子路由是否始终显示(1:是 0:否)")
    private Integer alwaysShow;

    @Schema(description = "【菜单】是否开启页面缓存(1:是 0:否)")
    private Integer keepAlive;

    @Schema(description = "下级列表")
    private List<?> children;
}
