package com.bcy.domain.response.sys;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 角色信息
 *
 * <AUTHOR>
 * @since 2024/09/26
 */
@Schema(description = "角色信息")
@Data
public class SysRoleResponse implements Serializable {

    @Serial
    private static final long serialVersionUID = -746027247859335469L;

    @Schema(description = "角色Id")
    private Long id;

    @ExcelProperty("角色名称")
    @Schema(description = "角色名称")
    private String name;

    @Schema(description = "角色编码 ADMIN-超级管理员 USER-普通用户 SYSTEM-系统管理员 OTHER-特殊权限用户 TEST-测试人员")
    private String code;

    @Schema(description = "角色备注")
    private String remark;
}
