package com.bcy.domain.response.sys;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 模块服务分页信息
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Schema(description = "模块服务信息")
@Data
public class SysModuleResponse implements Serializable {
    @Serial
    private static final long serialVersionUID = -6022069437500939907L;

    @Schema(description = "id")
    private Long id;

    @Schema(description = "模块名称")
    private String name;

    @Schema(description = "模块代码")
    private String code;

    @Schema(description = "路径")
    private String url;

    @Schema(description = "状态（0展示1隐藏）")
    private Integer status;

    @Schema(description = "备注")
    private String remark;
}
