package com.bcy.domain.response.log;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * API日志详情
 *
 * <AUTHOR>
 * @since 2024/09/26
 */
@Schema(description = "API日志详情")
@Data
public class SysApiLogResponse implements Serializable {

    @Serial
    private static final long serialVersionUID = -2943560631761174527L;


    @Schema(description = "请求地址")
    private String url;

    @Schema(description = "请求方法")
    private String method;

    @Schema(description = "请求参数")
    private String params;

    @Schema(description = "请求结果")
    private String result;

    @Schema(description = "错误信息")
    private String errorMsg;
}
