package com.bcy.domain.response.login;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 登录返回值
 *
 * <AUTHOR>
 * @since 2022/09/26
 */
@Schema(description = "登录返回值")
@Data
public class LoginResponse implements Serializable {

    @Serial
    private static final long serialVersionUID = -6560653974545297788L;

    @Schema(description = "token")
    private String accessToken;

    @Schema(description = "过期时间")
    private Long expireTime;
}
