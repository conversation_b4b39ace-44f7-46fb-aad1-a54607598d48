package com.bcy.domain.response.login;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 登录页配置响应
 *
 * <AUTHOR>
 * @since 2024/3/12 22:15
 */
@Schema(description = "登录页配置响应")
@Data
public class SysSettingResponse {
    @Schema(description = "标题")
    private String title = "博昌云科技";

    @Schema(description = "版本")
    private String version = "1.0.0";

    @Schema(description = "是否多租户版本（0否1是）")
    private Integer isTenant = 0;

    @Schema(description = "是否需要验证码（0否1是）")
    private Integer isCode = 0;

    @Schema(description = "版权所有提示")
    private String notice = "Copyright © 2023 - 2024 博昌云科技 All Rights Reserved.";

    @Schema(description = "备案号")
    private String beiAnNumber = "闽ICP备2024063607号-1";

    @Schema(description = "网安备案号")
    private String number = "";

    @Schema(description = "websocket链接")
    private String websocket = "ws://127.0.0.1:8080/sys/websocket";

}
