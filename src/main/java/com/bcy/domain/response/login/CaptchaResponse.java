package com.bcy.domain.response.login;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 验证码返回值
 *
 * <AUTHOR>
 * @since 2022/09/26
 */
@Schema(description = "验证码返回值")
@Data
public class CaptchaResponse implements Serializable {

    @Serial
    private static final long serialVersionUID = 1325984771235465024L;

   @Schema(description = "验证码缓存key")
    private String verifyCodeKey;

   @Schema(description = "验证码图片Base64字符串")
    private String verifyCodeBase64;
}
