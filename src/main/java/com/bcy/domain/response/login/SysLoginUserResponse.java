package com.bcy.domain.response.login;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * 登录用户信息
 *
 * <AUTHOR>
 * @since 2024/09/26
 */
@Schema(description = "登录用户信息")
@Data
public class SysLoginUserResponse implements Serializable {

    @Serial
    private static final long serialVersionUID = 4558080149589989004L;

    @Schema(description = "用户Id")
    private Long id;

    @Schema(description = "用户名")
    private String userName;

    @Schema(description = "用户昵称")
    private String nickName;

    @Schema(description = "工号")
    private String jobNumber;

    @Schema(description = "性别")
    private String sex;

    @Schema(description = "头像路径")
    private String avatar;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "部门名称")
    private String deptName;

    @Schema(description = "租户列表")
    private List<Long> tenantIds;

    @Schema(description = "权限列表")
    private Set<String> perms;

    @Schema(description = "角色列表")
    private Set<String> roles;

}
