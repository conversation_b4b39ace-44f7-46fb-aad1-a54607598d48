package com.bcy.domain.response.route;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 路由响应
 *
 * <AUTHOR>
 * @since 2024/3/12 22:15
 */
@Schema(description =  "路由信息")
@Data
public class SysRoutsResponse implements Serializable {
    @Schema(description = "菜单ID")
    private Long id;

    @Serial
    private static final long serialVersionUID = -6318639618922277392L;

    @Schema(description = "路由路径", example = "user")
    private String path;

    @Schema(description = "组件路径", example = "system/user/index")
    private String component;

    @Schema(description = "跳转链接", example = "https://www.youlai.tech")
    private String redirect;

    @Schema(description = "父级菜单ID")
    private Long parentId;

    @Schema(description = "路由名称")
    private String name;

    @Schema(description = "路由属性")
    private Meta meta;

    @Schema(description = "路由属性类型")
    @Data
    public static class Meta {

        @Schema(description = "路由title")
        private String title;

        @Schema(description = "ICON")
        private String icon;

        @Schema(description = "是否隐藏(true-是 false-否)", example = "true")
        private Boolean hidden;

        @Schema(description = "【菜单】是否开启页面缓存", example = "true")
        private Boolean keepAlive;

        @Schema(description = "【目录】只有一个子路由是否始终显示", example = "true")
        private Boolean alwaysShow;
    }

    @Schema(description = "子路由列表")
    private List<SysRoutsResponse> children;
}
