package com.bcy.domain.response.page.sys;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 部门表
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Schema(description = "部门表")
@Data
public class SysDeptPageResponse implements Serializable {
    @Serial
    private static final long serialVersionUID = -6131068337315902798L;

    @Schema(description = "部门Id")
    private Long id;

    @Schema(description = "父级ID")
    private Long parentId;

    @Schema(description = "部门名称")
    private String name;

    @Schema(description = "部门备注")
    private String remark;

    @Schema(description = "下级部门列表")
    private List<SysDeptPageResponse> children;


}
