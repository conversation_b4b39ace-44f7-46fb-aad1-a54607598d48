package com.bcy.domain.response.page.sys;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 模块服务分页信息
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Schema(description = "模块服务分页信息")
@Data
public class SysModulePageResponse implements Serializable {
    @Serial
    private static final long serialVersionUID = 2210169041580621688L;

    @Schema(description = "id")
    private Long id;

    @Schema(description = "模块名称")
    private String name;

    @Schema(description = "模块代码")
    private String code;

    @Schema(description = "路径")
    private String url;

    @Schema(description = "状态（0展示1隐藏）")
    private Integer status;

    @Schema(description = "备注")
    private String remark;
}
