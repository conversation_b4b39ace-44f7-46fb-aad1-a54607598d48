package com.bcy.domain.response.page.pet;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 用户宠物信息表单
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
@Schema(description = "用户宠物信息表单")
@Data
public class BcyPetInfoPageResponse implements Serializable {

    @Serial
    private static final long serialVersionUID = 6020165342981735177L;

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "所属用户")
    private String userName;

    @Schema(description = "分类名称")
    private String categoryName;

    @Schema(description = "宠物名称")
    private String name;

    @Schema(description = "简介")
    private String shortDescription;

    @Schema(description = "图片")
    private String image;

    @Schema(description = "性别")
    private String sex;

    @Schema(description = "出生日期")
    private Date birthday;

    @Schema(description = "是否默认(0是1否)")
    private Integer isDefault;
}