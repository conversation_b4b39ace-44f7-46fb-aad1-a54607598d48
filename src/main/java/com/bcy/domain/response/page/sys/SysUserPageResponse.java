package com.bcy.domain.response.page.sys;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 用户分页信息
 *
 * <AUTHOR>
 * @since 2024/09/26
 */
@Schema(description = "用户分页信息")
@Data
public class SysUserPageResponse implements Serializable {

    @Serial
    private static final long serialVersionUID = 4558080149589989004L;

    @Schema(description = "用户Id")
    @ExcelIgnore
    private Long id;

    @Schema(description = "头像路径")
    @ExcelProperty("头像路径")
    private String avatar;

    @Schema(description = "用户名")
    @ExcelProperty("用户名")
    private String userName;

    @Schema(description = "用户昵称")
    @ExcelProperty("用户昵称")
    private String nickName;

    @Schema(description = "工号")
    @ExcelProperty("工号")
    private String jobNumber;

    @Schema(description = "性别")
    @ExcelProperty("性别")
    private String sexName;

    @Schema(description = "性别")
    @ExcelIgnore
    private String sex;

    @Schema(description = "部门名称")
    @ExcelProperty("部门名称")
    private String deptName;

    @Schema(description = "手机号")
    @ExcelProperty("手机号")
    private String phone;

    @Schema(description = "邮箱")
    @ExcelProperty("邮箱")
    private String email;

    @Schema(description = "是否启用（0是1否）")
    private Integer status;

    @Schema(description = "状态")
    @ExcelProperty("状态")
    private String statusName;

    @Schema(description = "最后登录时间")
    @ExcelProperty("最后登录时间")
    private Date loginTime;

    @Schema(description = "创建人")
    @ExcelProperty("创建人")
    private String createUser;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private Date createTime;
}
