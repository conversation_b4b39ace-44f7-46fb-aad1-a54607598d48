package com.bcy.domain.response.page.web;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 留资客户分页响应
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
@Schema(description = "留资客户分页响应")
@Data
public class BcyCustomerPageResponse implements Serializable {

    @Serial
    private static final long serialVersionUID = 3855968836163234849L;

    @Schema(description = "客户ID")
    @ExcelIgnore
    private Long id;

    @Schema(description = "客户名称")
    @ExcelProperty("客户名称")
    private String name;

    @Schema(description = "联系电话")
    @ExcelProperty("联系电话")
    private String phone;

    @Schema(description = "客户需求")
    @ExcelProperty("客户需求")
    private String remark;

    @Schema(description = "浏览路径")
    @ExcelIgnore
    private String url;

    @Schema(description = "广告ID")
    @ExcelProperty("广告ID")
    private String adsId;

    @Schema(description = "线索来源")
    @ExcelProperty("线索来源")
    private String platformTypeName;

    @Schema(description = "客户需求")
    @ExcelProperty("客户需求")
    private String demand;

    @Schema(description = "是否联系（0否1是）")
    @ExcelIgnore
    private Integer status;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private Date createTime;
}
