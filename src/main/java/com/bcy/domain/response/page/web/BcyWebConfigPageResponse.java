package com.bcy.domain.response.page.web;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 留资站点配置分页响应
 *
 * <AUTHOR>
 * @since 2024-08-21
 */
@Schema(description = "留资站点配置分页响应")
@Data
public class BcyWebConfigPageResponse implements Serializable {
    @Serial
    private static final long serialVersionUID = -2606212401735089937L;

    @Schema(description = "页面配置ID")
    private Long id;

    @Schema(description = "站点名称")
    private String siteName;

    @Schema(description = "ico图标")
    private String icon;

    @Schema(description = "站点版权信息")
    private String siteCopyright;

    @Schema(description = "状态（0启用1禁用）")
    private Integer status;

    @Schema(description = "通知人")
    private String siteLinkPhone;

    @Schema(description = "线索来源")
    private String platformType;

    @Schema(description = "通知类型")
    private String noticeType;

    @Schema(description = "页面链接地址")
    private String siteUrl;
}