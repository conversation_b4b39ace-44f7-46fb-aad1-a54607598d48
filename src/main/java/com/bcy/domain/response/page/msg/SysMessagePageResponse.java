package com.bcy.domain.response.page.msg;

import cn.hutool.core.date.DatePattern;
import com.bcy.domain.response.BaseResponse;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 系统消息分页信息
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Schema(description = "系统消息分页信息")
@Data
@EqualsAndHashCode(callSuper = true)
public class SysMessagePageResponse extends BaseResponse implements Serializable {

    @Serial
    private static final long serialVersionUID = 3691923023923597302L;

    @Schema(description = "消息Id")
    private Long id;

    @Schema(description = "消息主题")
    private String title;

    @Schema(description = "通知类型")
    private String noticeType;

    @Schema(description = "消息类型")
    private String type;

    @Schema(description = "类型(0系统1个人)")
    private Integer msgType;

    @Schema(description = "是否发送")
    private Integer isPush;

    @Schema(description = "推送时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date pushTime;

    @Schema(description = "是否已读")
    private Integer isRead;
}
