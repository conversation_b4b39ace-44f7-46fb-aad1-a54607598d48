package com.bcy.domain.response.page.pet;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 宠物分类详情
 *
 * <AUTHOR>
 * @since 2024-08-21
 */
@Schema(description = "宠物分类详情")
@Data
public class BcyPetCategoryPageDetailResponse implements Serializable {

    @Serial
    private static final long serialVersionUID = -5947592171381733727L;

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "分类ID")
    private Long categoryId;

    @Schema(description = "详情")
    private String description;

    @Schema(description = "标签")
    private String tags;

}