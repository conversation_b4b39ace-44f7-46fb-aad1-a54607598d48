package com.bcy.domain.response.page.pet;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 科普文章表单
 *
 * <AUTHOR>
 * @since 2024-08-21
 */
@Schema(description = "宠物科普文章表单")
@Data
public class BcyPetContentPageResponse implements Serializable {

    @Serial
    private static final long serialVersionUID = 6307707244995718486L;

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "分类名称")
    private String categoryName;

    @Schema(description = "文章标题")
    private String title;

    @Schema(description = "文章缩略图")
    private String image;

    @Schema(description = "是否显示0显示 1不显示")
    private Integer status;

    @Schema(description = "文章类型")
    private String type;

    @Schema(description = "发布时间")
    private Date pushTime;

    @Schema(description = "自定义顺序")
    private Integer sort;

    @Schema(description = "文章标签")
    private String tags;
}