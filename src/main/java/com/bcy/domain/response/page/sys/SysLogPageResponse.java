package com.bcy.domain.response.page.sys;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 系统日志分页表单响应
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Schema(description = "系统日志分页表单响应")
@Data
public class SysLogPageResponse implements Serializable {

    @Serial
    private static final long serialVersionUID = 5663771848044044143L;

    @Schema(description = "操作用户")
    @ExcelProperty("操作用户")
    private String userName;

    @Schema(description = "操作名称")
    @ExcelProperty("操作名称")
    private String operation;

    @Schema(description = "操作模块")
    @ExcelProperty("操作模块")
    private String model;

    @Schema(description = "请求方法")
    @ExcelProperty("请求方法")
    private String method;

    @Schema(description = "请求参数")
    @ExcelProperty("请求参数")
    private String params;

    @Schema(description = "请求结果")
    @ExcelProperty("请求结果")
    private String result;

    @Schema(description = "请求耗时")
    @ExcelProperty("请求耗时")
    private Long time;

    @Schema(description = "请求IP")
    @ExcelProperty("请求IP")
    private String ip;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private Date createDate;
}
