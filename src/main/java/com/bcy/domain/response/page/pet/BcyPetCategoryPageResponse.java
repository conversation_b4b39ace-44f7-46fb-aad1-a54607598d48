package com.bcy.domain.response.page.pet;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 宠物分类列表
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
@Schema(description = "宠物分类列表")
@Data
public class BcyPetCategoryPageResponse implements Serializable {
    @Serial
    private static final long serialVersionUID = -4500279517196734891L;

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "分类名称")
    private String name;

    @Schema(description = "分类图标")
    private String ico;

    @Schema(description = "是否显示（0是1否）")
    private Integer status;

    @Schema(description = "自定义顺序")
    private Integer sort;
}
