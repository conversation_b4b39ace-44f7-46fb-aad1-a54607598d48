package com.bcy.domain.response.page.sys;

import com.bcy.domain.response.BaseResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 文件日志分页响应
 *
 * <AUTHOR>
 * @since 2023/8/24-22:27
 */
@Schema(description = "文件日志分页响应")
@Data
@EqualsAndHashCode(callSuper = true)
public class SysFileLogPageResponse extends BaseResponse implements Serializable {

    @Serial
    private static final long serialVersionUID = 3500158137892023857L;

    @Schema(description = "文件日志ID")
    private Long id;

    @Schema(description = "文件路径")
    private String fileUrl;

    @Schema(description = "文件名称")
    private String fileName;

    @Schema(description = "文件路径")
    private String filePath;

    @Schema(description = "文件类型")
    private String fileType;

    @Schema(description = "源文件名称")
    private String sourceFileName;

    @Schema(description = "状态（0未下载1已下载）")
    private Integer status;

    @Schema(description = "状态（0未下载1已下载）")
    private String statusName;

    @Schema(description = "失败原因")
    private String errMsg;

    @Schema(description = "执行时长(毫秒)")
    private Long time;

    @Schema(description = "创建时间")
    private Date exportTime;
}
