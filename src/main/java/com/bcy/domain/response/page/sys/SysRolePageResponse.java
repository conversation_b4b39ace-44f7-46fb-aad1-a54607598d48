package com.bcy.domain.response.page.sys;

import com.bcy.domain.response.BaseResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;

/**
 * 角色信息
 *
 * <AUTHOR>
 * @since 2024/09/26
 */
@Schema(description = "角色信息")
@Data
@EqualsAndHashCode(callSuper = true)
public class SysRolePageResponse extends BaseResponse implements Serializable {

    @Serial
    private static final long serialVersionUID = -746027247859335469L;

    @Schema(description = "角色Id")
    private Long id;

    @Schema(description = "角色名称")
    private String name;

    @Schema(description = "角色编码 ADMIN-超级管理员 USER-普通用户 SYSTEM-系统管理员 OTHER-特殊权限用户 TEST-测试人员")
    private String code;

    @Schema(description = "角色编码 ADMIN-超级管理员 USER-普通用户 SYSTEM-系统管理员 OTHER-特殊权限用户 TEST-测试人员")
    private String codeName;

    @Schema(description = "角色备注")
    private String remark;
}
