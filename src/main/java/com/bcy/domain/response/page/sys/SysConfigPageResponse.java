package com.bcy.domain.response.page.sys;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 系统配置分页信息
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Schema(description = "系统配置分页信息")
@Data
public class SysConfigPageResponse implements Serializable {

    @Serial
    private static final long serialVersionUID = 2536331136013822024L;

    @Schema(description = "配置Id")
    private Long id;

    @Schema(description = "配置名称")
    private String name;

    @Schema(description = "配置标识符")
    private String paramsKey;

    @Schema(description = "配置详情")
    private String paramsValue;

    @Schema(description = "是否启用（0是1否）")
    private Integer status;

    @Schema(description = "是否启用（0是1否）")
    private String statusName;


    @Schema(description = "备注")
    private String remark;
}
