package com.bcy.domain.response.page.sys;

import com.bcy.domain.response.BaseResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;

/**
 * 租户分页列表响应
 *
 * <AUTHOR>
 * @since 2024/09/28
 */
@Schema(description = "租户分页列表响应")
@Data
@EqualsAndHashCode(callSuper = true)
public class SysTenantPageResponse extends BaseResponse implements Serializable {

    @Serial
    private static final long serialVersionUID = 8550182463092439749L;

    @Schema(description = "租户Id")
    private Long id;

    @Schema(description = "租户名称")
    private String name;

    @Schema(description = "租户标识符")
    private String appKey;

    @Schema(description = "备注")
    private String remark;
}
