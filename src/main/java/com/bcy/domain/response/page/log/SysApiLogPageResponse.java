package com.bcy.domain.response.page.log;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * API日志分页表单响应
 *
 * <AUTHOR>
 * @since 2022/09/26
 */
@Schema(description = "系统日志分页表单响应")
@Data
public class SysApiLogPageResponse implements Serializable {

    @Serial
    private static final long serialVersionUID = -2943560631761174527L;

    @Schema(description = "API日志Id")
    private Long id;

    @Schema(description = "请求方式")
    private String method;

    @Schema(description = "操作名称")
    private String operatorName;

    @Schema(description = "模块")
    private String model;

    @Schema(description = "请求地址")
    private String url;

    @Schema(description = "操作状态(0:成功,1:失败)")
    private Integer status;

    @Schema(description = "执行时长(毫秒)")
    private Long time;

    @Schema(description = "IP地址")
    private String ip;

    @Schema(description = "请求IP地址")
    private String ipAddress;

    @Schema(description = "创建时间")
    private Date createTime;
}
