package com.bcy.domain.response.page.sys;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 字典分页列表响应
 *
 * <AUTHOR>
 * @since 2023-12-25
 */
@Schema(description = "字典分页列表响应")
@Data
public class SysDictionPageResponse implements Serializable {

    @Serial
    private static final long serialVersionUID = -8738650950771153988L;

    @Schema(description = "字典Id")
    private Long id;

    @Schema(description = "字典名称")
    private String name;

    @Schema(description = "字典key")
    private String dataCode;
}
