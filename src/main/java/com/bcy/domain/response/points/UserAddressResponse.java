package com.bcy.domain.response.points;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 用户收货地址响应实体
 */
@Schema(description = "用户收货地址")
@Data
public class UserAddressResponse {
    @Schema(description = "主键")
    private Long id;
    @Schema(description = "用户ID")
    private Long userId;
    @Schema(description = "用户名称")
    private String userName;
    @Schema(description = "收件人姓名")
    private String receiverName;
    @Schema(description = "收件人手机号")
    private String receiverPhone;
    @Schema(description = "省")
    private String province;
    @Schema(description = "市")
    private String city;
    @Schema(description = "区/县")
    private String district;
    @Schema(description = "详细地址")
    private String addressDetail;
    @Schema(description = "是否默认地址（1是0否）")
    private Integer isDefault;
} 