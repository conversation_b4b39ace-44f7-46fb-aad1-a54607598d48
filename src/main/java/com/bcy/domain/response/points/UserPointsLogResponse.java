package com.bcy.domain.response.points;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 用户积分响应
 */
@Schema(description = "用户积分响应")
@Data
public class UserPointsLogResponse {
    @Schema(description = "主键")
    private Long id;
    @Schema(description = "变动类型（1签到、2任务等）")
    private Integer changeType;
    @Schema(description = "变动积分（正/负）")
    private Integer changeValue;
    @Schema(description = "变动前积分")
    private Integer beforePoints;
    @Schema(description = "变动后积分")
    private Integer afterPoints;
    @Schema(description = "关联业务ID（如任务ID）")
    private Long relatedId;
    @Schema(description = "关联业务名称")
    private String relatedName;
    @Schema(description = "备注")
    private String remark;
} 