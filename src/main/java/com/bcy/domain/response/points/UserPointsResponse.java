package com.bcy.domain.response.points;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 用户积分响应
 */
@Schema(description = "用户积分")
@Data
public class UserPointsResponse {
    @Schema(description = "主键")
    private Long id;
    @Schema(description = "用户ID")
    private Long userId;
    @Schema(description = "用户名称")
    private String userName;
    @Schema(description = "当前可用积分")
    private Integer totalPoints;
    @Schema(description = "冻结积分")
    private Integer frozenPoints;
    @Schema(description = "已过期积分")
    private Integer expiredPoints;
} 