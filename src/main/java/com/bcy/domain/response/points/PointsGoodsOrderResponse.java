package com.bcy.domain.response.points;

import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

@Data
public class PointsGoodsOrderResponse {
    @Schema(description = "主键")
    private Long id;
    @Schema(description = "用户ID")
    private Long userId;
    @Schema(description = "商品ID")
    private Long goodsId;
    @Schema(description = "兑换数量")
    private Integer quantity;
    @Schema(description = "消耗积分")
    private Integer totalPoints;
    @Schema(description = "状态（0待确认1待发货）")
    private Integer status;
    @Schema(description = "收货地址")
    private Long addressId;
} 