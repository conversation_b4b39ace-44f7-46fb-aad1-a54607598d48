package com.bcy.domain.response.points;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 积分商品响应
 */
@Schema(description = "积分商品响应")
@Data
public class PointsGoodsResponse {
    @Schema(description = "主键")
    private Long id;
    @Schema(description = "商品名称")
    private String name;
    @Schema(description = "商品描述")
    private String description;
    @Schema(description = "所需积分")
    private Integer pointsCost;
    @Schema(description = "库存")
    private Integer stock;
    @Schema(description = "每人限兑数量")
    private Integer limitPerUser;
    @Schema(description = "已兑换数量")
    private Integer isUseNumber;
    @Schema(description = "是否上架(0否1是)")
    private Integer isActive;
} 