package com.bcy.domain.response.common;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 短信配置
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
@Schema(description = "短信配置")
@Data
public class SmsConfigResponse implements Serializable {
    @Serial
    private static final long serialVersionUID = 425523260171914022L;

    @Schema(description = "短信签名")
    private String signName;

    @Schema(description = "短信模板code")
    private String templateCode;

    @Schema(description = "腾讯云sdkAPPId")
    private String sdkAppId;
}
