package com.bcy.domain.response.pet;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 用户宠物信息表单
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
@Schema(description = "用户宠物信息表单")
@Data
public class BcyPetInfoResponse implements Serializable {

    @Serial
    private static final long serialVersionUID = 7553267247450470821L;

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "分类ID")
    private Long categoryId;

    @Schema(description = "所属用户")
    private String userName;

    @Schema(description = "宠物名称")
    private String name;

    @Schema(description = "简介")
    private String shortDescription;

    @Schema(description = "图片")
    private String image;

    @Schema(description = "性别")
    private String sex;

    @Schema(description = "出生日期")
    private Date birthday;

    @Schema(description = "是否默认(0是1否)")
    private Integer isDefault;
}