package com.bcy.domain.response.pet;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 宠物分类详情
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
@Schema(description = "宠物分类详情")
@Data
public class BcyPetCategoryResponse implements Serializable {

    @Serial
    private static final long serialVersionUID = 5302247563681699661L;

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "父级ID")
    private Long parentId;

    @Schema(description = "分类名称")
    private String name;

    @Schema(description = "banner图")
    private String images;

    @Schema(description = "分类图标")
    private String ico;

    @Schema(description = "是否显示（0是1否）")
    private Integer status;
    
    @Schema(description = "简介")
    private String keyword;

    @Schema(description = "自定义顺序")
    private Integer sort;

    @Schema(description = "详情列表")
    private List<BcyPetCategoryDetailResponse> detailResponseList;
}
