package com.bcy.domain.response.web;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 留资客户信息
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
@Schema(description = "留资客户信息")
@Data
public class BcyCustomerResponse implements Serializable {
    @Serial
    private static final long serialVersionUID = 4767934386848568191L;

    @Schema(description = "客户ID")
    private Long id;

    @Schema(description = "客户名称")
    private String name;

    @Schema(description = "联系电话")
    private String phone;

    @Schema(description = "客户需求")
    private String demand;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "访问全路径")
    private String url;

    @Schema(description = "来源平台")
    private String platformType;

    @Schema(description = "广告ID")
    private String adsId;

    @Schema(description = "站点ID")
    private Long siteId;

    @Schema(description = "是否联系（0否1是）")
    private Integer status;

    @Schema(description = "创建时间")
    private Date createTime;
}
