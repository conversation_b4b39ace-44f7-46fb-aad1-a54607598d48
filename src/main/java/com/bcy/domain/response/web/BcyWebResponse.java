package com.bcy.domain.response.web;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 留资站点配置分页响应
 *
 * <AUTHOR>
 * @since 2024-08-21
 */
@Schema(description = "留资站点配置分页响应")
@Data
public class BcyWebResponse implements Serializable {
    @Serial
    private static final long serialVersionUID = -2606212401735089937L;

    @Schema(description = "页面配置ID")
    private Long id;

    @Schema(description = "站点名称")
    private String siteName;

    @Schema(description = "ico图标")
    private String icon;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "上部分图片")
    private String headImage;

    @Schema(description = "下部分图片")
    private String bottomImage;

    @Schema(description = "站点版权信息")
    private String siteCopyright;

    @Schema(description = "通知人联系信息")
    private String siteLinkPhone;

    @Schema(description = "线索来源")
    private String platformType;

    @Schema(description = "按钮标题")
    private String buttonTitle;

    @Schema(description = "客服链接")
    private String linkUrl;

    @Schema(description = "通知内容")
    private String noticeMsg;

    @Schema(description = "微信通知模板Id")
    private String wxTemplateId;

    @Schema(description = "通知类型")
    private String noticeType;

    @Schema(description = "状态（0启用1禁用）")
    private Integer status;

    @Schema(description = "悬浮窗标题")
    private String title;

    @Schema(description = "悬浮窗副标题")
    private String subTitle;

    @Schema(description = "下部分图片")
    private List<String> bottomImageList;
}