package com.bcy.domain.dto.diction;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 字典信息
 *
 * <AUTHOR>
 * @since 2023-12-25
 */
@Schema(description = "字典信息")
@Data
public class DictionDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -8738650950771153988L;

    @Schema(description = "字典名称")
    private String name;

    @Schema(description = "字典key")
    private String dataCode;

    @Schema(description = "字典key")
    private List<DictionItemDTO> itemList;
}
