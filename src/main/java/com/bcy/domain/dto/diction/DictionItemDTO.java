package com.bcy.domain.dto.diction;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 字典详情
 *
 * <AUTHOR>
 * @since 2023-12-25
 */
@Schema(description = "字典详情")
@Data
public class DictionItemDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = -8738650950771153988L;

    @Schema(description = "字典值")
    private String value;

    @Schema(description = "字典名称")
    private String label;
}
