package com.bcy.utils;

public class StringFormatUtils {
    /**
     * 将A-B-C格式的字符串变成A-BC
     * @param str 输入字符串
     * @return 合并后的字符串
     */
    public static String mergeLastTwoParts(String str) {
        if (str == null || !str.contains("-")) {
            return str;
        }
        int lastDash = str.lastIndexOf('-');
        if (lastDash == -1) {
            return str;
        }
        String before = str.substring(0, lastDash);
        String after = str.substring(lastDash + 1);
        int secondLastDash = before.lastIndexOf('-');
        if (secondLastDash == -1) {
            // 只有一个-
            return before + after;
        }
        String first = before.substring(0, secondLastDash);
        String middle = before.substring(secondLastDash + 1);
        return first.isEmpty() ? middle + after : first + "-" + middle + after;
    }
} 