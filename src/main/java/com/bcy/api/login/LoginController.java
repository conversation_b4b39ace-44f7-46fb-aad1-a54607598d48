package com.bcy.api.login;

import cn.dev33.satoken.stp.StpUtil;
import com.bcy.annotation.BcyLog;
import com.bcy.annotation.BcyNoRepeat;
import com.bcy.business.login.CodeService;
import com.bcy.domain.response.login.CaptchaResponse;
import com.bcy.domain.response.login.LoginResponse;
import com.bcy.domain.response.login.SysSettingResponse;
import com.bcy.business.login.LoginService;
import com.bcy.business.sys.BcySysConfigService;
import com.bcy.domain.R;
import com.bcy.domain.form.login.LoginForm;
import com.bcy.domain.form.user.ForgetPwdForm;
import com.bcy.domain.form.user.RegisterForm;
import com.bcy.domain.request.mini.WechatMiniProgramLoginRequest;
import com.bcy.utils.SecurityContextUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

/**
 * 登录控制器
 *
 * <AUTHOR>
 * @since 2023-12-14
 */
@Tag(name = "登录控制器")
@RestController
@RequestMapping("login/")
public class LoginController {

    @Resource
    private LoginService loginService;

    @Resource
    private CodeService codeService;

    @Resource
    private BcySysConfigService bcySysConfigService;

    /**
     * 获取相关配置信息
     */
    @Operation(summary = "相关配置信息")
    @GetMapping("config/setting")
    public R<SysSettingResponse> setting() {
        return R.ok(bcySysConfigService.configSetting());
    }

    @GetMapping("captcha.jpg")
    @Operation(summary = "获取验证码")
    public R<CaptchaResponse> captcha(@RequestParam("uuid") String uuid) {
        return R.ok(codeService.getCaptcha(uuid));
    }

    @BcyNoRepeat
    @BcyLog(businessType = "用户登陆")
    @Operation(summary = "用户登录")
    @Parameter(name = "loginForm", description = "登录表单", required = true)
    @PostMapping("sys")
    public R<LoginResponse> login(@RequestBody @Validated LoginForm loginForm) {
        return R.ok(loginService.login(loginForm));
    }

    @BcyLog(businessType = "用户注销")
    @Operation(summary = "用户注销")
    @GetMapping("logout")
    public R<String> logout() {
        StpUtil.logout();
        SecurityContextUtils.setUserId(StringUtils.EMPTY);
        SecurityContextUtils.setUserName(StringUtils.EMPTY);
        SecurityContextUtils.setUserTenant(BigDecimal.ONE.longValue());
        return R.ok("退出成功");
    }

    @Operation(summary = "微信小程序登陆")
    @GetMapping(value = "wx")
    public R<LoginResponse> wxMiniLogin(WechatMiniProgramLoginRequest request) {
        return R.ok(loginService.wxLogin(request));
    }

    @BcyNoRepeat
    @BcyLog(businessType = "忘记密码")
    @Operation(summary = "忘记密码")
    @Parameter(name = "forgetPwdForm", description = "忘记密码表单", required = true)
    @PostMapping("forgetPwd")
    public R<String> forgetPwd(@RequestBody @Validated ForgetPwdForm forgetPwdForm) {
        boolean flag = loginService.forgetPwd(forgetPwdForm);
        return flag ? R.ok("更改成功！") : R.fail("更改失败！");
    }

    @BcyNoRepeat
    @BcyLog(businessType = "用户注册")
    @Operation(summary = "用户注册")
    @Parameter(name = "registerForm", description = "用户注册表单", required = true)
    @PostMapping("register")
    public R<String> register(@RequestBody @Validated RegisterForm registerForm) {
        boolean flag = loginService.register(registerForm);
        return flag ? R.ok("注册成功！") : R.fail("注册失败！");
    }
}
