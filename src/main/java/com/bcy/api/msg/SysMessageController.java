package com.bcy.api.msg;

import com.bcy.annotation.BcyNoRepeat;
import com.bcy.business.msg.BcySysMessageService;
import com.bcy.domain.form.msg.SysMessageForm;
import com.bcy.domain.response.msg.SysMessageResponse;
import com.bcy.domain.R;
import com.bcy.domain.form.IdsForm;
import com.bcy.domain.group.opration.UpdateGroup;
import com.bcy.domain.request.MessagePageRequest;
import com.bcy.enums.DateEnum;
import com.bcy.utils.PageUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 消息服务控制器
 *
 * <AUTHOR>
 * @since 2023-12-14
 */
@Tag(name = "消息服务控制器")
@RestController
@RequestMapping("msg/")
public class SysMessageController {
    @Resource
    private BcySysMessageService bcySysMessageService;

    @Operation(summary = "分页获取系统消息列表")
    @Parameter(name = "pageRequest", description = "分页表单", required = true)
    @PostMapping("list")
    public R<PageUtils> list(@RequestBody @Validated MessagePageRequest pageRequest) {
        return R.ok(bcySysMessageService.queryPage(pageRequest));
    }

    @Operation(summary = "根据Id获取消息详情")
    @Parameters({
            @Parameter(name = "id", description = "id", required = true),
            @Parameter(name = "type", description = "类型（0管理页面1待办页面）", required = true)
    })
    @GetMapping("info")
    public R<SysMessageResponse> info(@RequestParam("id") Long id, @RequestParam("type") Integer type) {
        return R.ok(bcySysMessageService.getInfoById(id, type));
    }

    @BcyNoRepeat(value = DateEnum.SECONDS)
    @Operation(summary = "新增消息信息")
    @Parameter(name = "messageForm", description = "消息表单", required = true)
    @PostMapping("insert")
    public R<String> insert(@RequestBody @Validated SysMessageForm messageForm) {
        boolean flag = bcySysMessageService.saveMessage(messageForm);
        return flag ? R.ok("新增成功") : R.fail("新增失败");
    }

    @Operation(summary = "修改消息信息")
    @Parameter(name = "messageForm", description = "消息表单", required = true)
    @PostMapping("update")
    public R<String> update(@RequestBody @Validated(UpdateGroup.class) SysMessageForm messageForm) {
        boolean flag = bcySysMessageService.updateMessage(messageForm);
        return flag ? R.ok("更新成功") : R.fail("更新失败");
    }

    @Operation(summary = "批量删除消息")
    @Parameter(name = "idsForm", description = "id数组表单", required = true)
    @PostMapping("delete")
    public R<String> delete(@RequestBody @Validated IdsForm idsForm) {
        boolean flag = bcySysMessageService.deleteMessages(idsForm);
        return flag ? R.ok("删除成功") : R.fail("删除失败");
    }

    @Operation(summary = "批量发送消息")
    @Parameter(name = "idsForm", description = "id数组表单", required = true)
    @PostMapping("pushMessages")
    public R<String> pushMessages(@RequestBody @Validated IdsForm idsForm) {
        bcySysMessageService.pushMessages(idsForm);
        return R.ok("发送成功");
    }

    @Operation(summary = "一键清空已读消息（用户）")
    @GetMapping("clearReadMsg")
    public R<String> clearReadMsg() {
        boolean result = bcySysMessageService.clearReadMsg();
        return result ? R.ok("清除成功") : R.fail("清除失败");
    }

    @Operation(summary = "一键已读消息（用户）")
    @GetMapping("readAllMsg")
    public R<String> readAllMsg() {
        boolean result = bcySysMessageService.readAll();
        return result ? R.ok("操作成功") : R.fail("操作失败");
    }

    @Operation(summary = "批量删除消息（用户）")
    @Parameter(name = "idsForm", description = "id数组表单", required = true)
    @PostMapping("deleteMessages")
    public R<String> deleteMessages(@RequestBody @Validated IdsForm idsForm) {
        boolean result = bcySysMessageService.deleteUserMessages(idsForm);
        return result ? R.ok("删除成功") : R.fail("删除失败");
    }

    @Operation(summary = "获取用户未读信息（用户）")
    @GetMapping("unReadList")
    public R<List<SysMessageResponse>> unReadList() {
        return R.ok(bcySysMessageService.unReadList());
    }

}
