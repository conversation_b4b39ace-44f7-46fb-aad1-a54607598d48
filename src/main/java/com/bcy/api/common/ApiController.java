package com.bcy.api.common;

import com.bcy.business.common.BcyMsgService;
import com.bcy.business.web.BcyCustomerService;
import com.bcy.domain.R;
import com.bcy.domain.form.user.RegisterCodeForm;
import com.bcy.domain.form.user.UserSimpleMailForm;
import com.bcy.domain.form.user.UserSmsForm;
import com.bcy.service.wechat.WechatBusinessService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 相关接口服务
 *
 * <AUTHOR>
 * @since 2023/3/27-21:38
 */
@Tag(name = "相关接口服务", description = "相关接口服务")
@RestController
@RequestMapping("api/")
public class ApiController {

    @Resource
    private WechatBusinessService wechatBusinessService;

    @Resource
    private BcyMsgService bcyMsgService;
    @Resource
    private BcyCustomerService bcyCustomerService;

    @Operation(summary = "微信token验证")
    @GetMapping(value = "wx", produces = "text/plain;charset=utf-8")
    public String wx(HttpServletRequest request) {
        return wechatBusinessService.checkSignature(request);
    }

    @Operation(summary = "发送短信")
    @Parameter(name = "smsForm", description = "短信表单", required = true)
    @PostMapping("sendUserSms")
    public R<String> sendUserSms(@RequestBody @Validated UserSmsForm smsForm) {
        bcyMsgService.sendUserSms(smsForm);
        return R.ok("发送成功");
    }

    @Operation(summary = "发送邮件")
    @Parameter(name = "userSimpleMailForm", description = "邮件表单", required = true)
    @PostMapping("sendUserMail")
    public R<String> sendUserMail(@RequestBody @Validated UserSimpleMailForm userSimpleMailForm) {
        bcyMsgService.sendUserMail(userSimpleMailForm);
        return R.ok("发送成功");
    }

    @Operation(summary = "发送注册验证码")
    @Parameter(name = "registerCodeForm", description = "注册码表单", required = true)
    @PostMapping("registerMsg")
    public R<String> registerMsg(@RequestBody @Validated RegisterCodeForm registerCodeForm) {
        bcyMsgService.registerMsg(registerCodeForm);
        return R.ok("发送成功");
    }

}
