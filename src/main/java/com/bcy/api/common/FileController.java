package com.bcy.api.common;

import com.bcy.business.common.BcyFileService;
import com.bcy.domain.R;
import com.bcy.domain.response.OssResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文件相关操作
 *
 * <AUTHOR>
 * @since 2023/3/26-14:03
 */
@Tag(name = "文件相关操作", description = "文件相关操作")
@RestController
public class FileController {
    @Resource
    private BcyFileService bcyFileService;

    @Operation(summary = "文件上传")
    @PostMapping("upload")
    @Parameters({@Parameter(name = "file", description = "文件", required = true), @Parameter(name = "type", description = "类型", required = true)})
    public R<OssResponse> upload(@RequestParam("file") MultipartFile file, @RequestParam("type") String type) {
        OssResponse response = bcyFileService.uploadFile(file, type);
        return R.ok(response);
    }


    @Operation(summary = "文件上传到指定路径")
    @PostMapping("uploadToPath")
    @Parameters({@Parameter(name = "file", description = "文件", required = true), @Parameter(name = "filePath", description = "指定路径", required = true)})
    public R<String> uploadToPath(@RequestParam("file") MultipartFile file, @RequestParam("filePath") String filePath) {
        String path = bcyFileService.uploadToPath(file, filePath);
        return R.ok(path);
    }

    @Operation(summary = "文件删除")
    @GetMapping("deleteFile")
    @Parameters(@Parameter(name = "filePath", description = "文件路径"))
    public R<String> deleteFile(@RequestParam("filePath") String filePath) {
        bcyFileService.deleteFile(filePath);
        return R.ok("删除成功");
    }

}
