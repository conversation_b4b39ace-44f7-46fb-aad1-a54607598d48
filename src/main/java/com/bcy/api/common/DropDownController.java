package com.bcy.api.common;

import com.bcy.business.common.BcySysDropDownService;
import com.bcy.domain.R;
import com.bcy.domain.response.DropDownResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 下拉列表服务
 *
 * <AUTHOR>
 * @since 2024/10/27-21:38
 */
@Tag(name = "下拉列表服务", description = "下拉列表服务")
@RestController
@RequestMapping("admin/")
public class DropDownController {

    @Resource
    private BcySysDropDownService bcySysDropDownService;

    @Operation(summary = "下拉列表服务")
    @Parameter(name = "type", description = "下拉框类型", required = true)
    @GetMapping({"dropDown"})
    public R<List<DropDownResponse>> dropDown(@RequestParam("type") String type){
        return R.ok(bcySysDropDownService.dropDownByType(type));
    }
}
