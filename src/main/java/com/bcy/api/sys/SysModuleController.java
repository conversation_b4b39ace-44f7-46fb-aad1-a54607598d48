package com.bcy.api.sys;

import com.bcy.annotation.BcyLog;
import com.bcy.annotation.BcyNoRepeat;
import com.bcy.domain.response.sys.SysModuleResponse;
import com.bcy.business.sys.BcySysModuleService;
import com.bcy.domain.R;
import com.bcy.domain.form.IdsForm;
import com.bcy.domain.form.sys.SysModuleForm;
import com.bcy.domain.group.opration.UpdateGroup;
import com.bcy.domain.request.PageRequest;
import com.bcy.enums.DateEnum;
import com.bcy.utils.PageUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 系统模块服务
 *
 * <AUTHOR>
 * @since 2023/3/27-21:38
 */
@Tag(name = "系统模块服务", description = "系统模块服务")
@RestController
@RequestMapping("module/")
public class SysModuleController {
    @Resource
    private BcySysModuleService bcySysModuleService;

    @Operation(summary = "模块服务列表")
    @GetMapping("moduleList")
    public R<List<SysModuleResponse>> moduleList() {
        return R.ok(bcySysModuleService.list());
    }

    @Operation(summary = "分页获取模块服务列表")
    @Parameter(name = "pageRequest", description = "分页表单", required = true)
    @PostMapping("list")
    public R<PageUtils> list(@RequestBody @Validated PageRequest pageRequest) {
        return R.ok(bcySysModuleService.queryPage(pageRequest));
    }

    @Operation(summary = "根据Id获取模块服务信息")
    @GetMapping("info/{id}")
    public R<SysModuleResponse> info(@PathVariable("id") Long id) {
        return R.ok(bcySysModuleService.getInfoById(id));
    }

    @BcyNoRepeat(value = DateEnum.SECONDS)
    @BcyLog(businessType = "新增模块服务")
    @Operation(summary = "新增模块服务信息")
    @Parameter(name = "moduleForm", description = "模块服务信息表单", required = true)
    @PostMapping("insert")
    public R<String> insert(@RequestBody @Validated SysModuleForm moduleForm) {
        boolean flag = bcySysModuleService.saveModule(moduleForm);
        return flag ? R.ok("新增成功") : R.fail("新增失败");
    }

    @BcyLog(businessType = "修改模块服务")
    @Operation(summary = "修改模块服务信息")
    @Parameter(name = "moduleForm", description = "模块服务信息表单", required = true)
    @PostMapping("update")
    public R<String> update(@RequestBody @Validated(UpdateGroup.class) SysModuleForm moduleForm) {
        boolean flag = bcySysModuleService.updateModule(moduleForm);
        return flag ? R.ok("更新成功") : R.fail("更新失败");
    }

    @BcyLog(businessType = "删除模块服务")
    @Operation(summary = "批量删除模块服务")
    @Parameter(name = "idsForm", description = "id数组表单", required = true)
    @PostMapping("delete")
    public R<String> delete(@RequestBody @Validated IdsForm idsForm) {
        boolean flag = bcySysModuleService.deleteModules(idsForm);
        return flag ? R.ok("删除成功") : R.fail("删除失败");
    }

    @BcyLog(businessType = "启用或禁用模块服务")
    @BcyNoRepeat(value = DateEnum.SECONDS)
    @Operation(summary = "启用或禁用模块服务")
    @GetMapping("updateStatus/{id}")
    public R<String> updateStatus(@PathVariable("id") Long id) {
        boolean flag = bcySysModuleService.updateStatus(id);
        return flag ? R.ok("更改成功") : R.fail("更改失败");
    }

}
