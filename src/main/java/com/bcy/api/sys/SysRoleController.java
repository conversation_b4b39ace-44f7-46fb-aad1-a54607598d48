package com.bcy.api.sys;

import com.bcy.annotation.BcyLog;
import com.bcy.annotation.BcyNoRepeat;
import com.bcy.domain.response.sys.SysRoleResponse;
import com.bcy.business.sys.BcySysRoleService;
import com.bcy.domain.R;
import com.bcy.domain.form.BindIdsForm;
import com.bcy.domain.form.IdsForm;
import com.bcy.domain.form.sys.SysRoleForm;
import com.bcy.domain.group.opration.UpdateGroup;
import com.bcy.domain.request.PageRequest;
import com.bcy.enums.DateEnum;
import com.bcy.utils.PageUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 系统角色服务
 *
 * <AUTHOR>
 * @since 2024/01/04
 */
@Tag(name = "系统角色服务", description = "系统角色服务")
@RestController
@RequestMapping("role/")
public class SysRoleController {
    @Resource
    private BcySysRoleService bcySysRoleService;

    @Operation(summary = "获取角色分页列表")
    @Parameter(name = "pageRequest", description = "分页表单", required = true)
    @PostMapping("list")
    public R<PageUtils> list(@RequestBody @Validated PageRequest pageRequest) {
        return R.ok(bcySysRoleService.queryPage(pageRequest));
    }

    @Operation(summary = "根据角色Id获取角色详情")
    @GetMapping("info/{id}")
    public R<SysRoleResponse> info(@PathVariable("id") Long id) {
        return R.ok(bcySysRoleService.getRoleInfo(id));
    }

    @BcyLog(businessType = "删除角色信息")
    @Operation(summary = "根据id数组批量删除角色信息")
    @Parameter(name = "idsForm", description = "id数组表单", required = true)
    @PostMapping("delete")
    public R<String> delete(@RequestBody @Validated IdsForm idsForm) {
        boolean flag = bcySysRoleService.deleteRoles(idsForm);
        return flag ? R.ok("删除成功") : R.fail("删除失败");
    }

    @BcyLog(businessType = "新增角色信息")
    @BcyNoRepeat(value = DateEnum.SECONDS)
    @Operation(summary = "新增角色信息")
    @Parameter(name = "roleForm", description = "角色表单", required = true)
    @PostMapping("insert")
    public R<String> insert(@RequestBody @Validated SysRoleForm roleForm) {
        boolean flag = bcySysRoleService.saveRole(roleForm);
        return flag ? R.ok("新增成功") : R.fail("新增失败");
    }

    @BcyLog(businessType = "修改角色信息")
    @Operation(summary = "修改角色信息")
    @Parameter(name = "roleForm", description = "角色表单", required = true)
    @PostMapping("update")
    public R<String> update(@RequestBody @Validated(UpdateGroup.class) SysRoleForm roleForm) {
        boolean flag = bcySysRoleService.updateRole(roleForm);
        return flag ? R.ok("更新成功") : R.fail("更新失败");
    }

    @Operation(summary = "根据角色Id绑定菜单Id")
    @Parameter(name = "bindIdsForm", description = "绑定ID关联表单", required = true)
    @PostMapping("bindMenuIds")
    public R<String> bindMenuIds(@RequestBody @Validated BindIdsForm bindIdsForm) {
        bcySysRoleService.bindMenuIds(bindIdsForm);
        return R.ok("绑定成功");
    }

    @Operation(summary = "根据角色Id获取已绑定菜单Id")
    @GetMapping("roleMenuIds/{id}")
    public R<List<Long>> roleMenuIds(@PathVariable("id") Long id) {
        return R.ok(bcySysRoleService.roleMenuIdList(id));
    }
}
