package com.bcy.api.sys;

import com.bcy.annotation.BcyNoRepeat;
import com.bcy.business.common.BcySignService;
import com.bcy.domain.R;
import com.bcy.domain.form.IdsForm;
import com.bcy.domain.group.opration.UpdateGroup;
import com.bcy.domain.response.login.SysLoginUserResponse;
import com.bcy.domain.response.sys.SysUserResponse;
import com.bcy.enums.DateEnum;
import com.bcy.business.sys.BcySysUserService;
import com.bcy.domain.form.common.SignDateForm;
import com.bcy.domain.form.sys.SysUserForm;
import com.bcy.domain.form.user.AvatarForm;
import com.bcy.domain.form.user.BindLinkForm;
import com.bcy.domain.form.user.PersonForm;
import com.bcy.domain.form.user.ResetPwdForm;
import com.bcy.domain.form.user.UpdatePwdForm;
import com.bcy.domain.request.UserPageRequest;
import com.bcy.utils.PageUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 用户服务
 *
 * <AUTHOR>
 * @since 2024/01/09
 */
@Tag(name = "用户服务", description = "用户服务")
@RestController
@Slf4j
@RequestMapping("user/")
public class SysUserController {
    @Resource
    private BcySysUserService userService;
    @Resource
    private BcySignService bcySignService;

    @Operation(summary = "获取用户列表（分页）")
    @Parameter(name = "pageRequest", description = "分页表单", required = true)
    @PostMapping("list")
    public R<PageUtils> list(@RequestBody @Validated UserPageRequest pageRequest) {
        return R.ok(userService.queryPage(pageRequest));
    }

    @Operation(summary = "登录用户信息")
    @GetMapping("loginUser")
    public R<SysLoginUserResponse> loginUser() {
        return R.ok(userService.loginUserInfo());
    }

    @Operation(summary = "根据用户Id获取用户信息")
    @GetMapping("info/{id}")
    public R<SysUserResponse> info(@PathVariable("id") Long id) {
        return R.ok(userService.getInfo(id));
    }

    @Operation(summary = "根据id数组批量删除用户信息")
    @Parameter(name = "idsForm", description = "id数组表单", required = true)
    @PostMapping("delete")
    public R<String> delete(@RequestBody @Validated IdsForm idsForm) {
        boolean flag = userService.deleteUsers(idsForm);
        return flag ? R.ok("删除成功") : R.fail("删除失败");
    }

    @BcyNoRepeat(value = DateEnum.SECONDS)
    @Operation(summary = "新增用户信息")
    @Parameter(name = "userForm", description = "用户表单", required = true)
    @PostMapping("insert")
    public R<String> insert(@RequestBody @Validated SysUserForm userForm) {
        boolean flag = userService.saveUser(userForm);
        return flag ? R.ok("新增成功") : R.fail("新增失败");
    }

    @Operation(summary = "修改用户信息")
    @Parameter(name = "userForm", description = "用户表单", required = true)
    @PostMapping("update")
    public R<String> update(@RequestBody @Validated(UpdateGroup.class) SysUserForm userForm) {
        boolean flag = userService.updateUser(userForm);
        return flag ? R.ok("更新成功") : R.fail("更新失败");
    }

    @Operation(summary = "修改个人信息")
    @Parameter(name = "userForm", description = "用户表单", required = true)
    @PostMapping("updatePerson")
    public R<String> updatePerson(@RequestBody @Validated PersonForm userForm) {
        boolean flag = userService.updatePerson(userForm);
        return flag ? R.ok("更新成功") : R.fail("更新失败");
    }

    @Operation(summary = "修改个人头像")
    @Parameter(name = "avatarForm", description = "头像表单", required = true)
    @PostMapping("updateAvatar")
    public R<String> updateAvatar(@RequestBody @Validated AvatarForm avatarForm) {
        boolean flag = userService.updateAvatar(avatarForm);
        return flag ? R.ok("更新成功") : R.fail("更新失败");
    }

    @Operation(summary = "重置用户密码")
    @Parameter(name = "resetPwdForm", description = "重置密码表单", required = true)
    @PostMapping("resetPwd")
    public R<String> resetPwd(@RequestBody @Validated ResetPwdForm resetPwdForm) {
        boolean flag = userService.resetPwd(resetPwdForm);
        return flag ? R.ok("重置成功") : R.fail("重置失败");
    }

    @Operation(summary = "修改登录用户密码")
    @PostMapping("updatePwd")
    @Parameter(name = "updatePwdForm", description = "密码更新表单", required = true)
    public R<String> updatePwd(@RequestBody @Validated UpdatePwdForm updatePwdForm) {
        boolean flag = userService.updatePwd(updatePwdForm);
        return flag ? R.ok("更改成功") : R.fail("更改失败");
    }

    @BcyNoRepeat(value = DateEnum.SECONDS)
    @Operation(summary = "启用或禁用用户")
    @GetMapping("updateStatus/{id}")
    public R<String> updateStatus(@PathVariable("id") Long id) {
        boolean flag = userService.updateStatus(id);
        return flag ? R.ok("更改成功") : R.fail("更改失败");
    }

    @BcyNoRepeat(value = DateEnum.SECONDS)
    @Operation(summary = "导出用户")
    @Parameter(name = "pageRequest", description = "分页表单", required = true)
    @PostMapping("export")
    public R<String> export(@RequestBody @Validated UserPageRequest pageRequest) {
        userService.export(pageRequest);
        return R.ok("导出中，请关注文件列表！");
    }


    @BcyNoRepeat(value = DateEnum.MINUTES)
    @Operation(summary = "绑定手机号或者邮箱")
    @Parameter(name = "bindLinkForm", description = "绑定表单", required = true)
    @PostMapping("bindLink")
    public R<String> bindLink(@RequestBody @Validated BindLinkForm bindLinkForm) {
        boolean flag = userService.bindLink(bindLinkForm);
        return flag ? R.ok("绑定成功") : R.fail("绑定失败");
    }

    @BcyNoRepeat(value = DateEnum.SECONDS)
    @Operation(summary = "用户签到")
    @Parameter(name = "signDateForm", description = "签到表单", required = true)
    @PostMapping("sign")
    public R<String> sign(@RequestBody SignDateForm signDateForm) {
        bcySignService.userSign(signDateForm);
        return R.ok("签到成功！");
    }

    @BcyNoRepeat(value = DateEnum.SECONDS)
    @Operation(summary = "用户签到记录")
    @GetMapping("signRecord")
    public R<Map<String, Boolean>> signRecord() {
        return R.ok(bcySignService.record());
    }


    /**
     * 统计当月连续签到次数
     */
    @Operation(summary = "统计当月连续签到次数")
    @PostMapping(value = "count")
    public R<Integer> count() {
        return R.ok(bcySignService.count());
    }
}
