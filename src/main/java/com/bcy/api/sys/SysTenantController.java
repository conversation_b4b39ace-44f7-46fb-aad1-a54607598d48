package com.bcy.api.sys;

import com.bcy.annotation.BcyLog;
import com.bcy.annotation.BcyNoRepeat;
import com.bcy.domain.response.sys.SysTenantResponse;
import com.bcy.business.sys.BcySysTenantService;
import com.bcy.domain.R;
import com.bcy.domain.form.IdsForm;
import com.bcy.domain.form.sys.SysTenantForm;
import com.bcy.domain.group.opration.UpdateGroup;
import com.bcy.domain.request.PageRequest;
import com.bcy.enums.DateEnum;
import com.bcy.utils.PageUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 租户相关服务
 *
 * <AUTHOR>
 * @since 2023/12/26
 */
@Tag(name = "租户相关服务", description = "租户相关服务")
@RestController
@RequestMapping("tenant/")
public class SysTenantController {
    @Resource
    private BcySysTenantService bcySysTenantService;

    @Operation(summary = "获取租户列表（分页）")
    @Parameter(name = "pageRequest", description = "分页表单", required = true)
    @PostMapping("list")
    public R<PageUtils> list(@RequestBody @Validated PageRequest pageRequest) {
        return R.ok(bcySysTenantService.queryPage(pageRequest));
    }

    @BcyLog(businessType = "删除租户信息")
    @Operation(summary = "根据id数组批量删除租户信息")
    @Parameter(name = "idsForm", description = "id数组表单", required = true)
    @PostMapping("delete")
    public R<String> delete(@RequestBody @Validated IdsForm idsForm) {
        boolean flag = bcySysTenantService.deleteTenant(idsForm);
        return flag ? R.ok("删除成功") : R.fail("删除失败");
    }

    @Operation(summary = "根据租户Id获取租户信息")
    @GetMapping("info/{id}")
    public R<SysTenantResponse> info(@PathVariable("id") Long id) {
        return R.ok(bcySysTenantService.getInfoById(id));
    }

    @BcyLog(businessType = "新增租户信息")
    @BcyNoRepeat(value = DateEnum.SECONDS)
    @Operation(summary = "新增租户信息")
    @Parameter(name = "tenantForm", description = "租户表单", required = true)
    @PostMapping("insert")
    public R<String> insert(@RequestBody @Validated SysTenantForm tenantForm) {
        boolean flag = bcySysTenantService.saveTenant(tenantForm);
        return flag ? R.ok("新增成功") : R.fail("新增失败");
    }

    @BcyLog(businessType = "修改租户信息")
    @Operation(summary = "修改租户信息")
    @Parameter(name = "tenantForm", description = "租户表单", required = true)
    @PostMapping("update")
    public R<String> update(@RequestBody @Validated(UpdateGroup.class) SysTenantForm tenantForm) {
        boolean flag = bcySysTenantService.updateTenant(tenantForm);
        return flag ? R.ok("更新成功") : R.fail("更新失败");
    }

    @Operation(summary = "已绑定的用户列表")
    @GetMapping("bindList/{id}")
    public R<List<Long>> bindList(@PathVariable("id") Long id) {
        List<Long> userIdList = bcySysTenantService.bindListByTenantId(id);
        return R.ok(userIdList);
    }

}
