package com.bcy.api.sys;

import com.bcy.annotation.BcyLog;
import com.bcy.annotation.BcyNoRepeat;
import com.bcy.domain.response.route.SysRoutsResponse;
import com.bcy.domain.response.sys.SysMenuResponse;
import com.bcy.business.sys.BcySysMenuService;
import com.bcy.domain.R;
import com.bcy.domain.form.sys.SysMenuForm;
import com.bcy.domain.group.opration.UpdateGroup;
import com.bcy.enums.DateEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 菜单服务
 *
 * <AUTHOR>
 * @since 2024/01/08
 */
@Tag(name = "菜单服务", description = "菜单服务")
@RestController
@RequestMapping("menu/")
public class SysMenuController {

    @Resource
    private BcySysMenuService bcySysMenuService;

    @Operation(summary = "获取菜单列表")
    @GetMapping("list")
    @Parameter(name = "name", description = "菜单名称")
    public R<List<SysMenuResponse>> list(@RequestParam("name") String name) {
        return R.ok(bcySysMenuService.getUserMenuList(true, name));
    }

    @Operation(summary = "获取当前用户左侧菜单栏")
    @GetMapping("nav")
    public R<List<SysRoutsResponse>> nav() {
        return R.ok(bcySysMenuService.userNav(false));
    }

    @Operation(summary = "根据菜单Id获取菜单详情")
    @GetMapping("info/{id}")
    public R<SysMenuResponse> info(@PathVariable("id") Long id) {
        return R.ok(bcySysMenuService.getInfo(id));
    }

    @BcyNoRepeat(value = DateEnum.SECONDS)
    @BcyLog(businessType = "新增菜单")
    @Operation(summary = "新增菜单信息")
    @Parameter(name = "menuForm", description = "菜单表单", required = true)
    @PostMapping("insert")
    public R<String> insert(@RequestBody @Validated SysMenuForm menuForm) {
        boolean flag = bcySysMenuService.saveMenu(menuForm);
        return flag ? R.ok("新增成功") : R.fail("新增失败");
    }

    @BcyLog(businessType = "修改菜单")
    @Operation(summary = "修改菜单信息")
    @PostMapping("update")
    @Parameter(name = "menuForm", description = "菜单表单", required = true)
    public R<String> update(@RequestBody @Validated(UpdateGroup.class) SysMenuForm menuForm) {
        boolean flag = bcySysMenuService.updateMenu(menuForm);
        return flag ? R.ok("更新成功") : R.fail("更新失败");
    }

    @BcyLog(businessType = "删除菜单")
    @Operation(summary = "根据id删除菜单信息")
    @GetMapping("delete/{id}")
    public R<String> delete(@PathVariable("id") Long id) {
        boolean flag = bcySysMenuService.deleteMenus(id);
        return flag ? R.ok("删除成功") : R.fail("删除失败");
    }

}
