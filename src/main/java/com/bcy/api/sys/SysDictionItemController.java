package com.bcy.api.sys;

import com.bcy.annotation.BcyNoRepeat;
import com.bcy.business.sys.BcySysDictionItemService;
import com.bcy.domain.response.sys.SysDictionItemResponse;
import com.bcy.domain.R;
import com.bcy.domain.form.IdsForm;
import com.bcy.domain.form.sys.SysDictionItemForm;
import com.bcy.domain.group.opration.UpdateGroup;
import com.bcy.domain.request.PageRequest;
import com.bcy.enums.DateEnum;
import com.bcy.utils.PageUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 字典项相关服务
 *
 * <AUTHOR>
 * @since 2024/09/13
 */
@Tag(name = "字典项相关服务", description = "字典项相关服务")
@RestController
@RequestMapping("diction/item/")
public class SysDictionItemController {
    @Resource
    private BcySysDictionItemService bcySysDictionItemService;

    @Operation(summary = "获取字典项列表（分页）")
    @Parameter(name = "pageRequest", description = "分页表单", required = true)
    @PostMapping("list")
    public R<PageUtils> list(@RequestBody @Validated PageRequest pageRequest) {
        return R.ok(bcySysDictionItemService.queryPage(pageRequest));
    }

    @Operation(summary = "根据id数组批量删除字典项信息")
    @Parameter(name = "idsForm", description = "id数组表单", required = true)
    @PostMapping("delete")
    public R<String> delete(@RequestBody @Validated IdsForm idsForm) {
        boolean flag = bcySysDictionItemService.deleteDiction(idsForm);
        return flag ? R.ok("删除成功") : R.fail("删除失败");
    }

    @Operation(summary = "根据字典项Id获取字典项信息")
    @GetMapping("info/{id}")
    public R<SysDictionItemResponse> info(@PathVariable("id") Long id) {
        return R.ok(bcySysDictionItemService.getInfoById(id));
    }

    @BcyNoRepeat(value = DateEnum.SECONDS)
    @Operation(summary = "新增字典项信息")
    @Parameter(name = "dictionItemForm", description = "字典项表单", required = true)
    @PostMapping("insert")
    public R<String> insert(@RequestBody @Validated SysDictionItemForm dictionItemForm) {
        boolean flag = bcySysDictionItemService.saveDiction(dictionItemForm);
        return flag ? R.ok("新增成功") : R.fail("新增失败");
    }

    /**
     * 修改字典项
     */
    @Operation(summary = "修改字典项信息")
    @Parameter(name = "dictionItemForm", description = "字典项表单", required = true)
    @PostMapping("update")
    public R<String> update(@RequestBody @Validated(UpdateGroup.class) SysDictionItemForm dictionItemForm) {
        boolean flag = bcySysDictionItemService.updateDiction(dictionItemForm);
        return flag ? R.ok("更新成功") : R.fail("更新失败");
    }
}
