package com.bcy.api.sys;

import com.bcy.annotation.BcyNoRepeat;
import com.bcy.domain.dto.diction.DictionDTO;
import com.bcy.domain.response.sys.SysDictionResponse;
import com.bcy.business.sys.BcySysDictionService;
import com.bcy.domain.R;
import com.bcy.domain.form.IdsForm;
import com.bcy.domain.form.sys.SysDictionForm;
import com.bcy.domain.group.opration.UpdateGroup;
import com.bcy.domain.request.PageRequest;
import com.bcy.enums.DateEnum;
import com.bcy.utils.PageUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 字典相关服务
 *
 * <AUTHOR>
 * @since 2024/09/13
 */
@Tag(name = "字典相关服务", description = "字典相关服务")
@RestController
@RequestMapping("diction/")
public class SysDictionController {
    @Resource
    private BcySysDictionService bcySysDictionService;

    @Operation(summary = "获取字典列表（分页）")
    @Parameter(name = "pageRequest", description = "分页表单", required = true)
    @PostMapping("list")
    public R<PageUtils> list(@RequestBody @Validated PageRequest pageRequest) {
        return R.ok(bcySysDictionService.queryPage(pageRequest));
    }

    @Operation(summary = "根据id数组批量删除字典信息")
    @Parameter(name = "idsForm", description = "id数组表单", required = true)
    @PostMapping("delete")
    public R<String> delete(@RequestBody @Validated IdsForm idsForm) {
        boolean flag = bcySysDictionService.deleteDiction(idsForm);
        return flag ? R.ok("删除成功") : R.fail("删除失败");
    }

    @Operation(summary = "根据字典Id获取字典信息")
    @GetMapping("info/{id}")
    public R<SysDictionResponse> info(@PathVariable("id") Long id) {
        return R.ok(bcySysDictionService.getInfoById(id));
    }

    @BcyNoRepeat(value = DateEnum.SECONDS)
    @Operation(summary = "新增字典信息")
    @Parameter(name = "dictionForm", description = "字典表单", required = true)
    @PostMapping("insert")
    public R<String> insert(@RequestBody @Validated SysDictionForm dictionForm) {
        boolean flag = bcySysDictionService.saveDiction(dictionForm);
        return flag ? R.ok("新增成功") : R.fail("新增失败");
    }

    @Operation(summary = "修改字典信息")
    @Parameter(name = "dictionForm", description = "字典表单", required = true)
    @PostMapping("update")
    public R<String> update(@RequestBody @Validated(UpdateGroup.class) SysDictionForm dictionForm) {
        boolean flag = bcySysDictionService.updateDiction(dictionForm);
        return flag ? R.ok("更新成功") : R.fail("更新失败");
    }

    @Operation(summary = "获取字典列表")
    @GetMapping("allList")
    public R<List<DictionDTO>> allList() {
        return R.ok(bcySysDictionService.allList());
    }
}
