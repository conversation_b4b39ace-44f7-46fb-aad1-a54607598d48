package com.bcy.api.sys;

import com.bcy.annotation.BcyLog;
import com.bcy.annotation.BcyNoRepeat;
import com.bcy.domain.R;
import com.bcy.domain.group.opration.UpdateGroup;
import com.bcy.domain.request.PageRequest;
import com.bcy.admin.domain.response.SysConfigResponse;
import com.bcy.enums.DateEnum;
import com.bcy.business.sys.BcySysConfigService;
import com.bcy.domain.form.sys.SysConfigForm;
import com.bcy.utils.PageUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 系统配置服务
 *
 * <AUTHOR>
 * @since 2023/3/27-21:38
 */
@Tag(name = "系统配置服务", description = "系统配置服务")
@RestController
@RequestMapping("config/")
public class SysConfigController {
    @Resource
    private BcySysConfigService sysConfigService;

    @Operation(summary = "分页获取参数列表")
    @Parameter(name = "PageRequest", description = "分页表单", required = true)
    @PostMapping("list")
    public R<PageUtils> list(@RequestBody @Validated PageRequest PageRequest) {
        return R.ok(sysConfigService.queryPage(PageRequest));
    }

    @Operation(summary = "根据Id获取配置信息")
    @GetMapping("info/{id}")
    public R<SysConfigResponse> info(@PathVariable("id") Long id) {
        return R.ok(sysConfigService.getInfoById(id));
    }

    @BcyNoRepeat(value = DateEnum.SECONDS)
    @BcyLog(businessType = "新增配置")
    @Operation(summary = "新增配置信息")
    @Parameter(name = "configForm", description = "配置信息表单", required = true)
    @PostMapping("insert")
    public R<String> insert(@RequestBody @Validated SysConfigForm configForm) {
        boolean flag = sysConfigService.saveConfig(configForm);
        return flag ? R.ok("新增成功") : R.fail("新增失败");
    }

    @BcyLog(businessType = "修改配置")
    @Operation(summary = "修改配置信息")
    @Parameter(name = "configForm", description = "配置信息表单", required = true)
    @PostMapping("update")
    public R<String> update(@RequestBody @Validated(UpdateGroup.class) SysConfigForm configForm) {
        boolean flag = sysConfigService.updateConfig(configForm);
        return flag ? R.ok("更新成功") : R.fail("更新失败");
    }

    @BcyLog(businessType = "删除配置")
    @Operation(summary = "批量删除配置")
    @PostMapping("delete/{id}")
    public R<String> delete(@PathVariable("id") Long id) {
        boolean flag = sysConfigService.deleteConfigs(id);
        return flag ? R.ok("删除成功") : R.fail("删除失败");
    }

    @BcyNoRepeat(value = DateEnum.SECONDS)
    @Operation(summary = "启用或禁用配置")
    @GetMapping("updateStatus/{id}")
    public R<String> updateStatus(@PathVariable("id") Long id) {
        boolean flag = sysConfigService.updateStatus(id);
        return flag ? R.ok("更改成功") : R.fail("更改失败");
    }

    @Operation(summary = "刷新缓存")
    @GetMapping("refreshCache")
    public R<String> refreshCache() {
        boolean flag = sysConfigService.refreshCache();
        return flag ? R.ok("刷新成功") : R.fail("刷新失败");
    }

}
