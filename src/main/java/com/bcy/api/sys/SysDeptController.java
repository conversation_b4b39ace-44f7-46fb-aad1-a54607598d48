package com.bcy.api.sys;

import com.bcy.annotation.BcyNoRepeat;
import com.bcy.domain.response.page.sys.SysDeptPageResponse;
import com.bcy.domain.response.sys.SysDeptResponse;
import com.bcy.business.sys.BcySysDeptService;
import com.bcy.domain.R;
import com.bcy.domain.form.sys.SysDeptForm;
import com.bcy.domain.group.opration.UpdateGroup;
import com.bcy.enums.DateEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 部门相关服务
 *
 * <AUTHOR>
 * @since 2024/11/09
 */
@Tag(name = "部门相关服务", description = "部门相关服务")
@RestController
@RequestMapping("dept/")
public class SysDeptController {
    @Resource
    private BcySysDeptService bcySysDeptService;

    @Operation(summary = "获取部门列表")
    @Parameter(name = "name", description = "部门名称")
    @GetMapping("list")
    public R<List<SysDeptPageResponse>> list(@RequestParam("name") String name) {
        return R.ok(bcySysDeptService.deptListPage(name));
    }

    @Operation(summary = "根据id数组批量删除部门信息")
    @GetMapping("delete/{id}")
    public R<String> delete(@PathVariable("id") Long id) {
        boolean flag = bcySysDeptService.deleteDept(id);
        return flag ? R.ok("删除成功") : R.fail("删除失败");
    }

    @Operation(summary = "根据部门Id获取部门信息")
    @GetMapping("info/{id}")
    public R<SysDeptResponse> info(@PathVariable("id") Long id) {
        return R.ok(bcySysDeptService.getInfoById(id));
    }

    @BcyNoRepeat(value = DateEnum.SECONDS)
    @Operation(summary = "新增部门信息")
    @Parameter(name = "deptForm", description = "部门表单", required = true)
    @PostMapping("insert")
    public R<String> insert(@RequestBody @Validated SysDeptForm deptForm) {
        boolean flag = bcySysDeptService.saveDept(deptForm);
        return flag ? R.ok("新增成功") : R.fail("新增失败");
    }

    @Operation(summary = "修改部门信息")
    @Parameter(name = "deptForm", description = "部门表单", required = true)
    @PostMapping("update")
    public R<String> update(@RequestBody @Validated(UpdateGroup.class) SysDeptForm deptForm) {
        boolean flag = bcySysDeptService.updateDept(deptForm);
        return flag ? R.ok("更新成功") : R.fail("更新失败");
    }
}
