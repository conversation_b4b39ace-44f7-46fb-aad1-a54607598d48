package com.bcy.api.web;

import com.bcy.business.web.BcyCustomerService;
import com.bcy.business.web.BcyWebConfigService;
import com.bcy.domain.R;
import com.bcy.domain.form.web.BcySeekAdviceForm;
import com.bcy.domain.request.web.BcyWebInfoRequest;
import com.bcy.domain.response.web.BcyWebResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 留资站点接口服务
 *
 * <AUTHOR>
 * @since 2023/3/27-21:38
 */
@Tag(name = "留资站点接口服务", description = "留资站点接口服务")
@RestController
@RequestMapping("/api/web/")
public class BcyWebApiController {
    @Resource
    private BcyWebConfigService bcyWebConfigService;
    @Resource
    private BcyCustomerService bcyCustomerService;

    @Operation(summary = "获取站点信息", description = "根据平台类型和站点ID获取站点信息")
    @Parameter(name = "bcyWebInfoRequest", description = "站点信息表单", required = true)
    @PostMapping("getInfo")
    public R<BcyWebResponse> getByIdPlatformType(@RequestBody @Validated BcyWebInfoRequest bcyWebInfoRequest) {
        return R.ok(bcyWebConfigService.getByIdPlatformType(bcyWebInfoRequest));
    }

    @Operation(summary = "资料提交前验证", description = "发送验证码钱验证是否存在该用户")
    @Parameter(name = "phone", description = "客户手机号", required = true)
    @Parameter(name = "siteId", description = "站点ID", required = true)
    @GetMapping("checkPhone")
    public R<String> checkPhone(@RequestParam("phone") String phone, @RequestParam("siteId") Long siteId) {
        boolean flag = bcyCustomerService.checkPhone(phone, siteId);
        return flag ? R.ok("验证成功") : R.fail("您已提交相关信息，请勿重复提交！");
    }

    @Operation(summary = "资料提交", description = "客户资料提交")
    @Parameter(name = "bcySeekAdviceForm", description = "客户提交表单", required = true)
    @PostMapping("seekAdvice")
    public R<String> seekAdvice(@RequestBody @Validated BcySeekAdviceForm bcySeekAdviceForm) {
        boolean flag = bcyCustomerService.seekAdvice(bcySeekAdviceForm);
        return flag ? R.ok("提交成功") : R.fail("提交失败");
    }

}
