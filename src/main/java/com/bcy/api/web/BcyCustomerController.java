package com.bcy.api.web;

import com.bcy.annotation.BcyNoRepeat;
import com.bcy.business.web.BcyCustomerService;
import com.bcy.domain.R;
import com.bcy.domain.form.web.BcyCustomerForm;
import com.bcy.domain.request.web.BcyCustomerPageRequest;
import com.bcy.domain.response.web.BcyCustomerResponse;
import com.bcy.enums.DateEnum;
import com.bcy.utils.PageUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 留资客户服务
 *
 * <AUTHOR>
 * @since 2024/9/22-21:38
 */
@Tag(name = "留资客户服务", description = "留资客户服务")
@RestController
@RequestMapping("customer/")
public class BcyCustomerController {
    @Resource
    private BcyCustomerService bcyCustomerService;

    @Operation(summary = "分页获取留资客户列表")
    @Parameter(name = "bcyCustomerPageRequest", description = "分页表单", required = true)
    @PostMapping("list")
    public R<PageUtils> list(@RequestBody @Validated BcyCustomerPageRequest bcyCustomerPageRequest) {
        return R.ok(bcyCustomerService.queryPage(bcyCustomerPageRequest));
    }

    @Operation(summary = "根据Id获取留资客户信息")
    @GetMapping("info/{id}")
    public R<BcyCustomerResponse> info(@PathVariable("id") Long id) {
        return R.ok(bcyCustomerService.getInfoById(id));
    }

    @BcyNoRepeat(value = DateEnum.SECONDS)
    @Operation(summary = "更改联系状态")
    @Parameter(name = "bcyCustomerForm", description = "状态表单", required = true)
    @PostMapping("updateStatus")
    public R<String> updateStatus(@RequestBody @Validated BcyCustomerForm bcyCustomerForm) {
        boolean flag = bcyCustomerService.updateStatus(bcyCustomerForm);
        return flag ? R.ok("更改成功") : R.fail("更改失败");
    }

    @Operation(summary = "导出客户列表")
    @PostMapping("export")
    @Parameter(name = "bcyCustomerPageRequest", description = "分页表单", required = true)
    public R<String> export(@RequestBody @Validated BcyCustomerPageRequest bcyCustomerPageRequest) {
        bcyCustomerService.export(bcyCustomerPageRequest);
        return R.ok("导出中，请关注文件列表！");
    }


}
