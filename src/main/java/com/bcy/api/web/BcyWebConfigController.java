package com.bcy.api.web;

import com.bcy.annotation.BcyLog;
import com.bcy.annotation.BcyNoRepeat;
import com.bcy.business.web.BcyWebConfigService;
import com.bcy.domain.R;
import com.bcy.domain.form.IdsForm;
import com.bcy.domain.form.web.BcyWebForm;
import com.bcy.domain.group.opration.UpdateGroup;
import com.bcy.domain.request.PageRequest;
import com.bcy.domain.response.web.BcyWebResponse;
import com.bcy.enums.DateEnum;
import com.bcy.utils.PageUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 留资站点服务
 *
 * <AUTHOR>
 * @since 2024/9/22-21:38
 */
@Tag(name = "留资站点服务", description = "留资站点服务")
@RestController
@RequestMapping("site/")
public class BcyWebConfigController {
    @Resource
    private BcyWebConfigService bcyWebConfigService;

    @Operation(summary = "分页获取留资站点列表")
    @Parameter(name = "pageRequest", description = "分页表单", required = true)
    @PostMapping("list")
    public R<PageUtils> list(@RequestBody @Validated PageRequest pageRequest) {
        return R.ok(bcyWebConfigService.queryPage(pageRequest));
    }

    @Operation(summary = "根据Id获取留资站点信息")
    @GetMapping("info/{id}")
    public R<BcyWebResponse> info(@PathVariable("id") Long id) {
        return R.ok(bcyWebConfigService.getInfoById(id));
    }

    @BcyNoRepeat(value = DateEnum.SECONDS)
    @BcyLog(businessType = "新增留资站点")
    @Operation(summary = "新增留资站点信息")
    @Parameter(name = "bcyWebForm", description = "留资站点信息表单", required = true)
    @PostMapping("insert")
    public R<String> insert(@RequestBody @Validated BcyWebForm bcyWebForm) {
        boolean flag = bcyWebConfigService.save(bcyWebForm);
        return flag ? R.ok("新增成功") : R.fail("新增失败");
    }

    @Operation(summary = "修改留资站点信息")
    @Parameter(name = "bcyWebForm", description = "留资站点信息表单", required = true)
    @PostMapping("update")
    public R<String> update(@RequestBody @Validated(UpdateGroup.class) BcyWebForm bcyWebForm) {
        boolean flag = bcyWebConfigService.update(bcyWebForm);
        return flag ? R.ok("更新成功") : R.fail("更新失败");
    }

    @BcyLog(businessType = "删除留资站点")
    @Operation(summary = "批量删除留资站点")
    @Parameter(name = "idsForm", description = "id数组表单", required = true)
    @PostMapping("delete")
    public R<String> delete(@RequestBody @Validated IdsForm idsForm) {
        boolean flag = bcyWebConfigService.delete(idsForm);
        return flag ? R.ok("删除成功") : R.fail("删除失败");
    }

    @BcyNoRepeat(value = DateEnum.SECONDS)
    @Operation(summary = "启用或禁用留资站点")
    @GetMapping("updateStatus/{id}")
    public R<String> updateStatus(@PathVariable("id") Long id) {
        boolean flag = bcyWebConfigService.updateStatus(id);
        return flag ? R.ok("更改成功") : R.fail("更改失败");
    }


}
