package com.bcy.api.log;

import com.bcy.annotation.BcyLog;
import com.bcy.business.log.BcyFileLogService;
import com.bcy.domain.R;
import com.bcy.domain.form.IdsForm;
import com.bcy.domain.request.PageRequest;
import com.bcy.utils.PageUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 文件日志服务控制器
 *
 * <AUTHOR>
 * @since 2023-12-14
 */
@Tag(name = "文件日志服务控制器")
@RestController
@RequestMapping("log/file")
public class SysFileLogController {
    @Resource
    private BcyFileLogService bcyFileLogService;

    @Operation(summary = "文件日志列表")
    @Parameter(name = "pageRequest", description = "分页表单", required = true)
    @PostMapping("list")
    public R<PageUtils> list(@RequestBody @Validated PageRequest pageRequest) {
        return R.ok(bcyFileLogService.queryPage(pageRequest));
    }

    @BcyLog(businessType = "批量删除文件日志")
    @Operation(summary = "批量删除文件日志")
    @Parameter(name = "idsForm", description = "id数组表单", required = true)
    @PostMapping("delete")
    public R<String> delete(@RequestBody @Validated IdsForm idsForm) {
        boolean flag = bcyFileLogService.deleteLogs(idsForm);
        return flag ? R.ok("删除成功") : R.fail("删除失败");
    }

    @Operation(summary = "下载文件")
    @GetMapping("updateStatus/{id}")
    public R<String> updateStatus(@PathVariable("id") Long id) {
        boolean flag = bcyFileLogService.updateStatus(id);
        return flag ? R.ok("下载成功") : R.fail("下载失败");
    }


}
