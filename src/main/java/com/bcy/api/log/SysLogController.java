package com.bcy.api.log;

import com.bcy.annotation.BcyLog;
import com.bcy.business.log.BcySysLogService;
import com.bcy.domain.R;
import com.bcy.domain.request.PageRequest;
import com.bcy.utils.PageUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 系统日志服务控制器
 *
 * <AUTHOR>
 * @since 2023-12-14
 */
@Tag(name = "系统日志服务控制器")
@RestController
@RequestMapping("log/sys/")
public class SysLogController {

    @Resource
    private BcySysLogService bcySysLogService;

    @Operation(summary = "分页获取系统日志列表")
    @Parameter(name = "pageRequest", description = "分页表单", required = true)
    @PostMapping("list")
    public R<PageUtils> list(@RequestBody @Validated PageRequest pageRequest) {
        return R.ok(bcySysLogService.queryPage(pageRequest));
    }

    @BcyLog(businessType = "清理日志")
    @Operation(summary = "清理日志(默认30天)")
    @Parameter(name = "day", description = "天数", required = true)
    @GetMapping("clean")
    public R<String> clean(@RequestParam("day") Integer day) {
        boolean flag = bcySysLogService.deleteLogs(day);
        return flag ? R.ok("清除成功") : R.fail("暂无数据");
    }
}
