package com.bcy.api.log;

import com.bcy.annotation.BcyLog;
import com.bcy.business.log.BcyApiLogService;
import com.bcy.domain.response.log.SysApiLogResponse;
import com.bcy.domain.R;
import com.bcy.domain.request.PageRequest;
import com.bcy.utils.PageUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 系统日志服务控制器
 *
 * <AUTHOR>
 * @since 2023-12-14
 */
@Tag(name = "系统API日志服务控制器")
@RestController
@RequestMapping("log/api/")
public class SysApiLogController {

    @Resource
    private BcyApiLogService bcyApiLogService;

    @Operation(summary = "分页获取Api日志列表")
    @Parameter(name = "pageRequest", description = "分页表单", required = true)
    @PostMapping("list")
    public R<PageUtils> list(@RequestBody @Validated PageRequest pageRequest) {
        return R.ok(bcyApiLogService.queryPage(pageRequest));
    }

    @BcyLog(businessType = "清理Api日志")
    @Operation(summary = "清理Api日志(默认30天)")
    @Parameter(name = "day", description = "天数", required = true)
    @GetMapping("clean")
    public R<String> apiClean(@RequestParam("day") Integer day) {
        boolean flag = bcyApiLogService.deleteLogs(day);
        return flag ? R.ok("清除成功") : R.fail("暂无数据");
    }

    @Operation(summary = "根据id获取详情")
    @GetMapping("info/{id}")
    public R<SysApiLogResponse> getInfo(@PathVariable("id") Long id) {
        return R.ok(bcyApiLogService.getInfo(id));
    }
}
