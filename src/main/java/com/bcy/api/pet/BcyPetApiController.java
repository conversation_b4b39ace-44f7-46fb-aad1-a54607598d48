package com.bcy.api.pet;

import com.bcy.business.pet.BcyPetCategoryService;
import com.bcy.domain.R;
import com.bcy.domain.response.pet.BcyPetCategoryResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 宠物API接口服务
 *
 * <AUTHOR>
 * @since 2024/3/27-21:38
 */
@Tag(name = "宠物API接口服务", description = "宠物API接口服务")
@RestController
@RequestMapping("/api/pet/")
public class BcyPetApiController {
    @Resource
    private BcyPetCategoryService bcyPetCategoryService;

    @Operation(summary = "获取宠物分类列表")
    @GetMapping({"allList/{id}"})
    public R<List<BcyPetCategoryResponse>> allList(@PathVariable("id") Long id) {
        return R.ok(bcyPetCategoryService.allList(id));
    }

    @Operation(summary = "根据分类ID获取详情")
    @GetMapping({"info/{id}"})
    public R<BcyPetCategoryResponse> getInfoById(@PathVariable("id") Long id) {
        return R.ok(bcyPetCategoryService.getCategoryInfo(id));
    }




}
