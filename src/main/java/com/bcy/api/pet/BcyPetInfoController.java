package com.bcy.api.pet;

import com.bcy.annotation.BcyNoRepeat;
import com.bcy.business.pet.BcyPetInfoService;
import com.bcy.domain.R;
import com.bcy.domain.form.pet.BcyPetInfoForm;
import com.bcy.domain.group.opration.UpdateGroup;
import com.bcy.domain.request.PageRequest;
import com.bcy.domain.response.pet.BcyPetInfoResponse;
import com.bcy.enums.DateEnum;
import com.bcy.utils.PageUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户宠物接口服务
 *
 * <AUTHOR>
 * @since 2024/10/17-21:38
 */
@Tag(name = "用户宠物接口服务", description = "用户宠物接口服务")
@RestController
@RequestMapping("pet/user/")
public class BcyPetInfoController {
    @Resource
    private BcyPetInfoService bcyPetInfoService;

    @Operation(summary = "分页获取用户宠物列表")
    @Parameter(name = "pageRequest", description = "分页表单", required = true)
    @PostMapping("list")
    public R<PageUtils> list(@RequestBody @Validated PageRequest pageRequest) {
        return R.ok(bcyPetInfoService.queryPage(pageRequest));
    }

    @Operation(summary = "根据Id获取宠物科普信息")
    @GetMapping("info/{id}")
    public R<BcyPetInfoResponse> info(@PathVariable("id") Long id) {
        return R.ok(bcyPetInfoService.getPetInfoById(id));
    }

    @Operation(summary = "用户宠物列表")
    @GetMapping("list")
    public R<List<BcyPetInfoResponse>> list() {
        return R.ok(bcyPetInfoService.userPetList());
    }

    @BcyNoRepeat(value = DateEnum.SECONDS)
    @Operation(summary = "新增宠物信息")
    @Parameter(name = "bcyPetInfoForm", description = "宠物信息表单", required = true)
    @PostMapping("insert")
    public R<String> insert(@RequestBody @Validated BcyPetInfoForm bcyPetInfoForm) {
        boolean flag = bcyPetInfoService.savePetInfo(bcyPetInfoForm);
        return flag ? R.ok("新增成功") : R.fail("新增失败");
    }

    @Operation(summary = "修改宠物信息")
    @Parameter(name = "bcyPetInfoForm", description = "宠物信息表单", required = true)
    @PostMapping("update")
    public R<String> update(@RequestBody @Validated(UpdateGroup.class) BcyPetInfoForm bcyPetInfoForm) {
        boolean flag = bcyPetInfoService.updatePetInfo(bcyPetInfoForm);
        return flag ? R.ok("更新成功") : R.fail("更新失败");
    }

    @Operation(summary = "批量删除宠物信息")
    @Parameter(name = "idsForm", description = "id数组表单", required = true)
    @PostMapping("delete/{id}")
    public R<String> delete(@PathVariable("id") Long id) {
        boolean flag = bcyPetInfoService.deletePetInfo(id);
        return flag ? R.ok("删除成功") : R.fail("删除失败");
    }

    @BcyNoRepeat(value = DateEnum.SECONDS)
    @Operation(summary = "设置默认信息")
    @GetMapping("updateStatus/{id}")
    public R<String> updateStatus(@PathVariable("id") Long id) {
        boolean flag = bcyPetInfoService.defaultPet(id);
        return flag ? R.ok("更改成功") : R.fail("更改失败");
    }


}
