package com.bcy.api.pet;

import com.bcy.annotation.BcyNoRepeat;
import com.bcy.business.pet.BcyPetCategoryService;
import com.bcy.domain.R;
import com.bcy.domain.form.pet.BcyPetCategoryForm;
import com.bcy.domain.group.opration.UpdateGroup;
import com.bcy.domain.request.PageRequest;
import com.bcy.domain.response.pet.BcyPetCategoryResponse;
import com.bcy.enums.DateEnum;
import com.bcy.utils.PageUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 宠物分类接口服务
 *
 * <AUTHOR>
 * @since 2024/10/17-21:38
 */
@Tag(name = "宠物分类接口服务", description = "宠物分类接口服务")
@RestController
@RequestMapping("pet/category/")
public class BcyPetCategoryController {
    @Resource
    private BcyPetCategoryService bcyPetCategoryService;

    @Operation(summary = "获取宠物分类列表")
    @Parameter(name = "pageRequest", description = "宠物分类名称")
    @PostMapping("list")
    public R<PageUtils> list(@RequestBody @Validated PageRequest pageRequest) {
        return R.ok(bcyPetCategoryService.queryPage(pageRequest));
    }

    @Operation(summary = "根据id删除宠物分类信息")
    @GetMapping("delete/{id}")
    public R<String> delete(@PathVariable("id") Long id) {
        boolean flag = bcyPetCategoryService.deletePetCategory(id);
        return flag ? R.ok("删除成功") : R.fail("删除失败");
    }

    @Operation(summary = "根据宠物分类Id获取宠物分类信息")
    @GetMapping("info/{id}")
    public R<BcyPetCategoryResponse> info(@PathVariable("id") Long id) {
        return R.ok(bcyPetCategoryService.getInfoById(id));
    }

    @BcyNoRepeat(value = DateEnum.SECONDS)
    @Operation(summary = "新增宠物分类信息")
    @PostMapping("insert")
    @Parameter(name = "petCategoryForm", description = "宠物分类表单", required = true)
    public R<String> insert(@RequestBody @Validated BcyPetCategoryForm petCategoryForm) {
        boolean flag = bcyPetCategoryService.savePetCategory(petCategoryForm);
        return flag ? R.ok("新增成功") : R.fail("新增失败");
    }

    @Operation(summary = "修改宠物分类信息")
    @PostMapping("update")
    @Parameter(name = "petCategoryForm", description = "宠物分类表单", required = true)
    public R<String> update(@RequestBody @Validated(UpdateGroup.class) BcyPetCategoryForm petCategoryForm) {
        boolean flag = bcyPetCategoryService.updatePetCategory(petCategoryForm);
        return flag ? R.ok("更新成功") : R.fail("更新失败");
    }
}
