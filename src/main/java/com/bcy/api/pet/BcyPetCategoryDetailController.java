package com.bcy.api.pet;

import com.bcy.annotation.BcyNoRepeat;
import com.bcy.business.pet.BcyPetCategoryDetailService;
import com.bcy.domain.R;
import com.bcy.domain.form.IdsForm;
import com.bcy.domain.form.pet.BcyPetCategoryDetailForm;
import com.bcy.domain.group.opration.UpdateGroup;
import com.bcy.domain.request.PageRequest;
import com.bcy.domain.response.pet.BcyPetCategoryDetailResponse;
import com.bcy.enums.DateEnum;
import com.bcy.utils.PageUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 宠物分类详情接口服务
 *
 * <AUTHOR>
 * @since 2024/10/17-21:38
 */
@Tag(name = "宠物分类详情接口服务", description = "宠物分类详情接口服务")
@RestController
@RequestMapping("pet/category/detail/")
public class BcyPetCategoryDetailController {
    @Resource
    private BcyPetCategoryDetailService bcyPetCategoryDetailService;

    @Operation(summary = "获取宠物分类详情列表")
    @Parameter(name = "pageRequest", description = "宠物分类名称")
    @PostMapping("list")
    public R<PageUtils> list(@RequestBody @Validated PageRequest pageRequest) {
        return R.ok(bcyPetCategoryDetailService.queryPage(pageRequest));
    }

    @Operation(summary = "批量删除分类详情")
    @Parameter(name = "idsForm", description = "id数组表单", required = true)
    @PostMapping("delete")
    public R<String> delete(@RequestBody @Validated IdsForm idsForm) {
        boolean flag = bcyPetCategoryDetailService.delete(idsForm);
        return flag ? R.ok("删除成功") : R.fail("删除失败");
    }

    @Operation(summary = "根据宠物分类详情Id获取宠物分类详情信息")
    @GetMapping("info/{id}")
    public R<BcyPetCategoryDetailResponse> info(@PathVariable("id") Long id) {
        return R.ok(bcyPetCategoryDetailService.getInfoById(id));
    }

    @BcyNoRepeat(value = DateEnum.SECONDS)
    @Operation(summary = "新增宠物分类详情信息")
    @PostMapping("insert")
    @Parameter(name = "bcyPetCategoryDetailForm", description = "宠物分类详情表单", required = true)
    public R<String> insert(@RequestBody @Validated BcyPetCategoryDetailForm bcyPetCategoryDetailForm) {
        boolean flag = bcyPetCategoryDetailService.save(bcyPetCategoryDetailForm);
        return flag ? R.ok("新增成功") : R.fail("新增失败");
    }

    @Operation(summary = "修改宠物分类详情信息")
    @PostMapping("update")
    @Parameter(name = "bcyPetCategoryDetailForm", description = "宠物分类详情表单", required = true)
    public R<String> update(@RequestBody @Validated(UpdateGroup.class) BcyPetCategoryDetailForm bcyPetCategoryDetailForm) {
        boolean flag = bcyPetCategoryDetailService.update(bcyPetCategoryDetailForm);
        return flag ? R.ok("更新成功") : R.fail("更新失败");
    }
}
