package com.bcy.api.pet;

import com.bcy.annotation.BcyNoRepeat;
import com.bcy.business.pet.BcyPetContentService;
import com.bcy.domain.R;
import com.bcy.domain.form.IdsForm;
import com.bcy.domain.form.pet.BcyPetContentForm;
import com.bcy.domain.group.opration.UpdateGroup;
import com.bcy.domain.request.PageRequest;
import com.bcy.domain.response.pet.BcyPetContentResponse;
import com.bcy.enums.DateEnum;
import com.bcy.utils.PageUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 宠物科普接口服务
 *
 * <AUTHOR>
 * @since 2024/10/17-21:38
 */
@Tag(name = "宠物科普接口服务", description = "宠物科普接口服务")
@RestController
@RequestMapping("pet/content/")
public class BcyPetContentController {
    @Resource
    private BcyPetContentService bcyPetContentService;

    @Operation(summary = "分页获取宠物科普列表")
    @Parameter(name = "pageRequest", description = "分页表单", required = true)
    @PostMapping("list")
    public R<PageUtils> list(@RequestBody @Validated PageRequest pageRequest) {
        return R.ok(bcyPetContentService.queryPage(pageRequest));
    }

    @Operation(summary = "根据Id获取宠物科普信息")
    @GetMapping("info/{id}")
    public R<BcyPetContentResponse> info(@PathVariable("id") Long id) {
        return R.ok(bcyPetContentService.getInfoById(id));
    }

    @BcyNoRepeat(value = DateEnum.SECONDS)
    @Operation(summary = "新增宠物科普信息")
    @Parameter(name = "bcyPetContentForm", description = "宠物科普信息表单", required = true)
    @PostMapping("insert")
    public R<String> insert(@RequestBody @Validated BcyPetContentForm bcyPetContentForm) {
        boolean flag = bcyPetContentService.save(bcyPetContentForm);
        return flag ? R.ok("新增成功") : R.fail("新增失败");
    }

    @Operation(summary = "修改宠物科普信息")
    @Parameter(name = "bcyPetContentForm", description = "宠物科普信息表单", required = true)
    @PostMapping("update")
    public R<String> update(@RequestBody @Validated(UpdateGroup.class) BcyPetContentForm bcyPetContentForm) {
        boolean flag = bcyPetContentService.update(bcyPetContentForm);
        return flag ? R.ok("更新成功") : R.fail("更新失败");
    }

    @Operation(summary = "批量删除宠物科普")
    @Parameter(name = "idsForm", description = "id数组表单", required = true)
    @PostMapping("delete")
    public R<String> delete(@RequestBody @Validated IdsForm idsForm) {
        boolean flag = bcyPetContentService.delete(idsForm);
        return flag ? R.ok("删除成功") : R.fail("删除失败");
    }

    @BcyNoRepeat(value = DateEnum.SECONDS)
    @Operation(summary = "启用或禁用宠物科普")
    @GetMapping("updateStatus/{id}")
    public R<String> updateStatus(@PathVariable("id") Long id) {
        boolean flag = bcyPetContentService.updateStatus(id);
        return flag ? R.ok("更改成功") : R.fail("更改失败");
    }


}
