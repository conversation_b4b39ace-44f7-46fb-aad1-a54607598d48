package com.bcy.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * 积分兑换状态枚举
 *
 * <AUTHOR>
 * @since 2025-06-16
 */
@Getter
@AllArgsConstructor
public enum PointsGoodsOrderStatusEnum {
    WAIT_CONFIRM(0, "待确认"),
    WAIT_DELIVERY(1, "待发货"),
    DELIVERED(2, "已发货"),
    COMPLETED(3, "已完成"),
    CANCELED(4, "已取消");

    /**
     * 编码
     */
    private final int code;
    /**
     * 描述
     */
    private final String desc;

    /**
     * 根据编码查询描述
     *
     * @param code 编码
     * @return 描述
     */
    public static String getDescByCode(Integer code) {
        return Arrays.stream(values()).filter((e) -> e.getCode() == code).findAny().map(PointsGoodsOrderStatusEnum::getDesc).orElse(StringUtils.EMPTY);

    }
} 