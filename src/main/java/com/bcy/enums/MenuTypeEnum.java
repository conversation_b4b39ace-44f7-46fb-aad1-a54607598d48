package com.bcy.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * 菜单类型枚举
 *
 * <AUTHOR>
 * @since 2024/09/13
 */
@Getter
public enum MenuTypeEnum {
    MENU_TYPE_CATALOGUE("CATALOG", "目录"),
    MENU_TYPE_MENU("MENU", "菜单"),
    MENU_TYPE_BUTTON("BUTTON", "按钮"),
    MENU_TYPE_EXT_LINK("EXT_LINK", "外链");


    /**
     * 编码
     */
    private final String code;
    /**
     * 描述
     */
    private final String desc;

    MenuTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据编码查询描述
     *
     * @param code 编码
     * @return 描述
     */
    public static String getNameByCode(String code) {
        return Arrays.stream(values()).filter((e) -> e.getCode().equals(code)).findAny().map(MenuTypeEnum::getDesc).orElse(StringUtils.EMPTY);
    }

}
