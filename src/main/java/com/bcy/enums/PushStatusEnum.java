package com.bcy.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * 发送状态枚举
 *
 * <AUTHOR>
 * @since 2023-09-12
 */
@Getter
public enum PushStatusEnum {
    NO_PUSH(0, "未发送"),
    HAS_PUSH(1, "已发送");

    /**
     * 编码
     */
    private final Integer code;
    /**
     * 描述
     */
    private final String desc;

    PushStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据编码查询描述
     *
     * @param code 编码
     * @return 描述
     */
    public static String getDescByCode(Integer code) {
        return Arrays.stream(values()).filter((e) -> e.getCode().equals(code)).findAny().map(PushStatusEnum::getDesc).orElse(StringUtils.EMPTY);
    }
}
