package com.bcy.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * 下拉框类型枚举
 *
 * <AUTHOR>
 * @since 2023/11/16
 */
@Getter
public enum CmsDropDownTypeEnum {
    SITE_LIST(0, "SITE_LIST", "留资站点下拉列表"),
    PET_CATEGORY_LIST(1, "PET_CATEGORY_LIST", "宠物分类下拉列表（不含下级）"),
    PET_CATEGORY_CHILD_LIST(2, "PET_CATEGORY_CHILD_LIST", "宠物分类下拉列表（含下级）");

    /**
     * 编码
     */
    private final Integer code;
    /**
     * 名称
     */
    private final String name;
    /**
     * 描述
     */
    private final String desc;

    CmsDropDownTypeEnum(Integer code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }

    /**
     * 根据编码查询描述
     *
     * @param code 编码
     * @return description
     */
    public static String getDescriptionByCode(Integer code) {
        return Arrays.stream(values()).filter(e -> e.getCode().equals(code)).findAny().map(CmsDropDownTypeEnum::getDesc).orElse(StringUtils.EMPTY);
    }


    /**
     * 根据编码查询枚举英文：name
     *
     * @param code 编码
     * @return 枚举：name
     */
    public static String getNameByCode(Integer code) {
        return Arrays.stream(values()).filter(e -> e.getCode().equals(code)).findAny().map(CmsDropDownTypeEnum::name).orElse(StringUtils.EMPTY);
    }

}
